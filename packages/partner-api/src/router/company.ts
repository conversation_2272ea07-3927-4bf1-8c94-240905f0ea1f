import { companyDetailsSchema, signUpInputValidation } from "@repo/validators";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";

const companyApiRouter = createTRPCRouter({
  createCompany: protectedProcedure
    .input(companyDetailsSchema)
    .mutation(async ({ input, ctx }) => {
      const {
        name,
        email,
        phoneNumber,
        address,
        latitude,
        longitude,
        fax,
        about,
        website,
      } = input;

      try {
        const user = await ctx.db.user.findFirst({
          where: {
            id: ctx.user.id,
          },
        });

        if (!user) {
          throw new TRPCError({
            message: "Unauthorized",
            code: "UNAUTHORIZED",
          });
        }

        const company = await ctx.db.companyDetails.findFirst({
          where: {
            adminUserId: ctx.user.id,
          },
        });

        console.log("compnay details are", company, input);

        if (!company) {
          const company = await ctx.db.companyDetails.create({
            data: {
              companyName: name,
              email: email,
              phoneNumber: phoneNumber,
              companyWebsiteLink: website ?? null,
              fax: fax,
              about: about ?? "",
              companyLocation: address,
              companyLatitude: latitude,
              companyLongitude: longitude,
              adminUserId: user.id,
            },
          });
          await ctx.db.user.update({
            data: {
              companyId: company.id,
            },
            where: {
              id: ctx.user.id,
            },
          });

          return {
            messageTitle: "Successfully created company.",
          };
        }

        await ctx.db.companyDetails.update({
          where: {
            id: company.id,
          },
          data: {
            companyName: name,
            companyLatitude: latitude,
            companyLocation: address,
            companyLongitude: longitude,
            companyWebsiteLink: website ?? null,
            fax: fax,
            about: about,
            email: email,
            phoneNumber: phoneNumber,
          },
        });

        return {
          messageTitle: "Company details updated successfully.",
        };
      } catch (error) {
        console.log("erro is", error);
        throw new TRPCError({
          message: "Error in updating company details",
          code: "BAD_REQUEST",
        });
      }
    }),

  getCompany: protectedProcedure.query(async ({ ctx }) => {
    const company = await ctx.db.companyDetails.findFirst({
      where: {
        adminUserId: ctx.user.id,
      },
      include: {
        companyAgents: true,
      },
    });
    return company;
  }),

  addAgent: protectedProcedure
    .input(signUpInputValidation)
    .mutation(async ({ input, ctx }) => {
      const {
        name,
        phoneNumber,
        email,
        adharcardNumber,
        pancardNumber,
        reraNumber,
        gstNumber,
        cityId,
      } = input;
      const user = await ctx.db.user.findFirst({
        where: {
          id: ctx.user.id,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Unauthorized",
        });
      }

      if (!user.companyId) {
        return {
          error: "You need to create company to add agents to it.",
        };
      }

      // Check if agent already exists using any of the unique identifiers
      const existingAgent = await ctx.db.user.findFirst({
        where: {
          OR: [
            { email: email },
            { pancardNumber: pancardNumber },
            { adharcardNumber: adharcardNumber },
            { phoneNumber: phoneNumber },
          ],
        },
      });

      if (existingAgent) {
        return {
          error:
            "Agent already exists. Please go to the existing agents tab to add them to your company.",
        };
      }

      //create new agent if it does not exist already
      const newAgent = await ctx.db.user.create({
        data: {
          name: name,
          email: email,
          phoneNumber: phoneNumber,
          pancardNumber: pancardNumber?.toUpperCase(),
          adharcardNumber: adharcardNumber,
          reraNumber: reraNumber,
          gstNumber: gstNumber,
          cityId: cityId,
        },
      });

      await ctx.db.companyDetails.update({
        data: {
          companyAgents: {
            connect: {
              id: newAgent.id,
            },
          },
        },
        where: {
          id: user.companyId,
        },
      });
      return {
        messageTitle: "Successfully added agent to the company.",
      };
    }),

  addExistingAgent: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;
      const user = await ctx.db.user.findFirst({
        where: {
          id: ctx.user?.id,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Unauthorized",
        });
      }
      if (!user.companyId) {
        return {
          error: "You need to create company to add agents to it.",
        };
      }

      await ctx.db.companyDetails.update({
        data: {
          companyAgents: {
            connect: {
              id: id,
            },
          },
        },
        where: {
          id: user.companyId,
        },
      });
      return {
        messageTitle: "Successfully added agent to the company.",
      };
    }),

  existingAgents: publicProcedure
    .input(z.object({ val: z.string() }))
    .query(async ({ input, ctx }) => {
      const { val } = input;

      const user = await ctx.db.user.findFirst({
        where: {
          id: ctx.user?.id,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Unauthorized",
        });
      }

      // Find agent that matches any of the provided identifiers
      const findAgents = await ctx.db.user.findMany({
        where: {
          OR: [
            {
              name: {
                contains: val,
                mode: "insensitive",
              },
            },
            {
              email: {
                contains: val,
                mode: "insensitive",
              },
            },
            {
              phoneNumber: {
                contains: val,
                mode: "insensitive",
              },
            },
          ],
          companyId: null,
        },
        select: {
          id: true,
          name: true,
          experience: true,
          filePublicUrl: true,
          cloudinaryProfileImageUrl: true,
          propertiesSold: true,
          rating: true,
          createdAt: true,
          verifiedAgent: true,
          companyDetails: {
            select: {
              id: true,
              companyName: true,
            },
          },
        },
      });

      return findAgents;
    }),

  getCompanyAgents: publicProcedure.query(async ({ ctx }) => {
    const agents = await ctx.db.companyDetails.findFirst({
      where: {
        adminUserId: ctx.user?.id,
      },
      include: {
        companyAgents: {
          where: {
            id: {
              not: ctx.user?.id,
            },
          },
          include: {
            // id: true,
            // name: true,
            // email: true,
            // propertiesSold: true,
            // createdAt: true,
            // verifiedAgent: true,
            // experience: true,
            // filePublicUrl: true,
            // rating: true,
            company: {
              select: {
                id: true,
                companyName: true,
              },
            },
          },
        },
      },
    });

    console.log("agents areeeee", agents);
    return agents;
  }),

  removeAgentFromCompany: publicProcedure
    .input(z.object({ agentId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      console.log("inside the mutation for removing agnet");
      const { agentId } = input;

      const removeAgent = await ctx.db.user.update({
        where: {
          id: agentId,
        },
        data: {
          companyId: null,
        },
      });

      if (!removeAgent) {
        return {
          error: "Error in removing agent from company",
        };
      }
      return {
        messageTitle: "Successfully removed agent from the company.",
      };
    }),
});

export default companyApiRouter;
