import * as console from "node:console";
import { NotificationEnum, ONBOARDING_STEP_ENUM, Prisma } from "@repo/database";
import { sendEmail } from "@repo/mail";
import { retryOTP, sendSms, verifyOTP } from "@repo/msg91";
import { db } from "@repo/partner-auth/src/db";
import {
  bioFormSchema,
  GiveUsFeedbackSchema,
  signUpInputValidation,
} from "@repo/validators";
import { TRPCError } from "@trpc/server";
import axios from "axios";
import { v2 as cloudinary } from "cloudinary";
import { SignJWT } from "jose";
import { z } from "zod";

import { env } from "../env";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";
import { customOtp } from "../utils";
import { createSuccessResponse } from "../utils/response";

type TGoogleRecaptchaResponse = {
  success: boolean; // whether this request was a valid reCAPTCHA token for your site
  score: number; // the score for this request (0.0 - 1.0)
  action: string; // the action name for this request (important to verify)
  challenge_ts: Date; // timestamp of the challenge load (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)
  hostname: string; // the hostname of the site where the reCAPTCHA was solved
};

const userRouter = createTRPCRouter({
  signup: publicProcedure
    .input(signUpInputValidation)
    .mutation(async ({ ctx, input }) => {
      const {
        name,
        phoneNumber,
        email,
        pancardNumber,
        adharcardNumber,
        reraNumber,
        gstNumber,
        cityId,
        referredBy,
      } = input;

      // before checking anything first of all we need to check that weither the phone number is already registered as customer or not.
      const isCustomer = await ctx.db.customer.findUnique({
        where: {
          phoneNumber: phoneNumber,
        },
      });

      if (isCustomer) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "This phone number is already registered as customer, You cannot register as agent using this phone number.",
        });
      }

      const whereCondition: Prisma.UserWhereInput = {
        OR: [
          { email: email ?? undefined },
          { phoneNumber: phoneNumber ?? undefined },
        ],
      };

      if (pancardNumber) {
        whereCondition.OR?.push({ pancardNumber: pancardNumber });
      }

      if (adharcardNumber) {
        whereCondition.OR?.push({ adharcardNumber: adharcardNumber });
      }

      if (reraNumber) {
        whereCondition.OR?.push({ reraNumber: reraNumber });
      }
      if (gstNumber) {
        whereCondition.OR?.push({ gstNumber: gstNumber });
      }

      // check for user already exists or not
      const isUser = await ctx.db.user.findFirst({
        where: whereCondition,
        include: {
          operationArea: {
            select: {
              id: true,
              name: true,
              areaAddressComponents: true,
              areaGooglePlaceId: true,
              areaLat: true,
              areaLng: true,
            },
          },
        },
      });

      if (isUser?.phoneNumber === phoneNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Phone Number already exists !",
        });
      }

      if (isUser?.email === email) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already exists !",
        });
      }

      if (pancardNumber && isUser?.pancardNumber === pancardNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Pancard already exists !",
        });
      }

      // add extra adharcard check. why => let say its a new user then isUser will be null and the isUser?.adharcardNumber will be an undefined and from input we are receiving undefined for adharcardNumber field then undefined === undefined will produces true and it prevents the new user signup with undefined adharcardNumber.
      if (adharcardNumber && isUser?.adharcardNumber === adharcardNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Adhar Card already exists !",
        });
      }

      if (reraNumber && isUser?.reraNumber === reraNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "RERA Number already exists !",
        });
      }

      if (gstNumber && isUser?.gstNumber === gstNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "GST Number already exists !",
        });
      }

      if (isUser) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "User already exists !",
        });
      }

      //find whether the user with the referredBy code exists or not
      let referredUserId = null;
      if (referredBy) {
        const referredUser = await ctx.db.user.findFirst({
          where: {
            inviteCode: referredBy,
          },
        });

        if (!referredUser) {
          throw new TRPCError({
            message: "Referral code doesn't exist.",
            code: "BAD_REQUEST",
          });
        }

        referredUserId = referredUser.id;

        await ctx.db.$transaction(async (prisma) => {
          //added the INR 100 to the user whose invite code is used
          await prisma.user.update({
            where: {
              id: referredUser.id,
            },
            data: {
              walletBalanceInCents: {
                increment: 10000,
              },
            },
          });
          console.log("updated wallet in cents");

          //add the entry in walletTransaction Table
          await prisma.walletTransaction.create({
            data: {
              message: `Referral bonus of 100 points credited to your wallet from ${name}`,
              amountInCents: 10000,
              userId: referredUser.id,
            },
          });
          console.log("created wallet transaction");

          //add the notification for the amount credited in wallet
          await prisma.notification.create({
            data: {
              receiverId: referredUser.id,
              description: `${name} used your referral code for signing up! You have received 100 points in your wallet.`,
              title: "Referral Bonus Received",
              metaData: {},
              type: NotificationEnum.WALLET,
            },
          });
        });
      }

      const otp = customOtp();

      // Sending OTP via Message91
      const sendOtp = await sendSms({ receiversPhoneNumber: phoneNumber, otp });

      if (!sendOtp) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send OTP !",
        });
      }

      const createdUser = await ctx.db.user.create({
        data: {
          name: name,
          phoneNumber: phoneNumber,
          email: email,
          pancardNumber: pancardNumber?.toUpperCase(),
          adharcardNumber: adharcardNumber,
          reraNumber: reraNumber,
          gstNumber: gstNumber,
          cityId: cityId,
          referredBy: referredBy,
          referredByUserId: referredUserId,
        },
        select: { phoneNumber: true },
      });

      const response = createSuccessResponse({
        success: true,
        statusCode: 200,
        messageTitle: "Success !",
        messageDescription: "Registration successfull.",
        data: createdUser,
      });

      return response;
    }),
  verifyOtp: publicProcedure
    .input(z.object({ phoneNumber: z.string(), otp: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { phoneNumber, otp } = input;

      const user = await db.user.findFirst({
        where: { phoneNumber: phoneNumber },
      });

      if (user === null) {
        return null;
      }

      const isValid = await verifyOTP(otp, phoneNumber);

      if (!isValid) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      const jwtToken = await new SignJWT({
        id: user.id,
        name: user.name,
        phoneNumber: user.phoneNumber,
        email: user.email,
      })
        .setProtectedHeader({ alg: "HS256" })
        .setExpirationTime("1y")
        .sign(new TextEncoder().encode(env.NEXTAUTH_SECRET));
      // const jwtToken = jwt.sign(
      //   {
      //     id: user.id,
      //   },
      //   env.NEXTAUTH_SECRET!,
      //   { expiresIn: "1y" },
      // );

      return { jwtToken };
    }),
  profile: protectedProcedure.query(async ({ ctx }) => {
    const { id } = ctx.user;

    if (!id) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
    }

    const user = await ctx.db.user.findFirst({
      where: { id: id },
      select: {
        id: true,
        name: true,
        phoneNumber: true,
        email: true,
        filePublicUrl: true,
        onboardingStatus: true,
        adharcardNumber: true,
        pancardNumber: true,
        reraNumber: true,
        gstNumber: true,
        cityId: true,
        referredBy: true,
        experience: true,
        bio: true,
        userLocation: true,
        inviteCode: true,
        longitude: true,
        latitude: true,
        createdAt: true,
        companyId: true,
        rating: true,
        reviews: true,
        bgFilePublicUrl: true,
        propertiesSold: true,
        cloudinaryProfileImagePublicId: true,
        cloudinaryProfileImageUrl: true,
        cloudinaryBgImagePublicId: true,
        cloudinaryBgImageUrl: true,

        company: {
          select: {
            id: true,
            companyName: true,
            filePublicUrl: true,
            about: true,
            companyWebsiteLink: true,
            companyLocation: true,
            email: true,
            phoneNumber: true,
            fax: true,
            cloudinaryCompanyLogoUrl: true,
          },
        },
        customerRatingsToAgents: true,
        coustomerConnections: {
          where: {
            state: "ACCEPTED",
            deletedAt: null,
          },
        },
        sentConnectionRequests: {
          where: {
            status: "ACCEPTED",
            deletedAt: null,
          },
        },
        receivedConnectionRequests: {
          where: {
            status: "ACCEPTED",
            deletedAt: null,
          },
        },
        operationArea: {
          select: {
            id: true,
            name: true,
            areaAddressComponents: true,
            areaGooglePlaceId: true,
            areaLat: true,
            areaLng: true,
          },
        },
        city: {
          select: {
            id: true,
            name: true,
          },
        },
        OtpSentAt: true,
      },
    });

    if (user === null) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
    }

    return {
      ...user,
      minAppVersion: env.MIN_APP_VERSION,
    };
  }),

  getProfileWithoutSensitiveData: protectedProcedure.query(async ({ ctx }) => {
    const { id } = ctx.user;

    if (!id) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
    }

    const user = await ctx.db.user.findFirst({
      where: { id: id },
      select: {
        id: true,
        name: true,
        filePublicUrl: true,
        cityId: true,
        referredBy: true,
        experience: true,
        bio: true,
        // userLocation: true,
        inviteCode: true,
        company: {
          select: { id: true, companyName: true, filePublicUrl: true },
        },
      },
    });

    return user;
  }),

  // profileUpdate: protectedProcedure
  //   .input(signUpInputValidation)
  //   .mutation(async ({ ctx, input }) => {
  //     try {
  //       const {
  //         name,
  //         email,
  //         phoneNumber,
  //         reraNumber,
  //         gstNumber,
  //         experience,
  //         bio,
  //         longitude,
  //         latitude,
  //         userLocation,
  //         operationArea,
  //       } = input;

  //           return { jwtToken };
  //       }),
  // profile: protectedProcedure.query(async ({ ctx }) => {
  //   const { id } = ctx.user;

  //   if (!id) {
  //     throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
  //   }

  //   const user = await ctx.db.user.findFirst({
  //     where: { id: id },
  //     include: {
  //       company: true,
  //       operationArea: true,
  //       coustomerConnections: true,
  //     },
  //   });

  //   return user;
  // }),

  profileUpdate: protectedProcedure
    .input(signUpInputValidation)
    .mutation(async ({ ctx, input }) => {
      try {
        const {
          name,
          email,
          phoneNumber,
          reraNumber,
          gstNumber,
          experience,
          // bio,
          longitude,
          latitude,
          userLocation,
          operationArea,
        } = input;

        const { id } = ctx.user;

        if (!phoneNumber) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Unauthorized",
          });
        }

        const whereCondition: Prisma.UserWhereInput = {
          OR: [{ email: email ?? undefined }],
          NOT: { phoneNumber: phoneNumber },
        };

        if (reraNumber) {
          whereCondition.OR?.push({ reraNumber: reraNumber });
        }
        if (gstNumber) {
          whereCondition.OR?.push({ gstNumber: gstNumber });
        }

        // check for user already exists or not
        const isUser = await ctx.db.user.findFirst({ where: whereCondition });

        if (isUser?.email === email) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Email already exists !",
          });
        }

        if (reraNumber && isUser?.reraNumber === reraNumber) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "RERA Number already exists !",
          });
        }

        if (gstNumber && isUser?.gstNumber === gstNumber) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "GST Number already exists !",
          });
        }

        await ctx.db.user.update({
          data: {
            name: name,
            email: email,
            reraNumber: reraNumber,
            gstNumber: gstNumber,
            experience: experience?.toString(),
            // bio: bio,
            userLocation: userLocation,
            longitude: longitude,
            latitude: latitude,
            // operationArea:{
            //  connect:operationArea?.map((item)=>(
            //   {
            //     longitude:item.longitude,
            //     latitude:item.latitude,
            //     googlePlaceId:item.googlePlaceId,
            //     location:item.location
            //   }
            //  ))
            // }
          },
          where: { email: email },
        });
        const partner = await ctx.db.user.findFirst({
          where: { id, active: true },
          select: {
            id: true,
            name: true,
            filePublicUrl: true,
            userLocation: true,
          },
        });
        try {
          await axios.put(
            `${env.MEILI_SEARCH_URL}/indexes/${env.PARTNER_INDEX}/documents`,
            partner,
            {
              headers: {
                "Content-Type": "application/json",
                "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
                Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
              },
            },
          );
        } catch (meilisearchError) {
          console.error("Meilisearch update failed:", meilisearchError);
        }

        const response = createSuccessResponse({
          success: true,
          statusCode: 200,
          messageTitle: "Success !",
          messageDescription: "User updated successfully.",
        });
        return response;
      } catch (error) {
        // Handle specific known errors
        if (error instanceof TRPCError) {
          throw error; // Re-throw TRPC errors as they are already properly formatted
        }
        // Handle Prisma errors
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2025") {
            // Record not found
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "User not found",
            });
          }
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Database operation failed",
          });
        }
        console.error("Profile update error:", error);

        // Generic error handling for unexpected errors
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update profile. Please try again later.",
        });
      }
    }),

  bioUpdate: protectedProcedure
    .input(bioFormSchema)
    .mutation(async ({ ctx, input }) => {
      const { bio } = input;

      try {
        await ctx.db.user.update({
          where: {
            id: ctx.user.id,
          },
          data: {
            bio: bio,
          },
        });
        const response = createSuccessResponse({
          success: true,
          statusCode: 200,
          messageTitle: "Success !",
          messageDescription: "Bio updated successfully.",
        });
        return response;
      } catch (err) {
        throw new TRPCError({
          message: "Error in updatng the bio.",
          code: "BAD_REQUEST",
        });
      }
    }),

  getPosts: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.post.findMany({
      where: {
        userId: ctx.user.id,
        deletedAt: null,
      },
      include: {
        user: {
          select: {
            id: true,
            filePublicUrl: true,
            cloudinaryProfileImageUrl: true,
            name: true,
            company: {
              select: {
                companyName: true,
              },
            },
          },
        },
        media: {
          select: {
            cloudinaryUrl: true,
            cloudinaryId: true,
            filePublicUrl: true,
            mediaType: true,
          },
        },
        comments: {
          select: {
            comment: true,
            isPinned: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                name: true,
                filePublicUrl: true,
                companyDetails: {
                  select: {
                    companyName: true,
                  },
                },
              },
            },
            customer: {
              select: {
                id: true,
                name: true,
                profileImagePublicUrl: true,
              },
            },
            customerId: true,
            userId: true,
          },
        },
        likes: {
          select: {
            id: true,
            postId: true,
          },
          where: {
            userId: ctx.user.id,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  signin: publicProcedure
    .input(z.object({ phoneNumber: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // check if user exists with provided phoneNumber or not
      const user = await ctx.db.user.findFirst({
        where: { phoneNumber: input.phoneNumber },
      });

      if (user === null) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No user found with provided phone number !",
        });
      }
      const otp = customOtp();
      console.log("otp is", otp);
      // Sending OTP via Message91
      const sendOtp = await sendSms({
        receiversPhoneNumber: user.phoneNumber,
        otp: otp,
      });

      if (!sendOtp) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send OTP !",
        });
      }

      const updatedUser = await ctx.db.user.update({
        where: { phoneNumber: input.phoneNumber },
        data: { OtpSentAt: new Date() },
        select: { phoneNumber: true },
      });

      const response = createSuccessResponse({
        success: false,
        statusCode: 200,
        messageTitle: "Success !",
        messageDescription: `OTP Sent to ${user.phoneNumber}.`,
        data: updatedUser,
      });

      console.log("response for otp is", response);

      return response;
    }),

  verifyCaptcha: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      const { token } = input;
      const RECAPTHCA_SECRET_KEY = "6LfHWJkqAAAAAOITtu4LU-UPryvr2BfNAvZ0WpEe";

      try {
        const formData = new URLSearchParams({
          secret: RECAPTHCA_SECRET_KEY,
          response: token,
        });
        const { data }: { data: TGoogleRecaptchaResponse } = await axios.post(
          "https://www.google.com/recaptcha/api/siteverify",
          formData,
          { headers: { "Content-Type": "application/x-www-form-urlencoded" } },
        );

        if (data.success && data.score > 0.6) {
          return { success: true, score: data.score };
        }

        return { success: false, score: data.score };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to verify captcha",
        });
      }
    }),

  resendOtp: publicProcedure
    .input(z.object({ phoneNumber: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { phoneNumber } = input;

      // check weither user exists with the provided phonenumber or not
      const user = await ctx.db.user.findFirst({
        where: { phoneNumber: phoneNumber },
      });

      if (!user) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No user found with the provided phone number.",
        });
      }

      // check valid otp or not before generating new OTP
      const oneMinuteAgo = new Date(Date.now() - 30 * 1000); // current time - 1 minute

      if (user.OtpSentAt && user.OtpSentAt >= oneMinuteAgo) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "Please wait at least 30 seconds before requesting a new OTP !",
        });
      }
      // const otp = customOtp();
      // Sending OTP via Message91
      const sendOtp = await retryOTP(phoneNumber);

      //   console.log("otp-sent: ", sendOtp);

      if (!sendOtp) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send OTP !",
        });
      }

      // const updatedUser = await ctx.db.user.update({
      //   where: {
      //     phoneNumber: phoneNumber,
      //   },
      //   data: {
      //     OtpSentAt: new Date(),
      //   },
      //   select: {
      //     phoneNumber: true,
      //   },
      // });

      const response = createSuccessResponse({
        success: true,
        statusCode: 200,
        messageTitle: "Success !",
        messageDescription: `OTP Resent to phone number ${phoneNumber}.`,
      });

      return response;
    }),

  createUserMedia: protectedProcedure
    .input(
      z.object({
        fileKey: z.string(),
        filePublicUrl: z.string().optional().nullable(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const user = await ctx.db.user.findFirst({ where: { id: ctx.user.id } });
      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      const { fileKey, filePublicUrl } = input;
      if (!fileKey) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      await ctx.db.user.update({
        where: { id: user.id },
        data: { fileKey: fileKey, filePublicUrl: filePublicUrl },
      });
    }),

  updateUserProfileImage: protectedProcedure
    .input(z.object({ cloudinaryId: z.string(), cloudinaryUrl: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { cloudinaryId, cloudinaryUrl } = input;

      try {
        // check weither the user previously uploaded the image is cloudinary upload or not.
        // 1. if yes = then delete the previous image from cloudinary
        // 2. if no = then do nothing

        const user = await ctx.db.user.findFirst({
          where: { id: ctx.user.id },
          select: { cloudinaryProfileImagePublicId: true },
        });

        if (user?.cloudinaryProfileImagePublicId) {
          cloudinary.config({
            api_key: env.CLOUDINARY_API_KEY,
            api_secret: env.CLOUDINARY_API_SECRET,
            cloud_name: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
          });

          await cloudinary.uploader.destroy(
            user.cloudinaryProfileImagePublicId,
            { invalidate: true },
          );
        }

        await ctx.db.user.update({
          where: { id: ctx.user.id },
          data: {
            cloudinaryProfileImagePublicId: cloudinaryId,
            cloudinaryProfileImageUrl: cloudinaryUrl,
            // why i am making this null because slowly we are migrating the images from s3 to cloudinary and we need to make s3 uploades null in db so that we can determine which images are uploaded to cloudinary and which are uploaded to s3.
            fileKey: null,
            filePublicUrl: null,
          },
        });

        return {
          message: "Profile image updated successfully",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        console.error("Error in updating user profile image:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user profile image",
        });
      }
    }),

  updateUserBgImage: protectedProcedure
    .input(
      z.object({
        purpose: z.enum(["delete", "update"]),
        cloudinaryId: z.string().optional(),
        cloudinaryUrl: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      cloudinary.config({
        api_key: env.CLOUDINARY_API_KEY,
        api_secret: env.CLOUDINARY_API_SECRET,
        cloud_name: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      });

      const { purpose, cloudinaryId, cloudinaryUrl } = input;

      try {
        const user = await ctx.db.user.findFirst({
          where: { id: ctx.user.id },
          select: { cloudinaryBgImagePublicId: true },
        });

        switch (purpose) {
          case "delete": {
            if (user?.cloudinaryBgImagePublicId) {
              await cloudinary.uploader.destroy(
                user.cloudinaryBgImagePublicId,
                {
                  invalidate: true,
                },
              );
            }

            await ctx.db.user.update({
              where: { id: ctx.user.id },
              data: {
                cloudinaryBgImagePublicId: null,
                cloudinaryBgImageUrl: null,
                // why i am making this null because slowly we are migrating the images from s3 to cloudinary and we need to make s3 uploades null in db so that we can determine which images are uploaded to cloudinary and which are uploaded to s3.
                bgFileKey: null,
                bgFilePublicUrl: null,
              },
            });

            return {
              message: "Background image deleted successfully",
            };
          }

          case "update": {
            if (!cloudinaryId || !cloudinaryUrl) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Cloudinary ID and URL are required",
              });
            }

            if (user?.cloudinaryBgImagePublicId) {
              await cloudinary.uploader.destroy(
                user.cloudinaryBgImagePublicId,
                {
                  invalidate: true,
                },
              );
            }

            await ctx.db.user.update({
              where: { id: ctx.user.id },
              data: {
                cloudinaryBgImagePublicId: cloudinaryId,
                cloudinaryBgImageUrl: cloudinaryUrl,
                // why i am making this null because slowly we are migrating the images from s3 to cloudinary and we need to make s3 uploades null in db so that we can determine which images are uploaded to cloudinary and which are uploaded to s3.
                bgFileKey: null,
                bgFilePublicUrl: null,
              },
            });

            return {
              message: "Background image updated successfully",
            };
          }
        }
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        console.error("Error in updating user background image:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user background image",
        });
      }
    }),

  createUserBgMedia: protectedProcedure
    .input(
      z.object({
        fileKey: z.string(),
        filePublicUrl: z.string().optional().nullable(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { fileKey, filePublicUrl } = input;
      if (!fileKey) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }
      await ctx.db.user.update({
        where: { id: ctx.user.id },
        data: { bgFileKey: fileKey, bgFilePublicUrl: filePublicUrl },
      });
    }),

  deleteBgUserMedia: protectedProcedure.mutation(async ({ ctx }) => {
    await ctx.db.user.update({
      where: { id: ctx.user.id },
      data: { bgFileKey: null, bgFilePublicUrl: null },
    });
  }),

  deleteUserMedia: protectedProcedure.mutation(async ({ ctx }) => {
    const user = await ctx.db.user.findFirst({ where: { id: ctx.user.id } });
    if (!user) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
    }

    await ctx.db.user.update({
      where: { id: user.id },
      data: { fileKey: null, filePublicUrl: null },
    });
  }),

  giveUsFeedback: protectedProcedure
    .input(GiveUsFeedbackSchema)
    .mutation(async ({ input, ctx }) => {
      const user = await ctx.db.user.findFirst({ where: { id: ctx.user.id } });
      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      const {
        agentCompanyName,
        agentName,
        doYouHaveAnythingElseToAddAboutTheAgent,
        rating,
        whatDoYouLikeAboutTheAgent,
      } = input;
      await ctx.db.feedback.create({
        data: {
          agentName: agentName,
          companyName: agentCompanyName,
          doYouHaveAnythingElseToAddAboutTheAgent:
            doYouHaveAnythingElseToAddAboutTheAgent,
          rating: rating,
          whatDoYouLikeAboutTheAgent: whatDoYouLikeAboutTheAgent,
        },
      });
    }),

  createCompanyMedia: protectedProcedure
    .input(
      z.object({
        fileKey: z.string(),
        filePublicUrl: z.string().optional().nullable(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const company = await ctx.db.companyDetails.findFirst({
        where: { adminUserId: ctx.user.id },
      });

      if (!company) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      const { fileKey, filePublicUrl } = input;
      if (!fileKey) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }
      const updated = await ctx.db.companyDetails.update({
        where: { id: company.id },
        data: { fileKey: fileKey, filePublicUrl: filePublicUrl },
      });
      console.log("updated is", updated);
    }),

  updateCompanyLogo: protectedProcedure
    .input(
      z.object({
        purpose: z.enum(["delete", "update"]),
        cloudinaryId: z.string().optional(),
        cloudinaryUrl: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      cloudinary.config({
        api_key: env.CLOUDINARY_API_KEY,
        api_secret: env.CLOUDINARY_API_SECRET,
        cloud_name: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      });

      // why i am setting s3 related because null slowly we are migrating the images from s3 to cloudinary and we need to make s3 uploades null in db so that we can determine which images are uploaded to cloudinary and which are uploaded to s3.

      const { purpose, cloudinaryId, cloudinaryUrl } = input;

      try {
        switch (purpose) {
          case "delete": {
            const company = await ctx.db.companyDetails.findFirst({
              where: { adminUserId: ctx.user.id },
            });

            if (!company) {
              throw new TRPCError({
                code: "NOT_FOUND",
                message: "No company found with the provided ID",
              });
            }

            // deleting the image from cloudinary too.
            if (company.cloudinaryCompanyLogoPublicId) {
              await cloudinary.uploader.destroy(
                company.cloudinaryCompanyLogoPublicId,
                { invalidate: true },
              );
            }

            await ctx.db.companyDetails.update({
              where: { id: company.id },
              data: {
                fileKey: null,
                filePublicUrl: null,
                cloudinaryCompanyLogoPublicId: null,
                cloudinaryCompanyLogoUrl: null,
              },
            });

            return {
              message: "Company logo deleted successfully",
            };
          }

          case "update": {
            if (!cloudinaryId || !cloudinaryUrl) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Cloudinary ID and URL are required for update",
              });
            }

            const company = await ctx.db.companyDetails.findFirst({
              where: { adminUserId: ctx.user.id },
              select: { cloudinaryCompanyLogoPublicId: true },
            });

            if (!company) {
              throw new TRPCError({
                code: "NOT_FOUND",
                message: "You need to create a company first",
              });
            }

            // delete the previous image from cloudinary
            if (company.cloudinaryCompanyLogoPublicId) {
              await cloudinary.uploader.destroy(
                company.cloudinaryCompanyLogoPublicId,
                { invalidate: true },
              );
            }

            await ctx.db.companyDetails.update({
              where: { adminUserId: ctx.user.id },
              data: {
                cloudinaryCompanyLogoPublicId: cloudinaryId,
                cloudinaryCompanyLogoUrl: cloudinaryUrl,
                fileKey: null,
                filePublicUrl: null,
              },
            });

            return {
              message: "Company logo updated successfully",
            };
          }

          default:
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Invalid purpose provided",
            });
        }
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        console.error("Error in updating user profile image:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user profile image",
        });
      }
    }),

  deleteCompanyMedia: protectedProcedure.mutation(async ({ ctx }) => {
    const company = await ctx.db.companyDetails.findFirst({
      where: { adminUserId: ctx.user.id },
    });
    if (!company) {
      throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
    }

    await ctx.db.companyDetails.update({
      where: { id: company.id },
      data: { fileKey: null, filePublicUrl: null },
    });
  }),

  getTrendingAgents: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.user.findMany({
      orderBy: { rating: { sort: "desc" } },
      where: { active: true },
      take: 3,
    });
  }),

  getCities: publicProcedure.query(async ({ ctx }) => {
    try {
      const cities = await ctx.db.city.findMany();

      return cities;
    } catch (err) {
      console.log(err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cities",
      });
    }
  }),

  onboardingStep1: protectedProcedure
    .input(
      z.object({
        onboardingPreference: z.enum(["BUY", "RENT"], {
          required_error: "You need to select one choice.",
        }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { onboardingPreference } = input;

      const user = await ctx.db.user.findFirst({
        select: { id: true },
        where: { id: ctx.user.id },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      await ctx.db.user.update({
        where: { id: user.id },
        data: {
          onboardingPreference: onboardingPreference,
          onboardingStep: ONBOARDING_STEP_ENUM.STEP_2,
        },
      });

      const response = createSuccessResponse({
        success: true,
        statusCode: 200,
        messageTitle: "Success !",
        messageDescription: "Onboarding Preference updated successfully.",
      });
      return response;
    }),

  onboardingStep2: protectedProcedure
    .input(
      z.object({
        location: z.string().min(2),
        latitude: z.string(),
        longitude: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findFirst({
        select: { id: true },
        where: { id: ctx.user.id },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "Unauthorized" });
      }

      const { location, latitude, longitude } = input;
      try {
        await ctx.db.user.update({
          where: { id: user.id },
          data: {
            userLocation: location,
            latitude: latitude,
            longitude: longitude,
            // onboardingStatus: true,
            onboardingStep: ONBOARDING_STEP_ENUM.STEP_3,
          },
        });

        const response = createSuccessResponse({
          success: true,
          statusCode: 200,
          messageTitle: "Success !",
          messageDescription: "Updated successfully.",
        });
        return response;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update onboarding",
        });
      }
    }),

  updateOnboardingStep: publicProcedure
    .input(z.nativeEnum(ONBOARDING_STEP_ENUM))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.user.update({
        data: {
          onboardingStep: input,
        },
        where: {
          id: ctx.user?.id,
        },
      });
      return;
    }),
  getOnboardingStatus: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const onboardingStatus = await ctx.db.user.findUnique({
        where: { id: ctx.user.id },
        select: { onboardingStatus: true },
      });
      return onboardingStatus;
    } catch (error) {
      console.log(error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch onboarding status",
      });
    }
  }),

  updateOnBoardingStatus: protectedProcedure
    .input(z.object({}))
    .mutation(async ({ ctx }) => {
      try {
        await ctx.db.user.update({
          where: { id: ctx.user.id },
          data: { onboardingStatus: true },
        });

        return;
      } catch (err) {
        console.log(err);
        throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
      }
    }),

  nearbyAgents: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.user.findMany({
      include: {
        companyDetails: { select: { id: true, companyName: true } },
        sentConnectionRequests: {
          select: { id: true },
          where: { senderId: ctx.user.id },
        },
        receivedConnectionRequests: {
          select: { id: true },
          where: { receiverId: ctx.user.id },
        },
      },
      where: { id: { not: ctx.user.id }, active: true },
    });
  }),

  getAllAgents: protectedProcedure.query(async ({ ctx }) => {
    return ctx.db.user.findMany({
      include: {
        posts: {
          select: {
            id: true,
            content: true,
            totalComments: true,
            totalLikes: true,
            createdAt: true,
            updatedAt: true,
            media: { select: { filePublicUrl: true, mediaType: true } },
            user: {
              select: {
                id: true,
                filePublicUrl: true,
                name: true,
                company: { select: { companyName: true } },
              },
            },
            comments: {
              select: {
                comment: true,
                isPinned: true,
                createdAt: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    filePublicUrl: true,
                    companyDetails: { select: { companyName: true } },
                  },
                },
              },
            },
            likes: { select: { id: true, postId: true }, take: 1 },
          },
        },
        companyDetails: true,
      },
      orderBy: { createdAt: "desc" },
    });
  }),

  requestOtpForProfileUpdate: protectedProcedure.mutation(async ({ ctx }) => {
    const user = ctx.user;

    const userPhoneNumber = user.phoneNumber;
    const userDb = await ctx.db.user.findUnique({
      where: {
        id: user.id,
      },
    });

    if (userDb === null) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No user found with provided phone number !",
      });
    }
    // check valid otp or not before generating new OTP
    const fiveMinuteAgo = new Date(Date.now() - 60 * 5 * 1000); // current time - 5 minutes

    if (userDb.OtpSentAt && userDb.OtpSentAt >= fiveMinuteAgo) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Please wait at least 5 minutes before requesting a new OTP !",
      });
    }

    const otp = customOtp();

    try {
      const smsResp = await sendSms({
        receiversPhoneNumber: userPhoneNumber,
        otp,
      });
      const emailBodySendGrid = {
        from: env.FROM_EMAIL,
        subject: "OTP for updating profile",
        to: [userDb.email],
        html: `<p>Hi,<strong> ${userDb.name}</strong><br/></p>
                   <span>usUse this OTP to verify - </span><br/>
                   ${otp}`,
      };
      console.log("email bodyyyyyyyy", emailBodySendGrid);
      await sendEmail(emailBodySendGrid, env.SENDGRID_API_KEY);

      if (smsResp) {
        await ctx.db.user.update({
          where: {
            phoneNumber: ctx.user.phoneNumber,
          },
          data: {
            OtpSentAt: new Date(),
          },
          select: {
            phoneNumber: true,
          },
        });
        return {
          message: `Otp sent to phone number and email: ${userPhoneNumber} & ${user.email}`,
        };
      } else {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to sent otp",
        });
      }
    } catch (err) {
      console.error(err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to request otp",
      });
    }
  }),
  verifyOtpForProfileUpdate: protectedProcedure
    .input(
      signUpInputValidation
        .pick({
          phoneNumber: true,
          //   adharcardNumber: true,
          //   pancardNumber: true,
          email: true,
        })
        .extend({
          otp: z
            .string({ message: "Invalid otp" })
            .min(4, { message: "Minimum 4 digits required" })
            .max(4, { message: "Maximum 4 digits allowed" }),
        }),
    )
    .mutation(async ({ ctx, input }) => {
      const { otp, phoneNumber, email } = input;

      try {
        const resp = await verifyOTP(otp, ctx.user.phoneNumber);

        if (!resp) {
          throw new TRPCError({ code: "UNAUTHORIZED", message: "Invalid otp" });
        }

        await ctx.db.user.update({
          where: { id: ctx.user.id },
          data: { phoneNumber: phoneNumber, email: email },
        });

        return { message: "Details updated successfully" };
      } catch (err) {
        console.error(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to verify otp",
        });
      }
    }),

  updateInviteCode: protectedProcedure
    .input(
      z.object({
        inviteCode: z.string().transform((val) => val.toUpperCase().trim()),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { inviteCode } = input;

      try {
        const isInviteCodeExists = await ctx.db.user.findFirst({
          where: {
            inviteCode: {
              contains: inviteCode,
              mode: "insensitive",
            },
          },
        });

        if (isInviteCodeExists) {
          return {
            warning: true,
            message: "Invite code already exists.",
          };
        }

        await ctx.db.user.update({
          where: { id: ctx.user.id },
          data: { inviteCode: inviteCode },
        });

        return { message: "Invite code updated successfully" };
      } catch (err) {
        console.error(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to udpate invite code.",
        });
      }
    }),
});

export default userRouter;
