import z from "zod";

const phoneRegex = new RegExp(
  /^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/,
);

const pancardRegex = new RegExp(/([A-Z]){5}([0-9]){4}([A-Z]){1}$/);

const gstRegex = new RegExp(
  /\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}/,
);

const adharcardNumber = new RegExp(/^[1-9]\d{11}$/);

const signUpInputValidation = z.object({
  id:z.string().optional(),
  name: z.string().min(2, { message: "Name must be of 2 characters" }),
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: "Invalid phone number" }),
  email: z.string().email(),
  pancardNumber: z
    .union([
      z.string()
        .regex(pancardRegex, { message: "Please enter a valid PAN card number in the correct format (e.g., **********)." })
        .min(10, { message: "PAN card number must have exactly 10 characters." }),
      z.literal(""),
    ])
    .optional(),
  adharcardNumber: z
    .string()
    .regex(adharcardNumber, { message: "Invalid adharcard number" })
    .min(12, { message: "Adhar Card Number must be of 12 digits" }),
  reraNumber: z.string().optional(),
  gstNumber: z
    .string()
    // .regex(gstRegex, { message: "Invalid gst number" })
    .optional(),
});

export default signUpInputValidation;
