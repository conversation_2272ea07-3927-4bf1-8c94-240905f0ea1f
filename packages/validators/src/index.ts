import z from "zod";
import { EnquiryType, PropertyForEnum, FurnishingEnum, PropertyStateEnum, FacingEnum, PossessionStateEnum } from "@repo/database";

export * from "./customer"
// export * from './property-validators';

const phoneRegex = new RegExp(
  /^[1-9][0-9]{9}$/,
);

const pancardRegex = new RegExp(/^([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}$/, "i");

const gstRegex = new RegExp(
  /\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}/,
);

const adharcardNumber = new RegExp(/^[1-9]\d{11}$/);

export const signUpInputValidation = z.object({
    name: z.string().min(2, { message: "Please provide a valid name with at least 2 characters." }),
    phoneNumber: z
      .string()
      .regex(phoneRegex, { message: "Please enter 10 digit phone number." }),
    email: z.string().email({ message: "Please enter a valid email address." }),
    pancardNumber: z
      .union([
        z.string()
          .regex(pancardRegex, { message: "Please enter a valid PAN card number in the correct format (e.g., **********)." })
          .min(10, { message: "PAN card number must have exactly 10 characters." }),
        z.literal(""),
      ])
      .optional(),
    adharcardNumber: z
      .string()
      .regex(adharcardNumber, { message: "Please enter a valid Aadhar card number with exactly 12 digits." })
      .min(12, { message: "Aadhar card number must have exactly 12 digits." }).optional(),
    cityId: z.string({ message: "Please select a city." }).min(1, { message: "Please select a city." }),
    reraNumber: z.string().optional(),
    gstNumber: z
      .string()
      // .regex(gstRegex, { message: "Please enter a valid GST number." })
      .optional(),
    referredBy: z.string().optional(),
    bio: z.string().optional(),
    experience: z.string().optional(),
    userLocation: z.string().optional(),
    longitude: z.string().optional(),
    latitude: z.string().optional(),
    operationArea: z.string().optional(),
  });

  export const bioFormSchema=z.object({
    bio: z.string().optional(),
})
export const operationAreaSchema=z.object({
    operationArea: z.string().optional(),
})

export const languageSchema=z.object({
    language: z.string().optional(),
})

export const landingSignUpInputValidation = z.object({
  name: z.string().min(2, { message: "Name must be of 2 characters" }),
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: "Invalid phone number" }),
  email: z.string().email(),
  agentCode: z.string().optional().nullable(),
  enquiryType: z.nativeEnum(EnquiryType)
});

export const companyDetailsSchema=z.object({
  name: z.string({required_error:"Company name is required."}).min(5, { message: "Name must be of 5 characters" }),
  email: z.string({required_error:"Company email is required."}).email(),
  phoneNumber: z
  .string({message:"Phone no. is required."})
  .regex(phoneRegex, { message: "Invalid phone number" }),
 address: z.string({required_error:"Company location is required."}).min(2,{message:"Select location"}),
  latitude: z.string(),
longitude: z.string(),
website: z
  .string({ required_error: "Company website link is required." }).optional()
  .refine((val) => {
    if (!val) return true;

    // Check if it's already a valid URL with http/https
    if (val.match(/^https?:\/\//i)) {
      try {
        new URL(val);
        return true;
      } catch {
        return false;
      }
    }
    // If it's a plain string, check if it contains a dot
    return val.includes('.');
  }, "Enter valid website link.")
  .transform((val) => {
    if (!val) return undefined;

    // If doesn't start with http/https, prepend https://
    if (!val.match(/^https?:\/\//i)) {
      return `https://${val}`;
    }
    return val;
  }),
fax:z.string().length(11 , {message:"Fax number must be of 11 digits"}).optional(),
about:z.string().optional()
})

export const propertyCategorySchema = z.string();
export const propertyForSchema = z.nativeEnum(PropertyForEnum, { required_error: "Please select a value", invalid_type_error: "Please select a value" });

export const step1FormSchema = z.object({
  registeryFileKey: z.string().optional(),
  propertyTitle: z.string().min(5, { message: "Title should have at least 5 characters" }),
  propertyFor :  z.nativeEnum(PropertyForEnum, { required_error: "Please select a value", invalid_type_error: "Please select a value" }),
  propertyCategoryId: z.string(),
  propertyTypeId: z.string(),
  bedrooms: z.coerce.number().min(1).nonnegative().optional(),
  bathrooms: z.coerce.number().min(1).nonnegative().optional(),
  propertyPrice: z.coerce.number({ message: "Enter an amount" }).nonnegative(),
  securityDeposit: z.coerce.number().nonnegative().optional(),
  areaUnitId: z.string(),
  area: z.coerce.number({message:"Required"}),
  aboutProperty: z.string().min(1).optional(),


});

export const step2FormSchema = z.object({
  id: z.string().optional().nullable(),
  propertyAddress: z.string().min(1).optional(),
  propertyLatitude: z.number(),
  propertyLongitude: z.number(),
  propertyGooglePlaceId: z.string(),
  propertyAddressComponents: z.any(),
  propertyMarkersLatLng: z.record(z.any(), z.any(), {message: "Please add the property markers on the map."}).array(),
  propertyLocation: z.string().min(1).optional(),
  utilities: z.array(z.object({
    utility: z.string().min(1,{message:"Required"}),
    distanceInKm: z.coerce.number({ message: "Enter a distance in Km" }).gt(0),
  })),
})

export const step3FormSchema = z.object({
  id: z.string().optional().nullable(),
  societyOrLocalityName: z.string().min(1, {message: "Society or locality name must be of atleast 1 character."}).optional(),
  buildYear: z.coerce.number().min(1900).max(21000,{message:"Required"}).nonnegative().optional(),
  possessionState: z.nativeEnum(PossessionStateEnum, { message:"Required",required_error: "Please select a value", invalid_type_error: "Please select a value" }).optional(),
  amenities: z.array(z.object({
    id: z.string(),
    name: z.string(),
  })),
  facing: z.nativeEnum(FacingEnum, { required_error: "Please select a value", invalid_type_error: "Please select a value",message:"Required" }).optional(),
  furnishing: z.nativeEnum(FurnishingEnum, { required_error: "Please select a value", invalid_type_error: "Please select a value" ,message:"Required"}).optional(),
  totalFloors: z.coerce.number().min(1, {message:"Required"}).nonnegative().optional(),
  floorNumber: z.coerce.number().min(1, {message:"Required"}).nonnegative().optional(),
  carParking: z.coerce.number().min(1, {message:"Required"}).nonnegative().optional(),
  propertyState: z.nativeEnum(PropertyStateEnum, { required_error: "Please select a value", invalid_type_error: "Please select a value",message:"Required" }).optional(),
});

export const createMediaSectionSchema = z.object({
  title: z.string(),
  propertyId: z.string(),
});

export const createMediaSchema = z.object({
  fileKey: z.string(),
  filePublicUrl: z.string().optional().nullable(),
  mediaSectionId: z.string(),
});

export const GiveUsFeedbackSchema = z.object({
  agentName: z.string().min(1, {message: "Agent name must be of atleast 1 character."}),
  agentCompanyName: z.string().min(1, {message: "Agent company name must be of atleast 1 character."}),
  rating: z.number().min(1).max(5, {message: "Invalid rating."}),
  whatDoYouLikeAboutTheAgent: z.string().array().optional(),
  doYouHaveAnythingElseToAddAboutTheAgent: z.string().min(1, {message: "Atleast 1 character is required."}),
})

export const CustomerPropertiesFilterSchema = z.object({
  propertyFor: z.nativeEnum(PropertyForEnum).optional(),
  minPrice: z.number().optional(),
  maxPrice: z.number().optional(),
  beds: z.number().optional(),
  baths: z.number().optional(),
  homeTypes: z.string().array().optional(),
  areaUnitId: z.string(),

  minArea: z.number().optional(),
  maxArea: z.number().optional(),
  listingTypes: z.string().array().optional(),
  furnishType: z.nativeEnum(FurnishingEnum).optional(),
  propertyState: z.nativeEnum(PropertyStateEnum).optional(),
  possessionState: z.nativeEnum(PossessionStateEnum).optional(),
  amenities: z.string().array().optional(),
  facing: z.nativeEnum(FacingEnum).optional(),
  pointOfInterests: z.string().array().optional(),
  stayType: z.string().optional(),
  rentAmenities: z.string().optional(),
  propertyCategory: z.string().optional(),
  searchQuery: z.string().optional(),
  take: z.number(),
  page: z.number().optional(),
});

const boundsSchema = z.object({
  ne: z.object({
    lat: z.number(),
    lng: z.number(),
  }),
  sw: z.object({
    lat: z.number(),
    lng: z.number(),
  }),
});

export const propertiesFilterSchemaWithBounds = z.object({
  bounds: boundsSchema,
  propertyFor: z.nativeEnum(PropertyForEnum).optional(),
  minPrice: z.number().optional(),
  maxPrice: z.number().optional(),
  beds: z.number().optional(),
  baths: z.number().optional(),
  homeTypes: z.string().array().optional(),
  areaUnitId: z.string().optional(),
  minArea: z.number().optional(),
  maxArea: z.number().optional(),
  listingTypes: z.string().array().optional(),
  furnishType: z.nativeEnum(FurnishingEnum).optional(),
  propertyState: z.nativeEnum(PropertyStateEnum).optional(),
  possessionState: z.nativeEnum(PossessionStateEnum).optional(),
  amenities: z.string().array().optional(),
  facing: z.nativeEnum(FacingEnum).optional(),
  pointOfInterests: z.string().array().optional(),
  stayType: z.string().optional(),
  rentAmenities: z.string().optional(),
  propertyCategory: z.string().optional(),
  searchQuery: z.string().optional(),
  take: z.number().min(1).max(100).default(50),
  cursor: z.string().nullish(), // <-- "cursor" needs to exist, but can be any type
  // direction: z.enum(["forward", "backward"]),
  page: z.number(),
});

export const CustomerAgentsFilterSchema = z.object({
  take: z.number(),
  page: z.number().optional(),
});

export const resumeFormSchema = z.object({
    name: z.string().min(2, {
      message: "Name is required.",
    }),
    email: z
      .string()
      .min(1, { message: "Email is required." })
      .email({ message: "Enter the valid email id." }),
    phoneNumber: z
      .string()
      .min(1, { message: "Phone number is required." })
      .regex(phoneRegex, { message: "Invalid phone number" })
      .max(10,{message:"Phone no can't be of greater than 10 digits."}),
      role:z.string().min(1,{message:"Role is required."}),
      resumeFileKey:z.string().min(1,{message:"Resume is required"})
  });

export const deleteAccountFormSchema=z.object({
    email:z.string().min(1,{message:"Email is required."}).email({message:"Email is invalid."}),
    phoneNumber: z
    .string()
    .min(1, { message: "Phone number is required." })
    .regex(phoneRegex, { message: "Invalid phone number" })
    .max(10,{message:"Phone no can't be of greater than 10 digits."}),
    reason:z.string().min(1,{message:"Reason is requierd."}).min(5,{message:"Reason should contain atleast 5 chars."})
})
