import { db } from "@repo/database";
import { verifyOTP } from "@repo/msg91";

const authenticate = async ({
  phoneNumber,
  otp,
}: {
  phoneNumber: string;
  otp: string;
}) => {
  try {
    const isUser = await db.user.findUnique({
      where: {
        phoneNumber: phoneNumber,
      },
    });

    if (isUser === null) {
      return null;
    }

    const isValid = await verifyOTP(otp, phoneNumber);

    if (isValid) {
      return isUser;
    }
    return null;
  } catch (err) {
    console.log(err);
    return null;
  }
};

export { authenticate };
