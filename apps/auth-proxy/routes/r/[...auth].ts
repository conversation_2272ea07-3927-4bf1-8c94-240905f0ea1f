import { Auth } from "@auth/core";
import Credentials from "@auth/core/providers/credentials";
import { eventHandler, toWebRequest } from "h3";
import {authenticate} from "../authenticate";

export default eventHandler(async (event) =>
  Auth(toWebRequest(event), {
    basePath: "/r",
    secret: process.env.AUTH_SECRET,
    trustHost: !!process.env.VERCEL,
    redirectProxyUrl: process.env.AUTH_REDIRECT_PROXY_URL,
    providers: [
      Credentials({
        name: "Credentials",
        credentials: {
          phoneNumber: {
            label: "PhoneNumber",
            type: "text",
            placeholder: "Phone Number",
          },
          otp: {
            label: "OTP",
            type: "text",
            placeholder: "OTP",
          },
        },
        async authorize(credentials) {
          const { phoneNumber, otp } = credentials as {
            phoneNumber: string;
            otp: string;
          };

          return authenticate({ phoneNumber, otp });
        },
      }),
    ],
  }),
);
