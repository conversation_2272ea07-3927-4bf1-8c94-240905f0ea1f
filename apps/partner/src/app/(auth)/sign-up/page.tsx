"use client";

import { <PERSON><PERSON> } from "@repo/ui/components/ui/button";
import { Input } from "@repo/ui/components/ui/input";
import Link from "next/link";
import { MdKeyboardArrowRight } from "react-icons/md";
import { api } from "~/trpc/react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { cn } from "@repo/ui/lib/utils";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@repo/ui/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/ui/popover";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { signUpInputValidation } from "@repo/validators";
import type { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { toast } from "@repo/ui/components/ui/sonner";
import { Check, ChevronsUpDown } from "lucide-react";
import { useEffect } from "react";

const SignUp = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: cities, isSuccess } = api.user.getCities.useQuery();
  const signupMutation = api.user.signup.useMutation();
  const referralCode = searchParams.get("referralCode");

  const form = useForm<z.infer<typeof signUpInputValidation>>({
    resolver: zodResolver(signUpInputValidation),
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
      pancardNumber: "",
      cityId: "",
      reraNumber: undefined,
      gstNumber: undefined,
      referredBy: referralCode ?? "",
    },
  });

  const adharcardField = form.watch("adharcardNumber");

  useEffect(() => {
    if (adharcardField === "") {
      form.setValue("adharcardNumber", undefined);
    }
    // console.log("aadhar card val-----------", adharcardField);
  }, [adharcardField, form]);

  const onSubmit = async (values: z.infer<typeof signUpInputValidation>) => {
    signupMutation
      .mutateAsync(values)
      .then(() => {
        router.push(
          `/otp-verification?phoneNumber=${values.phoneNumber}`,
        );
      })
      .catch((err) => {
        toast.error(err.message ?? "Error in signing up");
      });
        
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col items-center justify-center gap-2 md:items-start md:justify-normal 2xl:gap-5">
        <h1 className="font-airbnb_w_xbd text-2xl font-extrabold leading-[36px] text-primary-2-850 md:text-[28px] lg:text-[30px] xl:text-[32px] xl:leading-[40px] 2xl:text-[40px] 2xl:leading-[48px]">
          Sign Up
        </h1>
        <p className="text-center font-airbnb_w_bk text-base lg:text-lg 2xl:text-2xl">
          Create account to continue with Deer Connect
        </p>
      </div>

      <div>
        <Form {...form}>
          <form
            className="flex flex-col gap-3 md:gap-4 xl:gap-6"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium lg:text-lg">Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder=""
                      id="name"
                      className="text-text-600"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone Number and Email */}
            <div className="gap-4 md:flex lg:flex-col xl:flex-row">
              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium lg:text-lg">
                        Phone Number
                      </FormLabel>
                      <FormControl className="w-full">
                        <Input
                          type="text"
                          placeholder=""
                          id="name"
                          className="text-text-600"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium lg:text-lg">
                        Email
                      </FormLabel>
                      <FormControl className="w-full">
                        <Input
                          type="text"
                          id="name"
                          className="text-text-600"
                          placeholder=""
                          value={field.value}
                          onChange={(e) =>
                            field.onChange(e.target.value.toLowerCase())
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Pancard Number and Adhar Card */}
            <div className="gap-4 md:flex lg:flex-col xl:flex-row">
              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                <FormField
                  control={form.control}
                  name="pancardNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium lg:text-lg">
                        Pancard Number{" "}
                        <span className="font-airbnb_w_lt text-sm text-text-400">
                          (optional)
                        </span>
                      </FormLabel>
                      <FormControl className="w-full">
                        <Input
                          type="text"
                          id="name"
                          className="text-text-600"
                          placeholder=""
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                <FormField
                  control={form.control}
                  name="adharcardNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium lg:text-lg">
                        Adhar Card Number{" "}
                        <span className="font-airbnb_w_lt text-sm text-text-400">
                          (optional)
                        </span>
                      </FormLabel>
                      <FormControl className="lg:w-full">
                        <Input
                          type="text"
                          className="text-text-600"
                          placeholder=""
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="gap-4 md:flex lg:flex-col xl:flex-row">
              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                <FormField
                  control={form.control}
                  name="cityId"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="font-medium lg:text-lg">
                        City*
                      </FormLabel>
                      <FormControl className="w-full">
                        {/* <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select your city" />
                          </SelectTrigger>
                          <SelectContent>
                            {isSuccess && cities.length > 0 ? (
                              cities.map((item) => (
                                <SelectItem
                                  key={item.id}
                                  value={item.id}
                                  // onClick={() => form.setValue("city", item.name)}
                                >
                                  {item.name}
                                </SelectItem>
                              ))
                            ) : (
                              <p className="p-2">No city</p>
                            )}
                          </SelectContent>
                        </Select> */}
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                // variant=" "
                                role="combobox"
                                className={cn(
                                  "w-full justify-between bg-white text-black hover:bg-white",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value && isSuccess
                                  ? cities.find(
                                      (item) => item.id === field.value,
                                    )?.name
                                  : "Select city"}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                            <Command className="">
                              <CommandInput placeholder="Search city..." />
                              <CommandList>
                                <CommandEmpty>No city found.</CommandEmpty>
                                <CommandGroup>
                                  {isSuccess && cities.length > 0 ? (
                                    cities.map((item) => (
                                      <CommandItem
                                        value={item.name}
                                        key={item.id}
                                        onSelect={() => {
                                          form.setValue("cityId", item.id);
                                        }}
                                      >
                                        {item.name}
                                        <Check
                                          className={cn(
                                            "ml-auto",
                                            item.id === field.value
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                      </CommandItem>
                                    ))
                                  ) : (
                                    <p className="p-2">No city</p>
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                {/* RERA Number */}
                <div className="flex flex-col gap-2">
                  <FormField
                    control={form.control}
                    name="reraNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium lg:text-lg">
                          RERA Number{" "}
                          <span className="font-airbnb_w_lt text-sm text-text-400">
                            (optional)
                          </span>
                        </FormLabel>
                        <FormControl className="w-full">
                          <Input
                            type="text"
                            className="text-text-600"
                            placeholder=""
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            {/* GST Number */}
            <div className="gap-4 md:flex lg:flex-col xl:flex-row">
              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                <div className="flex flex-col gap-2">
                  <FormField
                    control={form.control}
                    name="gstNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium lg:text-lg">
                          GST Number{" "}
                          <span className="font-airbnb_w_lt text-sm text-text-400">
                            (optional)
                          </span>
                        </FormLabel>
                        <FormControl className="w-full">
                          <Input
                            type="text"
                            className="text-text-600"
                            placeholder=""
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="flex flex-col gap-2 md:w-1/2 lg:w-full xl:w-1/2">
                {/* referred by */}
                <div className="flex flex-col gap-2">
                  <FormField
                    control={form.control}
                    name="referredBy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-medium lg:text-lg">
                          Referral Code{" "}
                          <span className="font-airbnb_w_lt text-sm text-text-400">
                            (optional)
                          </span>
                        </FormLabel>
                        <FormControl className="w-full">
                          <Input
                            type="text"
                            className="text-text-600"
                            placeholder=""
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="flex w-full flex-col items-center justify-center gap-4">
              {signupMutation.isPending ? (
                <LoadingButton
                  className="px-[80px] py-3.5 font-medium sm:px-[143px] md:px-[139px]"
                  loading
                >
                  Sending OTP
                </LoadingButton>
              ) : (
                <Button className="px-6 py-3.5 font-medium" type="submit">
                  <p className="px-[80px] sm:px-[143px] md:px-[139px]">
                    Register
                  </p>
                </Button>
              )}

              <div className="flex items-center justify-center gap-3 font-airbnb_w_bk text-text-600">
                <p>Already have a account?</p>
                <Link
                  href="/sign-in"
                  className="flex items-center font-airbnb_w_md font-medium text-secondary-2-700"
                >
                  Login{" "}
                  <MdKeyboardArrowRight className="text-secondary-2-700" />
                </Link>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default SignUp;
