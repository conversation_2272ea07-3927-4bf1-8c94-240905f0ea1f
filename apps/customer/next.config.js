/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
await import("./src/env.js");
import { PrismaPlugin } from "@prisma/nextjs-monorepo-workaround-plugin";

/** @type {import("next").NextConfig} */
const config = {
  reactStrictMode: false,
  compiler: {
    removeConsole: process.env.NODE_ENV === "production" ? true : false,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  experimental: {
    nodeMiddleware: true,
  },
  webpack: (config, { isServer }) => {
     if (isServer) {
      config.plugins = [...config.plugins, new PrismaPlugin()]
    }    
    return config;
  },
  transpilePackages: ["@repo/ui"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "deerconnect-dev.s3.ap-south-1.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "deerconnect.s3.ap-south-1.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
      },
    ],
  },
};

export default config;
