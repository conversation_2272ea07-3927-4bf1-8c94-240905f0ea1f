import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    AUTH_SECRET:
      process.env.NODE_ENV === "production"
        ? z.string()
        : z.string().optional(),
    DATABASE_URL: z.string().url(),
    NODE_ENV: z
      .enum(["development", "test", "production"])
      .default("development"),
    CLOUDINARY_API_SECRET: z.string(),
    CLOUDINARY_API_KEY: z.string(),
    // MEILI_SEARCH_KEY: z.string(),
    // MEILI_SEARCH_URL: z.string().url(),
    // PROPERTY_INDEX: z.string(),
    // PARTNER_INDEX: z.string(),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
    NEXT_PUBLIC_GOOGLE_MAPS_KEY: z.string(),
    NEXT_PUBLIC_MEILI_SEARCH_KEY: z.string(),
    NEXT_PUBLIC_MEILI_SEARCH_URL: z.string().url(),
    NEXT_PUBLIC_PROPERTY_INDEX: z.string(),
    NEXT_PUBLIC_PARTNER_INDEX: z.string(),
    NEXT_PUBLIC_LINK_TO_PARTNER_WEBSITE: z.string(),
    NEXT_PUBLIC_RECAPTCHA_SITE_KEY: z.string(),
    NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET: z.string(),
    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string(),
    NEXT_PUBLIC_CLOUDINARY_API_KEY: z.string(),
    NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME: z.string(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    AUTH_SECRET: process.env.AUTH_SECRET,
    DATABASE_URL: process.env.DATABASE_URL,
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_GOOGLE_MAPS_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY,
    NEXT_PUBLIC_MEILI_SEARCH_KEY: process.env.NEXT_PUBLIC_MEILI_SEARCH_KEY,
    NEXT_PUBLIC_MEILI_SEARCH_URL: process.env.NEXT_PUBLIC_MEILI_SEARCH_URL,
    // PROPERTY_INDEX: process.env.PROPERTY_INDEX,
    // PARTNER_INDEX: process.env.PARTNER_INDEX,
    // MEILI_SEARCH_KEY: process.env.MEILI_SEARCH_KEY,
    // MEILI_SEARCH_URL: process.env.MEILI_SEARCH_URL,
    NEXT_PUBLIC_PROPERTY_INDEX: process.env.NEXT_PUBLIC_PROPERTY_INDEX,
    NEXT_PUBLIC_PARTNER_INDEX: process.env.NEXT_PUBLIC_PARTNER_INDEX,
    NEXT_PUBLIC_LINK_TO_PARTNER_WEBSITE:
      process.env.NEXT_PUBLIC_LINK_TO_PARTNER_WEBSITE,
    NEXT_PUBLIC_RECAPTCHA_SITE_KEY: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
    NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET:
      process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET,
    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:
      process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
    NEXT_PUBLIC_CLOUDINARY_API_KEY: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
    CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,
    CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,
    NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME:
      process.env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME,
  },
  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  /**
   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
   * `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
});
