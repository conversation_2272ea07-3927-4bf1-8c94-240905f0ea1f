import { auth } from "@repo/customer-auth";
import { NextResponse } from "next/server";

const DEFAULT_REDIRECT = "/sign-in";
const PUBLIC_ROUTES = [
  "/",
  "/sign-in",
  "/sign-up",
  "/otp-verification",
  "/unit-converter",
  "/delete-account",
  "/privacy-policy",
  "/terms-and-conditions",
  "/site-map",
  "/safety-guide",
  "/about-us",
  "/careers",
  "/contact-us",
  "/disclaimer",
  "/user-agreement",
];

export default auth((req) => {
  const { nextUrl } = req;

  const isAuthenticated = !!req.auth;
  const isPublicRoute = PUBLIC_ROUTES.includes(nextUrl.pathname);
  const isPropertyListing = nextUrl.pathname.startsWith("/property-listing");

  if (isPublicRoute) return NextResponse.next();

  if (!isAuthenticated && !isPublicRoute) {
    // For property listings, preserve search parameters
    if (isPropertyListing && nextUrl.search) {
      const callbackUrl = encodeURIComponent(
        `${nextUrl.pathname}${nextUrl.search}`,
      );
      const redirectUrl = new URL(
        `${DEFAULT_REDIRECT}?callbackUrl=${callbackUrl}`,
        nextUrl,
      );
      return Response.redirect(redirectUrl);
    }

    return Response.redirect(new URL(DEFAULT_REDIRECT, nextUrl));
  }

  //   const user = await db.user.findFirst({
  //     where: {
  //       id: req.auth?.user?.id,
  //     },
  //   });
  //   if (!user) {
  //     return NextResponse.redirect(new URL(DEFAULT_REDIRECT, req.url));
  //   }

  //if the user has not completed the onboarding steps
  //   if (!user.onboardingStatus) {
  //     //if tries to go to any other route , bring it back to onboarding one
  //     if (!isOnboardingRoute) {
  //       if (user.onboardingStep === ONBOARDING_STEP_ENUM.STEP_1) {
  //         return NextResponse.redirect(
  //           new URL("/onboarding?onboardingStep=1", req.url),
  //         );
  //       } else if (ONBOARDING_STEP_ENUM.STEP_2 === user.onboardingStep) {
  //         return NextResponse.redirect(
  //           new URL("/onboarding?onboardingStep=2", req.url),
  //         );
  //       } else {
  //         return NextResponse.redirect(
  //           new URL("/onboarding?onboardingStep=3", req.url),
  //         );
  //       }
  //     }

  //     //if they are on the onboarding step make sure they are on the correct one
  //     const params = nextUrl.searchParams;
  //     //getting stepNo from url of the page
  //     const stepNo = Number(params.get("onboardingStep"));

  //     //actual step no on which the user is right now
  //     let currentStepNumber;
  //     if (user.onboardingStep === ONBOARDING_STEP_ENUM.STEP_1)
  //       currentStepNumber = 1;
  //     else if (user.onboardingStep === ONBOARDING_STEP_ENUM.STEP_2)
  //       currentStepNumber = 2;
  //     else currentStepNumber = 3;

  //     // If URL step doesn't match the user's current step in database
  //     if (stepNo !== currentStepNumber) {
  //       // Redirect to the correct onboarding step
  //       return NextResponse.redirect(
  //         new URL(`/onboarding?onboardingStep=${currentStepNumber}`, req.url),
  //       );
  //     }
  //   }

  // if (isPublicRoute && isAuthenticated)
  //   return Response.redirect(new URL(DEFAULT_REDIRECT, nextUrl));

  return NextResponse.next();
});

export const config = {
  runtime: "nodejs",
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    "/((?!api|_next/static|_next/image|_next/icons|_next/logos|_next/main|_next/shared|icons|logos|main|shared|videos|images|favicon.ico|sitemap.xml|robots.txt|sign-in|sign-up|logo.svg|footer-logo.svg).*)",
  ],
};
