"use server";

import { auth } from "@repo/customer-auth";
import { db } from "~/server/db";
import { HydrateClient } from "~/trpc/server";
import { redirect } from "next/navigation";
import { ONBOARDING_PARAM_NAME } from "./utils/constants";
import LandingPage from "./components/landing-page/landing-page";

export default async function Home() {
  const session = await auth();

  if (session?.user?.id) {
    const user = await db.customer.findUnique({
      where: {
        id: session.user.id,
      },
    });

    if (user) {
      if (user.onboardingStatus === false) {
        redirect(`/onboarding?${ONBOARDING_PARAM_NAME}=1`);
      }
    }
  }

  return (
    <HydrateClient>
      <LandingPage />
    </HydrateClient>
  );
}
