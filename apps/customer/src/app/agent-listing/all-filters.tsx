"use client";

import { ChevronDown, XIcon } from "lucide-react";
import React, { useEffect, useState } from "react";

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogClose,
  DialogTrigger,
} from "@repo/ui/components/ui/dialog";
import Image from "next/image";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import MoreFilters from "./more-filters";
import PropertyStatusFilters from "./property-status-filters";
import NearbyAgentsFilters from "./nearby-agents-filters";
import { useRouter } from "next/navigation";
import { cn } from "@repo/ui/lib/utils";
import { Button } from "@repo/ui/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@repo/ui/components/ui/accordion";

const AllFilters = () => {
  const router = useRouter();
  const params = new URLSearchParams();

  const [selectedCity, setSelectedCity] = useState<string | undefined>(
    params.get("city") ?? undefined,
  );
  const [propertyStatus, setPropertyStatus] = useState<string | undefined>(
    undefined,
  );
  const [propertyTo, setPropertyTo] = useState<string | undefined>(undefined);
  const [minAgentRaings, setMinAgentRatings] = useState<number | undefined>(
    undefined,
  );
  const [maxAgentRaings, setMaxAgentRatings] = useState<number | undefined>(
    undefined,
  );
  const [minExperience, setMinExperience] = useState<number | undefined>(
    undefined,
  );
  const [maxExperience, setMaxExperience] = useState<number | undefined>(
    undefined,
  );

  // Update URL parameters when filters change
  const updateURLParams = () => {
    if (selectedCity) params.set("city", selectedCity.toString());
    if (propertyStatus) params.set("propertyStatus", propertyStatus.toString());

    if (propertyTo) params.set("propertyTo", propertyTo.toString());
    if (minAgentRaings)
      params.set("minAgentRatings", minAgentRaings.toString());
    if (maxAgentRaings)
      params.set("maxAgentRatings", maxAgentRaings.toString());
    if (minExperience) params.set("minExperience", minExperience.toString());
    if (maxExperience) params.set("maxExperience", maxExperience.toString());

    // Update URL without refreshing the page
    router.replace(`?${params.toString()}`, { scroll: false });
  };

  useEffect(() => {
    updateURLParams();
  }, [
    selectedCity,
    propertyStatus,
    propertyTo,
    minAgentRaings,
    maxAgentRaings,
    minExperience,
    maxExperience,
  ]);

  const resetAllFilters = () => {
    setSelectedCity(undefined);
    setPropertyStatus(undefined);
    setPropertyTo(undefined);
    setMinAgentRatings(undefined);
    setMaxAgentRatings(undefined);
    setMinExperience(undefined);
    setMaxExperience(undefined);
  };

  return (
    <div>
      {/* small screen filters */}
      <div className="block sm:hidden">
        <Dialog>
          <DialogTrigger asChild>
            <div className="rounded-lg bg-primary-500 px-3 py-[9px]">
              <Image
                src="/icons/settings.svg"
                alt="filter"
                height={50}
                width={50}
                className="size-6"
              />
            </div>
          </DialogTrigger>

          <DialogContent className="max-h-full overflow-y-scroll">
            <DialogTitle className="hidden">Agents Popup</DialogTitle>
            <DialogClose className="sticky top-0 z-20 flex items-center justify-end p-4 backdrop-blur-lg">
              <XIcon className="size-4 text-primary-700" />
            </DialogClose>
            <div className="my-6 xs:px-4">
              <Accordion
                type="multiple"
                defaultValue={[
                  "item-1",
                  "item-2",
                  "item-3",
                  "item-4",
                  "item-4",
                  "item-5",
                ]}
                className="space-y-3"
              >
                <AccordionItem
                  value="item-1"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold capitalize text-text-550 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      Nearby Agents
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="pl-4">
                    <NearbyAgentsFilters
                      selectedCity={selectedCity}
                      setSelectedCity={setSelectedCity}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-2"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-text-550 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      Property Status
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <PropertyStatusFilters
                      propertyStatus={propertyStatus}
                      setPropertyStatus={setPropertyStatus}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-3"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-text-550 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      More
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <MoreFilters
                      propertyTo={propertyTo}
                      minExperience={minExperience}
                      minAgentRatings={minAgentRaings}
                      maxExperience={maxExperience}
                      maxAgentRatings={maxAgentRaings}
                      setPropertyTo={setPropertyTo}
                      setMinExperience={setMinExperience}
                      setMinAgentRatings={setMinAgentRatings}
                      setMaxExperience={setMaxExperience}
                      setMaxAgentRatings={setMaxAgentRatings}
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <Button
                variant="outline"
                onClick={resetAllFilters}
                className="my-3 flex w-full items-center gap-2"
              >
                <Image
                  src="/icons/bin.svg"
                  alt="Del"
                  height={50}
                  width={50}
                  className="size-5"
                />{" "}
                <span className="text-lg font-medium">Reset</span>
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* for large screens filters */}
      <div className="no-scrollbar hidden w-full flex-wrap gap-4 sm:flex md:gap-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm text-text-550 lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                selectedCity &&
                  "border border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              Nearby Agents
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <NearbyAgentsFilters
              selectedCity={selectedCity}
              setSelectedCity={setSelectedCity}
            />
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm text-text-550 lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                propertyStatus &&
                  "border border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              Property Status
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <PropertyStatusFilters
              propertyStatus={propertyStatus}
              setPropertyStatus={setPropertyStatus}
            />
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm text-text-550 lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                (propertyTo ??
                  minAgentRaings ??
                  maxAgentRaings ??
                  minExperience ??
                  maxExperience) &&
                  "border border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              More
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <MoreFilters
              propertyTo={propertyTo}
              minExperience={minExperience}
              minAgentRatings={minAgentRaings}
              maxExperience={maxExperience}
              maxAgentRatings={maxAgentRaings}
              setPropertyTo={setPropertyTo}
              setMinExperience={setMinExperience}
              setMinAgentRatings={setMinAgentRatings}
              setMaxExperience={setMaxExperience}
              setMaxAgentRatings={setMaxAgentRatings}
            />
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="outline"
          onClick={resetAllFilters}
          className="flex items-center gap-2 border border-primary-2-700 bg-primary-2-700"
        >
          <Image
            src="/icons/bin.svg"
            alt="Del"
            height={50}
            width={50}
            className="size-5"
          />{" "}
          <span className="font-airbnb_w_md font-medium text-white">Reset</span>
        </Button>
      </div>
    </div>
  );
};

export default AllFilters;
