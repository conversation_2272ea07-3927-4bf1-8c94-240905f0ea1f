import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { Label } from "@repo/ui/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/ui/radio-group";
import React from "react";

const PropertyToOptions = ["Buy", "Rent", "Lease"];

const MinAgentRatingsOptions = [3, 4]; // --- these 2 minratings and maxratings must have same size other it will break the dropdown options
const MaxAgentRatingsOptions = [3.5, 4.5]; // --- see above comment ~

const MinExperiencesOptions = [1, 5, 10]; // --- these 2 minexperience and maxexperience must have same size other it will break the dropdown options
const MaxExperiencesOptions = [5, 10, 30]; // --- see above comment ~

type MoreFiltersProps = {
  propertyTo: string | undefined;
  minAgentRatings: number | undefined;
  maxAgentRatings: number | undefined;
  minExperience: number | undefined;
  maxExperience: number | undefined;
  setPropertyTo: (v: string | undefined) => void;
  setMinAgentRatings: (v: number | undefined) => void;
  setMaxAgentRatings: (v: number | undefined) => void;
  setMinExperience: (v: number | undefined) => void;
  setMaxExperience: (v: number | undefined) => void;
};

const MoreFilters = ({
  propertyTo,
  minAgentRatings,
  minExperience,
  setPropertyTo,
  setMinAgentRatings,
  setMaxAgentRatings,
  setMaxExperience,
  setMinExperience,
}: MoreFiltersProps) => {
  return (
    <div className="space-y-4">
      <div className="space-y-4">
        <p className="text-sm font-semibold">Property To</p>
        <RadioGroup
          defaultValue={propertyTo}
          onValueChange={setPropertyTo}
          className="space-y-2"
        >
          {PropertyToOptions.map((item, idx) => (
            <div className="flex cursor-pointer items-center gap-3" key={idx}>
              <RadioGroupItem value={item.toLowerCase()} id={item} />
              <Label htmlFor={item} className="cursor-pointer">
                {item}
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>

      <div className="space-y-4">
        <p className="text-sm font-semibold">Agent Ratings</p>
        <ul className="space-y-3">
          {MinAgentRatingsOptions.map((item, idx) => {
            return (
              <li className="flex items-center gap-3">
                <Checkbox
                  id={`${item} to ${MaxAgentRatingsOptions[idx]}`}
                  checked={
                    `${item} to ${MaxAgentRatingsOptions[idx]}` ===
                    `${minAgentRatings} to ${MaxAgentRatingsOptions[idx]}`
                  }
                  onCheckedChange={() => setMinAgentRatings(item)}
                />
                <Label
                  htmlFor={`${item} to ${MaxAgentRatingsOptions[idx]}`}
                  onClick={() => {
                    setMinAgentRatings(item);
                    setMaxAgentRatings(MaxAgentRatingsOptions[idx]);
                  }}
                >
                  {item} to {MaxAgentRatingsOptions[idx]}
                </Label>
              </li>
            );
          })}
          <li
            className="flex items-center gap-3"
            onClick={() => {
              setMinAgentRatings(4.5);
              setMaxAgentRatings(undefined);
            }}
          >
            <Checkbox
              id="above 4.5"
              checked={"above 4.5" === `above ${minAgentRatings}`}
              onCheckedChange={() => setMinAgentRatings(4.5)}
            />
            <Label htmlFor="above 4.5">above 4.5</Label>
          </li>
        </ul>
      </div>

      <div className="space-y-4">
        <p className="text-sm font-semibold">Experience</p>
        <ul className="space-y-3">
          {MinExperiencesOptions.map((item, idx) => {
            return (
              <li className="flex items-center gap-3">
                <Checkbox
                  id={`${item} to ${MaxExperiencesOptions[idx]}`}
                  checked={
                    `${item} to ${MaxExperiencesOptions[idx]}` ===
                    `${minExperience} to ${MaxExperiencesOptions[idx]}`
                  }
                  onCheckedChange={() => setMinExperience(item)}
                />
                <Label
                  htmlFor={`${item} to ${MaxExperiencesOptions[idx]}`}
                  onClick={() => {
                    setMinExperience(item);
                    setMaxExperience(MaxExperiencesOptions[idx]);
                  }}
                >
                  {item} to {MaxExperiencesOptions[idx]}
                </Label>
              </li>
            );
          })}
          <li
            onClick={() => {
              setMinExperience(30);
              setMaxExperience(undefined);
            }}
            className="flex items-center gap-3"
          >
            <Checkbox checked={30 === minExperience} />
            Above
          </li>
        </ul>
      </div>
    </div>
  );
};

export default MoreFilters;
