"use client";

import React, { useState } from "react";
import TopBar from "./top-bar";
import Agents from "./agents";

import AgentInfoWindowGmap from "@repo/ui/components/shared/agent-infowindow-gmap";
import type { AgentDetails, Points } from "../types";

const AgentListing = () => {
  const [points, setPoints] = useState<Points[] | []>([]);
  const [agents, setAgents] = useState<AgentDetails[] | []>([]);

  return (
    <div className="flex flex-col gap-[18px] px-5 sm:gap-6 xl:gap-6">
      <TopBar points={points} agents={agents} />
      <div className="w-full items-start lg:flex lg:justify-between">
        <div className="sticky top-[80px] hidden w-full lg:block">
          {/* <Map points={points} agents={agents} /> */}
          <AgentInfoWindowGmap points={points} agents={agents} />
        </div>
        <Agents setPoints={setPoints} setAgents={setAgents} />
      </div>
    </div>
  );
};

export default AgentListing;
