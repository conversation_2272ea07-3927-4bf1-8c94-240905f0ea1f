"use client";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import Image from "next/image";
import React, { Suspense } from "react";

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/ui/dialog";


import AllFilters from "./all-filters";
import { XIcon } from "lucide-react";

import AgentInfowindowGmap from "@repo/ui/components/shared/agent-infowindow-gmap";
import type { AgentDetails, Points } from "../types";

type TopBarProps = {
  points: Points[];
  agents: AgentDetails[] | [];
};

const TopBar = ({ points, agents }: TopBarProps) => {
  //   const [searchText, setSearchText] = useState<string | undefined>(undefined);

  return (
    <Suspense fallback={null}>
      <div className="flex flex-col gap-3 py-[10px] sm:gap-4 sm:py-3 2xl:py-4">
        {/* search bar visible on small screens and hidden over md screens */}
        {/* <SearchBar
          searchText={searchText}
          setSearchText={setSearchText}
          className="flex lg:hidden"
        /> */}

        {/* MapView button and Filter button */}
        <div className="flex items-center justify-between sm:flex-col-reverse sm:items-start sm:gap-4 lg:flex-row-reverse xl:gap-6">
          <div className="flex items-center gap-4 2xl:gap-6">
            {/* <Button className="hidden sm:block lg:py-3 xl:px-6">
              Search result
            </Button> */}

            {/* Map view button */}
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="px-3" asChild>
                  <div className="block flex items-center gap-2 lg:hidden">
                    <span>Map View</span>
                    <Image
                      src="/icons/map.svg"
                      height={50}
                      width={50}
                      alt="map"
                      className="size-[18px]"
                    />
                  </div>
                </Button>
              </DialogTrigger>
              <DialogContent className="h-full max-h-[90vh] max-w-full">
                <DialogTitle className="hidden">Map</DialogTitle>
                <DialogClose className="flex items-center justify-end px-4">
                  <XIcon className="size-4 text-primary-700" />
                </DialogClose>

                {/* <Map points={points} agents={agents} /> */}
                <AgentInfowindowGmap points={points} agents={agents} />
              </DialogContent>
            </Dialog>

            {/* search bar visibile on large screens and hidden under lg */}
            {/* <SearchBar
              searchText={searchText}
              setSearchText={setSearchText}
              className="hidden lg:flex"
            /> */}
          </div>

          {/* Filter button */}
          <Suspense fallback={<div>Loading...</div>}>
            <AllFilters />
          </Suspense>
        </div>
      </div>
    </Suspense>
  );
};

export default TopBar;
