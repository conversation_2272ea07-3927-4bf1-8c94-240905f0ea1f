"use client";

import React, { useState } from "react";

// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@repo/ui/components/ui/dropdown-menu";
// import { ChevronDown } from "lucide-react";
import AgentListResult from "./agent-list-result";
import type { AgentDetails, Points } from "../types";


type AgentsProps = {
  setPoints: (v: Points[]) => void;
  setAgents: (v: AgentDetails[] | []) => void;
};

const Agents = ({ setPoints, setAgents }: AgentsProps) => {
  const [totalResults, setTotalResults] = useState<number>(0);

  return (
    <div className="flex flex-col gap-3 lg:min-w-[375px] xl:min-w-[712px] 2xl:w-full">
      {/* Sort by and Results count bar with a title */}
      <div className="flex flex-col py-2 lg:px-5 lg:py-[10px]">
        <p className="text-lg font-medium text-text-600 lg:text-xl 2xl:text-2xl">
          Real Estate Home
        </p>
        <div className="flex items-center justify-between">
          <p className="font-airbnb_w_bk text-sm text-text-600 2xl:text-lg">
            Total {totalResults} results
          </p>
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="flex items-center gap-2 text-sm font-medium text-primary-600 2xl:text-lg">
                Sort by: home for sale
                <ChevronDown className="size-5" />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Profile</DropdownMenuItem>
              <DropdownMenuItem>Billing</DropdownMenuItem>
              <DropdownMenuItem>Team</DropdownMenuItem>
              <DropdownMenuItem>Subscription</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu> */}
        </div>
      </div>

      <AgentListResult
        setPoints={setPoints}
        setAgents={setAgents}
        setTotalResults={setTotalResults}
      />
    </div>
  );
};

export default Agents;
