"use client";

import React, { useEffect, useMemo } from "react";
import { api } from "~/trpc/react";

import { useRouter, useSearchParams } from "next/navigation";
import PaginationBar from "../components/shared/pagination-bar";
import AgentCard from "@repo/ui/components/shared/agent-card";
import AgentCardSkeleton from "@repo/ui/components/skeleton/agent-card-skeleton";
import type { AgentListResultProps, Points } from "../types";



const AgentListResult = ({
    setPoints,
    setAgents,
    setTotalResults,
}: AgentListResultProps) => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { data, isLoading } = api.agent.getAgentsAccordingTofilters.useQuery({
        page: Number(searchParams.get("page") ?? 1),
        take: 4,
    });

    const handePageChange = (pageNumber: string) => {
        const params = new URLSearchParams(searchParams);
        params.set("page", pageNumber);
        router.push(`?${params}`);
    };

    const agentData = useMemo(() => data?.agents ?? [], [data?.agents]);
    const totalPages = data?.totalPages ?? 0;
    const totalResults = data?.totalResults ?? 0;

    useEffect(() => {
        if (agentData.length) {
            const latLongs: Points[] = [{ lat: 23.512, lng: 80.329 }];
            agentData.map((item) =>
                latLongs.push({
                    lat: Number(item.latitude),
                    lng: Number(item.longitude),
                }),
            );

            setPoints(latLongs);
            setAgents(agentData);
            setTotalResults(totalResults);
        }
    }, [agentData, setPoints, setAgents, setTotalResults, totalResults]);

    return (
        <>
            <div className="flex flex-wrap justify-center gap-5 2xl:justify-start 2xl:px-5">
                {isLoading ? (
                    Array.from({ length: 4 }).map((_, idx) => (
                        <AgentCardSkeleton key={idx} />
                    ))
                ) : agentData.length ? (
                    agentData.map((agent) => (
                        <div className="flex flex-wrap justify-start gap-5">
                            <AgentCard link={`/agent-detail/${agent.id}`} key={agent.id} {...agent} />
                        </div>
                    ))
                ) : (
                    <div className="text-xl font-semibold text-primary-600">
                        No Agent Found
                    </div>
                )}
            </div>
            <PaginationBar
                selectedPage={Number(searchParams.get("page") ?? 1)}
                setSelectedPage={handePageChange}
                totalPages={totalPages}
            />
        </>
    );
};

export default AgentListResult;
