import { Label } from "@repo/ui/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/ui/radio-group";
import React from "react";

export const Top10CitiesOfIndia = [
  "Mumbai",
  "Jaipur",
  "Ahmedabad",
  "Chennai",
  "Banglore",
  "Kolkata",
  "Hyderabad",
  "Pune",
  "Delhi",
  "Surat",
];

type NearbyAgentsFiltersProps = {
  selectedCity: string | undefined;
  setSelectedCity: (v: string | undefined) => void;
};

const NearbyAgentsFilters = ({
  selectedCity,
  setSelectedCity,
}: NearbyAgentsFiltersProps) => {
  return (
    <div>
      <RadioGroup defaultValue={selectedCity} onValueChange={setSelectedCity}>
        {Top10CitiesOfIndia.map((item, idx) => (
          <div
            className="flex cursor-pointer items-center gap-3 py-2"
            key={idx}
          >
            <RadioGroupItem value={item.toLowerCase()} id={item} />
            <Label htmlFor={item} className="cursor-pointer">
              {item}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default NearbyAgentsFilters;
