import { Label } from "@repo/ui/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/ui/radio-group";
import React from "react";

const PropertyStatus = ["Sold", "Active", "In Process"];

type PropertyStatusFiltersProps = {
  propertyStatus: string | undefined;
  setPropertyStatus: (v: string | undefined) => void;
};

const PropertyStatusFilters = ({
  propertyStatus,
  setPropertyStatus,
}: PropertyStatusFiltersProps) => {
  return (
    <div>
      <RadioGroup
        defaultValue={propertyStatus}
        onValueChange={setPropertyStatus}
      >
        {PropertyStatus.map((item, idx) => (
          <div
            className="flex cursor-pointer items-center gap-3 py-2"
            key={idx}
          >
            <RadioGroupItem value={item.toLowerCase()} id={item} />
            <Label htmlFor={item} className="cursor-pointer">
              {item}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default PropertyStatusFilters;
