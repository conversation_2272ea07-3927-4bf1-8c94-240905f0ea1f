"use client";

import type { MouseE<PERSON> } from "react";
import LikeAgentButton from "./like-agent-button";
import ShareAgentButton from "~/app/components/shared/share-agent-button";
type LikeAndShareProps = {
  mainClassname?: string;
  likeBtnClassname?: string;
  shareBtnClassname?: string;
  agentId: string;
};
const AgentLikeAndShareButton = ({
  agentId,
  likeBtnClassname,
  shareBtnClassname,
}: LikeAndShareProps) => {
  const handleShareClick = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation(); // This stops the event from bubbling up
  };
  return (
    <>
      <div className="flex flex-row items-center gap-[15px]">
        <LikeAgentButton
          agentId={agentId}
          likeBtnClassname={likeBtnClassname}
        ></LikeAgentButton>

        <div onClick={handleShareClick}>
          <ShareAgentButton
            agentId={agentId}
            shareBtnClassname={shareBtnClassname}
          ></ShareAgentButton>
        </div>
      </div>
    </>
  );
};

export default AgentLikeAndShareButton;
