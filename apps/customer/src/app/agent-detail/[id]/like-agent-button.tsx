"use client";
import React, { useState, useEffect } from "react";
import { HeartIcon } from "lucide-react";
import type { MouseEvent } from "react";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { cn } from "@repo/ui/lib/utils";

const LikeAgentButton = ({
  agentId,
  likeBtnClassname,
}: {
  agentId: string;
  likeBtnClassname?: string;
}) => {
  const [isFilled, setIsFilled] = useState<boolean>(false);
  const trpcUtils = api.useUtils();

  const { data: isLiked, isLoading } = api.likeAgent.isAgentLiked.useQuery(
    { agentId },
    {
      refetchOnWindowFocus: false,
    },
  );

  const likeAgentMutation = api.likeAgent.addLikedAgents.useMutation();

  useEffect(() => {
    if (typeof isLiked === "boolean") {
      setIsFilled(isLiked);
    }
  }, [isLiked]);

  const handleLikeAgents = async (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setIsFilled(!isFilled);

    try {
      await likeAgentMutation.mutateAsync({ agentId });
      toast.success(
        isFilled ? "Removed from liked agents" : "Added to liked agents",
      );
      void trpcUtils.likeAgent.invalidate();
    } catch (error) {
      setIsFilled(isFilled);
      toast.error("Failed to update like status");
    }
  };

  return (
    <div
      className="group cursor-pointer rounded-full bg-white p-[6px]"
      onClick={handleLikeAgents}
    >
      <HeartIcon
        className={cn(
          `size-5 text-secondary-2-700 transition-all duration-300 ease-in-out ${isFilled ? "scale-110" : "scale-100"} ${isLoading ? "opacity-50" : "opacity-100"} `,
          likeBtnClassname,
        )}
        fill={isFilled ? "currentColor" : "none"}
      />
    </div>
  );
};

export default LikeAgentButton;
