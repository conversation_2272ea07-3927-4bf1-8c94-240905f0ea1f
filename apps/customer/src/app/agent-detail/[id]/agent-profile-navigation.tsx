"use client";

import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";
import AgentLikeAndShareButton from "./agent-like-share-button";

const AgentProfileNavigation = ({ slug }: { slug: string }) => {
  const router = useRouter();
  const handleBack = () => {
    router.back();
  };

  return (
    <div className="flex cursor-pointer items-center justify-between">
      <ChevronLeft
        className="size-5 md:size-6 lg:size-[30px] 2xl:size-10"
        onClick={handleBack}
      />
      <div className="flex items-center gap-2 lg:gap-3 xl:gap-[14px]">
        {/* <div className="rounded-full bg-white p-[6px]">
          <HeartIcon className="size-5 text-secondary-2-700 md:size-6 lg:size-[30px] 2xl:size-10" />
        </div>
        <Share2Icon className="size-5 text-secondary-2-700 md:size-6 lg:size-[30px] 2xl:size-10" /> */}
        <AgentLikeAndShareButton
          agentId={slug}
          likeBtnClassname="size-5 md:size-6 lg:size-[30px]"
          shareBtnClassname="size-5 md:size-6 lg:size-[30px]"
        />
      </div>
    </div>
  );
};

export default AgentProfileNavigation;
