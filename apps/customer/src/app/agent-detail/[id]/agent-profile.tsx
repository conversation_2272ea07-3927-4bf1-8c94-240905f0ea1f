import { formatDistanceToNow } from "date-fns";
import Image from "next/image";
import React from "react";
import type {
  TAgentProfileWithExtraDetails,
  TAgentSoldPropertiesWithExtraDetails,
  TCustomerProfile,
} from "~/app/types";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@repo/ui/components/ui/tabs";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";

import PolygonOnMap from "@repo/ui/components/shared/polygon-on-map";
import PropertyCard from "@repo/ui/components/shared/property-card";
import LikePropertyButton from "~/app/components/shared/like-property-button";
import ContactAgentButton from "~/app/components/shared/contact-agent-button";
import SharePropertyButton from "~/app/components/shared/share-property-button";
import AgentProfileNavigation from "./agent-profile-navigation";
import { auth } from "@repo/customer-auth";

type AgentProfileProps = {
  agent: TAgentProfileWithExtraDetails;
  customer: TCustomerProfile;
  soldProperties: TAgentSoldPropertiesWithExtraDetails[];
  slug: string;
};

const AgentProfile = async ({
  agent,
  soldProperties,
  customer,
  slug,
}: AgentProfileProps) => {
  const session = await auth();

  const companyDetails = [
    {
      info: "Website:",
      value: `${agent.company?.companyWebsiteLink ?? ""}`,
    },
    {
      info: "Location:",
      value: `${agent.company?.companyLocation ?? ""}`,
    },
    {
      info: "Mobile:",
      value: `${agent.company?.phoneNumber ?? ""}`,
    },
    {
      info: "Email:",
      value: `${agent.company?.email ?? ""}`,
    },
    {
      info: "Fax:",
      value: `${agent.company?.fax ?? ""}`,
    },
  ];

  return (
    <div className="bg-[rgba(244,_240,_238,_0.50)] py-[10px] md:py-3 xl:py-4 2xl:py-5">
      {/* navigation, agent details */}
      <div className="flex flex-col gap-3 px-5 md:gap-4 lg:gap-5 xl:gap-6 2xl:gap-8">
        {/* navigation */}
        <AgentProfileNavigation slug={slug} />
        {/* profile image and name  */}
        <div className="flex items-center gap-3 lg:gap-4 xl:gap-5 2xl:gap-6">
          <div className="relative aspect-[86/86] w-[86px] rounded-[18px] md:aspect-[150/150] md:w-[150px] lg:aspect-[170/170] lg:w-[170px] xl:aspect-[190/190] xl:w-[190px]">
            <Image
              alt={agent.name}
              fill={true}
              src={agent.filePublicUrl ?? "/images/placeholder-user-image.jpg"}
              className="relative rounded-[18px] object-cover"
            />
          </div>

          <div className="flex flex-1 flex-col md:gap-4 xl:gap-6 2xl:gap-[30px]">
            {/* name and company name and stars */}
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-[2px] md:gap-1">
                <h2 className="font-airbnb_w_xbd text-[20px] text-primary-2-750 md:text-2xl lg:text-[30px] 2xl:text-4xl">
                  {agent.name}
                </h2>
                <p className="line-clamp-1 font-airbnb_w_bk text-sm font-medium text-text-600 md:text-lg lg:text-xl 2xl:text-2xl">
                  {agent.company?.companyName}
                </p>
              </div>

              <div className="flex flex-col items-center justify-between">
                <div className="flex items-center gap-[2px]">
                  <Image
                    src="/icons/star.svg"
                    alt="star"
                    height={100}
                    width={100}
                    className="size-4"
                  />
                  <span className="text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl">
                    {agent.rating ?? "N/A"}
                  </span>
                </div>
                <p className="text-xs md:text-sm lg:text-lg 2xl:text-xl">
                  {agent.reviews ?? "N/A"} Reviews
                </p>
              </div>
            </div>

            {/* stars and statistics for over md screens */}
            <div className="hidden items-center md:flex md:justify-between">
              <Statistics
                propertiesSold={agent.propertiesSold}
                experience={agent.experience}
                createdAt={agent.createdAt}
              />
              <div className="flex justify-end lg:flex-1">
                <ContactAgentButton
                  agentId={agent.id}
                  connectedAgentId={customer.connections[0]?.agentId ?? ""}
                />
              </div>
            </div>
          </div>
        </div>

        {/* statistics for lessthan md screens */}
        <div className="md:hidden">
          <Statistics
            propertiesSold={agent.propertiesSold}
            experience={agent.experience}
            createdAt={agent.createdAt}
          />
        </div>
      </div>

      {/* location */}
      <div className="mt-3 flex items-center justify-between bg-[#F3EFEC] px-5 py-2">
        <p className="flex items-center gap-1.5 font-airbnb_w_bk text-xs text-primary-600 md:text-base 2xl:text-lg">
          <Image
            src="/icons/location-pin.svg"
            alt="location"
            height={50}
            width={50}
            className="size-4 lg:size-5 2xl:text-2xl"
          />
          Location:
        </p>

        <p className="text-xs font-medium text-text-550 md:text-base 2xl:text-lg">
          {agent.userLocation}
        </p>
      </div>

      {/* tabs */}
      <div className="px-5 py-5">
        <Tabs defaultValue="About-Agent">
          <TabsList className="mb-6 rounded-lg bg-secondary-2-100 text-secondary-2-700 md:mb-4 lg:mb-5 xl:mb-6 2xl:mb-[30px]">
            <TabsTrigger
              value="About-Agent"
              className="rounded-bl-sm rounded-tl-sm px-2.5 py-2 md:px-4 md:py-3"
            >
              About Agent
            </TabsTrigger>
            <TabsTrigger
              value="Listing"
              className="rounded-br-sm rounded-tr-sm px-2.5 py-2 md:px-4 md:py-3"
            >
              Listing
            </TabsTrigger>
          </TabsList>
          <TabsContent value="About-Agent">
            <div className="flex flex-col gap-4 xl:gap-5 2xl:gap-6">
              <div className="flex flex-col justify-between gap-5 md:gap-4 lg:flex-row">
                <div className="flex w-full flex-col gap-6 md:gap-3 lg:w-[60%] lg:gap-[14px] xl:w-[58%] xl:gap-4 2xl:w-[68%] 2xl:gap-[18px]">
                  {/* agent description */}
                  <div className="font-airbnb_w_bk text-sm font-normal text-text-600 2xl:text-base">
                    {agent.bio}
                  </div>
                  {/* operation areas */}
                  {agent.operationArea.length ? (
                    <div className="flex flex-col gap-2 md:gap-3 2xl:gap-[14px]">
                      <div className="font-airbnb_w_md text-sm font-medium text-text-550 md:text-lg lg:text-xl 2xl:text-2xl">
                        Operation Areas:
                      </div>
                      <div className="flex flex-wrap items-center gap-3 2xl:gap-[14px]">
                        {agent.operationArea.map((area, index) => (
                          <>
                            <div
                              key={index}
                              className="rounded-md bg-primary-2-100 px-2.5 py-1.5 font-airbnb_w_md text-xs font-medium text-primary-2-700 md:px-3 md:py-2.5 md:text-sm lg:text-base 2xl:text-lg"
                            >
                              {area.name}
                            </div>
                          </>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <></>
                  )}
                  {/* property-descrption */}
                  {agent.company && (
                    <div className="flex flex-col gap-5 md:gap-[14px] 2xl:gap-4">
                      <div className="flex flex-row items-start gap-3 2xl:gap-[14px]">
                        <div className="relative aspect-square w-[48px] md:w-[80px] xl:w-[100px]">
                          <Image
                            src={
                              agent.company.filePublicUrl ??
                              "/images/agent-fallback.png"
                            }
                            alt="property-logo-image"
                            className="w-[48px] rounded-[26px] object-cover"
                            fill
                          />
                        </div>
                        <div className="flex flex-col gap-2 md:gap-1.5 xl:gap-2">
                          <div className="font-airbnb_w_bd text-base font-bold text-secondary-2-700 lg:text-lg xl:text-xl 2xl:text-2xl">
                            {agent.company.companyName}
                          </div>
                          <div className="font-airbnb_w_bk text-sm font-normal text-text-550 2xl:text-base">
                            {agent.company.about}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex w-full flex-col gap-[14px] md:gap-4 lg:w-[38%] lg:justify-between xl:w-[40%] 2xl:w-[30%]">
                  {companyDetails.map((detail, index) => (
                    <React.Fragment key={index}>
                      {detail.value && (
                        <div className="flex flex-row items-start justify-between gap-5 rounded-sm bg-primary-2-100 px-3 py-2 text-base font-normal 2xl:text-lg">
                          <div className="font-airbnb_w_bk text-primary-2-800">
                            {detail.info}
                          </div>
                          <div className="text-right font-airbnb_w_md">
                            {detail.value}
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  ))}
                </div>
                <div className="flex w-full justify-end md:hidden">
                  <ContactAgentButton
                    agentId={agent.id}
                    connectedAgentId={customer.connections[0]?.agentId ?? ""}
                  />
                </div>
              </div>
              {agent.company?.companyLatitude &&
                agent.company.companyLongitude && (
                  <PolygonOnMap
                    className="h-[345px] w-full"
                    point={{
                      lat: Number(agent.company.companyLatitude),
                      lng: Number(agent.company.companyLongitude),
                    }}
                  />
                )}
            </div>
          </TabsContent>
          <TabsContent value="Listing">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-5">
                <div className="flex items-center justify-between">
                  <div className="font-airbnb_w_md text-2xl font-medium text-text-600">
                    Listed (
                    {agent.properties.length ? agent.properties.length : "0"})
                  </div>
                </div>
                {agent.properties.length ? (
                  <Carousel
                    opts={{
                      align: "start",
                    }}
                    className="w-full"
                  >
                    <CarouselContent>
                      {agent.properties.map((item, index) => (
                        <CarouselItem
                          key={index}
                          className="basis-[356px] md:basis-[364px]"
                        >
                          <PropertyCard
                            key={index}
                            property={item}
                            locationIcon="/icons/location.svg"
                            id={item.id}
                            propertyOwnerId={item.userId}
                            userId={session?.user?.id}
                            contactOrCheckResponsesButton={
                              <ContactAgentButton
                                agentId={agent.id}
                                connectedAgentId={
                                  customer.connections[0]?.agentId ?? ""
                                }
                              />
                            }
                            likePropertyButton={
                              <LikePropertyButton
                                propertyId={item.id}
                                isPropertyLiked={
                                  !!item.customerFavourites.length
                                }
                              />
                            }
                            sharePropertyButton={
                              <SharePropertyButton propertyId={item.id} />
                            }
                          />
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                  </Carousel>
                ) : (
                  <div className="text-lg">No listings available</div>
                )}
              </div>

              <div className="flex flex-col gap-5">
                <div className="flex items-center justify-between">
                  <div className="font-airbnb_w_md text-2xl font-medium text-text-600">
                    Sold ({soldProperties.length})
                  </div>
                  {soldProperties.length ? (
                    <div className="font-airbnb_w_md text-lg font-medium text-secondary-600 underline">
                      View All
                    </div>
                  ) : (
                    <></>
                  )}
                </div>
                {soldProperties.length ? (
                  <Carousel
                    opts={{
                      align: "start",
                    }}
                    className="w-full"
                  >
                    <CarouselContent>
                      {soldProperties.map((item, index) => (
                        <CarouselItem
                          key={index}
                          className="basis-[356px] md:basis-[364px]"
                        >
                          <PropertyCard
                            key={index}
                            property={item}
                            locationIcon="/icons/location.svg"
                            id={item.id}
                            propertyOwnerId={item.userId}
                            userId={session?.user?.id}
                            contactOrCheckResponsesButton={
                              <ContactAgentButton
                                agentId={agent.id}
                                connectedAgentId={
                                  customer.connections[0]?.agentId ?? ""
                                }
                              />
                            }
                            likePropertyButton={
                              <LikePropertyButton
                                propertyId={item.id}
                                isPropertyLiked={
                                  !!item.customerFavourites.length
                                }
                              />
                            }
                            sharePropertyButton={
                              <SharePropertyButton propertyId={item.id} />
                            }
                          />
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                  </Carousel>
                ) : (
                  <div className="text-lg">No property available</div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AgentProfile;

type StatisticsProps = {
  propertiesSold: number | null;
  experience: string | null;
  createdAt: Date;
};

const Statistics = ({
  propertiesSold,
  experience,
  createdAt,
}: StatisticsProps) => {
  return (
    <div className="flex flex-1 items-center gap-[10px]">
      {/* total sales */}
      <div className="flex flex-1 flex-col items-center justify-center gap-0.5">
        <span className="font-airbnb_w_bd text-primary-2-700 md:text-lg lg:text-xl">
          {propertiesSold ?? "0"}
        </span>
        <p className="flex items-center gap-0.5 text-nowrap font-airbnb_w_bk text-xs text-text-600 md:text-sm lg:text-base">
          <Image
            src="/icons/home.svg"
            alt="home"
            height={50}
            width={50}
            className="size-3"
          />
          Total sales closed
        </p>
      </div>

      {/* experience */}
      <div className="flex flex-1 flex-col items-center justify-center gap-0.5 border-l border-[#949494]">
        <span className="font-airbnb_w_bd text-primary-2-700 md:text-lg lg:text-xl">
          {experience ?? "0"} yrs
        </span>
        <p className="flex items-center gap-0.5 font-airbnb_w_bk text-xs text-text-600 md:text-sm lg:text-base">
          <Image
            src="/icons/experience.svg"
            alt="home"
            height={50}
            width={50}
            className="size-3"
          />
          Experience
        </p>
      </div>

      {/* active */}
      <div className="flex flex-1 flex-col items-center justify-center gap-0.5 border-l border-[#949494]">
        <span className="font-airbnb_w_bd text-primary-2-700 md:text-lg lg:text-xl">
          {formatDistanceToNow(createdAt)}
        </span>
        <p className="flex items-center gap-0.5 font-airbnb_w_bk text-xs text-text-600 md:text-sm lg:text-base">
          <Image
            src="/icons/clock.svg"
            alt="home"
            height={50}
            width={50}
            className="size-3"
          />
          Active
        </p>
      </div>
    </div>
  );
};
