import React from "react";
import { api } from "~/trpc/server";
import AgentProfile from "./agent-profile";

const AgentDetailPage = async ({
  params,
}: {
  params: Promise<{ id: string }>;
}) => {
  const agentId = (await params).id;
  const agentDetail = await api.agent.getProfileById({ agentId });
  const customerDetail = await api.user.getProfile();

  console.log("agent detail: ", agentDetail);

  return (
    <>
      {agentDetail.agent ? (
        <AgentProfile
          customer={customerDetail}
          agent={agentDetail.agent}
          soldProperties={agentDetail.soldProperteis}
          slug={agentId}
        />
      ) : (
        <div className="flex min-h-[50vh] items-center justify-center text-xl font-bold text-primary-700">
          Agent Not Found
        </div>
      )}
    </>
  );
};

export default AgentDetailPage;
