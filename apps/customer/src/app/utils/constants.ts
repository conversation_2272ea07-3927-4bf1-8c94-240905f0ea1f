export const CHAT_PARAM_NAME = "conversationId";
export const ONBOARDING_PARAM_NAME = "step";
import { env } from "~/env";
import FacebookIcon from "../../../public/icons/top-nav/facebook.svg";
import InstagramIcon from "../../../public/icons/top-nav/instagram.svg";
import LinkedinIcon from "../../../public/icons/top-nav/linkedin.svg";
import XIcon from "../../../public/icons/top-nav/x.svg";
import Youtube from "../../../public/icons/top-nav/youtube.svg";

export const OnboadingCards = [
  {
    id: 1,
    image: "/icons/buy-icon.svg",
    alt: "buy-icon",
    title: "Buy a Property",
    text: "Let us help you find the perfect place to call your own",
    for: "BUY",
  },
  {
    id: 2,
    image: "/icons/buy-icon.svg",
    alt: "rent-icon",
    title: "Rent a Property",
    text: "Looking to sell your property faster, We are here to assist you",
    for: "RENT",
  },
  {
    id: 3,
    image: "/icons/buy-icon.svg",
    alt: "sell-icon",
    title: "Sell a Property",
    text: "Looking to sell your property faster, We are here to assist you",
    for: "SELL",
  },
];

export const AuthSlides = [
  {
    id: 1,
    url: "/images/login-image-1.png",
    heading: "Sell, Rent",
    subHeading: "Find your dream home. Buy or rent, search, filter, move in.",
  },
  {
    id: 2,
    url: "/images/login-image-2.png",
    heading: "Trusted Agent Partners",
    subHeading:
      "Trust verified RERA agents. Secure your property with seal number.",
  },
  {
    id: 3,
    url: "/images/login-image-3.png",
    heading: "Free Listings, Wider Reach",
    subHeading: "Reach agents & create listing at No Cost.",
  },
];

export const SocialMediaLinks = [
  {
    id: 1,
    src: FacebookIcon,
    alt: "facebook",
    link: "https://www.facebook.com/welcomemydeer/",
  },
  {
    id: 2,
    src: InstagramIcon,
    alt: "instagram",
    link: "https://www.instagram.com/welcomemydeer/",
  },
  //   {
  //     id: 3,
  //     src: LinkedinIcon,
  //     alt: "linkedin",
  //     link: "https://www.linkedin.com/company/deerconnect",
  //   },
  //   {
  //     id: 4,
  //     src: XIcon,
  //     alt: "x",
  //     link: "https://x.com/DeerConnect",
  //   },
  //   {
  //     id: 5,
  //     src: Youtube,
  //     alt: "youtube",
  //     link: "https://www.youtube.com/@DeerConnect",
  //   },
];

export const ServicesCards = [
  {
    title: "Buy a Property",
    desc: "Let us help you find the perfect place to call your own",
    button: "Begin Your Journey",
    image: "/images/buy-home.png",
    link: "/property-listing?propertyFor=SALE",
  },
  {
    title: "Sell a Property",
    desc: "Looking to sell your property faster, We are here to assist you",
    button: "Search",
    image: "/images/rent-home.png",
    link: `${env.NEXT_PUBLIC_LINK_TO_PARTNER_WEBSITE}`,
  },
  {
    title: "Rent a Property",
    desc: "Have a property you want to rent out? We're here to help.",
    button: "Start your Search",
    image: "/images/rent-home.png",
    link: "/property-listing?propertyFor=RENT",
  },
];

export const RealEstateJourneyCards = [
  {
    title: "Global Exposure",
    description:
      "Showcase your listing beyond borders through syndication on international real estate websites and luxury lifestyle publications, reaching millions of potential buyers worldwide.",
    imageRight: true,
    url: "/images/real-estate-image-1.png",
    border: true,
  },
  {
    title: "Global Exposure",
    description:
      "Showcase your listing beyond borders through syndication on international real estate websites and luxury lifestyle publications, reaching millions of potential buyers worldwide.",
    imageRight: false,
    url: "/images/real-estate-image-2.png",
    border: false,
  },
  {
    title: "Global Exposure",
    description:
      "Showcase your listing beyond borders through syndication on international real estate websites and luxury lifestyle publications, reaching millions of potential buyers worldwide.",
    imageRight: true,
    url: "/images/real-estate-image-3.png",
    border: true,
  },
  {
    title: "Global Exposure",
    description:
      "Showcase your listing beyond borders through syndication on international real estate websites and luxury lifestyle publications, reaching millions of potential buyers worldwide.",
    imageRight: false,
    url: "/images/real-estate-image-4.png",
    border: false,
  },
];

export const CTAStoresCards = [
  {
    id: 1,
    icon: "/icons/apple-store.svg",
    title: "Download on the",
    storeName: "App Store",
  },
  {
    id: 2,
    icon: "/icons/play-store.svg",
    title: "GET IT ON",
    storeName: "Google Play",
  },
];

export const NavLinks = [
  {
    text: "Home",
    link: "/",
  },
  {
    text: "Buy",
    link: "/property-listing?propertyFor=SALE",
  },
  {
    text: "Rent",
    link: "/property-listing?propertyFor=RENT",
  },
  //   {
  //     text: "Explore",
  //     link: "/explore",
  //   },
  //   {
  //     text: "Projects",
  //     link: "/projects",
  //   },
];

export const FooterSocialIcons = [
  {
    id: 1,
    src: FacebookIcon,
    alt: "facebook",
    link: "https://www.facebook.com/deerconnect",
  },
  {
    id: 2,
    src: InstagramIcon,
    alt: "instagram",
    link: "https://www.instagram.com/deerconnect",
  },
  {
    id: 3,
    src: LinkedinIcon,
    alt: "linkedin",
    link: "https://www.linkedin.com/company/deerconnect",
  },
  {
    id: 4,
    src: XIcon,
    alt: "x",
    link: "https://x.com/DeerConnect",
  },
  {
    id: 5,
    src: Youtube,
    alt: "youtube",
    link: "https://www.youtube.com/@DeerConnect",
  },
];

export const ProfileSidebarSettingsLinks = [
  {
    name: "Edit Profile",
    link: "/profile/edit",
    iconPath: "/icons/edit-profile.svg",
  },
  {
    name: "Property History",
    link: "/profile/property-history",
    iconPath: "/icons/property-history.svg",
  },
  {
    name: "My Reviews",
    link: "/profile/my-reviews",
    iconPath: "/icons/my-reviews.svg",
  },
  {
    name: "Settings",
    link: "/profile/settings",
    iconPath: "/icons/profile-settings.svg",
  },
  {
    name: "Favourites",
    link: "/profile/favourites",
    iconPath: "/icons/favourites.svg",
  },
  {
    name: "Chats",
    link: "/profile/chats",
    iconPath: "/icons/chats.svg",
  },
];

export const ProfileSidebarReportAndHelpLinks = [
  //   {
  //     name: "Report Fraud",
  //     link: "/profile/report-fraud",
  //     iconPath: "/icons/report-fraud.svg",
  //   },
  {
    name: "Help Center",
    link: "/profile/help-center",
    iconPath: "/icons/help-center.svg",
  },
];

export const ProfileSidebarReferralsAndFeedbackLinks = [
  {
    name: "Refer Your Friend",
    link: "/profile/refer-your-friend",
    iconPath: "/icons/refer-your-friend.svg",
  },
  //   {
  //     name: "Give Us Feedback",
  //     link: "/profile/give-us-feedback",
  //     iconPath: "/icons/give-us-feedback.svg",
  //   },
];

export const ProfileSidebarTermsAndConditionsLinks = [
  {
    name: "Privacy Policy",
    link: "/profile/privacy-policy",
    iconPath: "/icons/policy-terms.svg",
  },
  {
    name: "Terms of Services",
    link: "/profile/terms-of-services",
    iconPath: "/icons/policy-terms.svg",
  },
];

export const PossessionPropertyOptions = [
  "Immediate",
  "Within 3 months",
  "Under 1 year",
  "Below 3 years",
];

export const RealEstateAdvantages = [
  {
    id: "0",
    title: "PAN India Exposure",
    description:
      "Reach a vast network of brokers across India, allowing you to significantly expand your property listings and reach potential clients in every corner of the country.",
  },
  {
    id: "1",
    title: "Direct Connectedness",
    description:
      "Enjoy the benefits of connecting directly with verified brokers nationwide. Our Platform removes the need for middlemen, ensuring you have full control over your transactions without having to distribute your profits amongst a chain of brokers.",
  },
  {
    id: "2",
    title: "Access to Verified Properties",
    description:
      "We provide you with exclusive access to a range of verified listings, ensuring that every property is authentic & trustworthy. Our secure platform facilities seamless transactions, giving you peace of mind.",
  },
  {
    id: "3",
    title: "Authenticity & Credibility",
    description:
      "Connect and collaborate with uthorized & Reputed Brokers from all across India. Negotiate deals and maximize your profit, while building lasting professional relationships within the real estate industry.",
  },
];

export const RealEstateBottomValues = [
  {
    title: "15 years",
    description: "Experience in real estate",
  },
  {
    title: "100%",
    description: "Covers all the locations in INDIA, from states to cities",
  },
  {
    title: "4.9",
    description: "Satisfied consumer using app & website",
  },
];

export const RealEstateImages = [
  {
    id: "0",
    image: "/images/pan-india-exposure.png",
  },
  { id: "1", image: "/images/direct-connectedness.png" },
  {
    id: "2",
    image: "/images/access-to-verified-properties.png",
  },
  {
    id: "3",
    image: "/images/authenticity-credibility.png",
  },
];
export const TestimonialsData = [
  {
    id: "1",
    writerName: "Mika",
    writerLocation: "India",
    description: "Amazing Property",
    stars: 4,
  },
  {
    id: "2",
    writerName: "Data",
    writerLocation: "Universe",
    description: "Property with Greenery",
    stars: 5,
  },
];
