"use client";

import React from "react";
import Stepper from "./stepper";
import Step1 from "./step-1";
import Step2 from "./step-2";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ONBOARDING_PARAM_NAME } from "../utils/constants";

const Onboarding = () => {
  const pathName = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const step = Number(searchParams.get(ONBOARDING_PARAM_NAME));

  if (!step || step > 2) {
    router.replace(pathName + `?${ONBOARDING_PARAM_NAME}=1`);
  }

  return (
    <>
      <div className="mb-3 flex justify-center bg-primary-2-100 px-10 py-[18px] text-center font-medium leading-[26px] text-primary-2-750 md:text-lg md:leading-7 lg:px-[80px] lg:py-5 lg:text-xl xl:leading-[30px] 2xl:px-[100px]">
        Please complete onboarding steps to continue to deer
      </div>

      <div className="p-5 md:p-10 xl:p-20 2xl:p-[100px]">
        <div className="flex flex-col items-center justify-between">
          {/* top main */}
          <div className="flex w-full flex-col items-center gap-10 md:gap-[50px] xl:gap-[70px] 2xl:gap-20">
            {/* stepper */}
            <Stepper currentStep={step}></Stepper>
            {/* main sction */}
            {step === 1 && <Step1 />}
            {step === 2 && <Step2 />}
          </div>
        </div>
      </div>
    </>
  );
};

export default Onboarding;
