import { XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

const Stepper = ({ currentStep }: { currentStep: number }) => {
  const router = useRouter();
  //   const { mutate: updatStatus } = api.user.updateOnBoardingStatus.useMutation();

  const handleUpdateStatus = () => {
    // updatStatus();
    router.push("/profile/feed");
  };

  return (
    <>
      {/* step indicator */}
      <div className="flex w-full flex-col items-center gap-3 xl:max-w-6xl 2xl:gap-[18px]">
        {/* step number */}
        <div className="flex w-full items-center justify-between">
          <p className="font-airbnb_w_md text-lg font-medium text-primary-2-800 md:text-xl 2xl:text-2xl">
            {currentStep}/2
          </p>
          <div onClick={handleUpdateStatus}>
            <XIcon className="size-[18px] cursor-pointer text-text-600 md:size-[30px]"></XIcon>
          </div>
        </div>
        {/* stepper */}
        <div className="flex h-1 w-full bg-primary-2-100 2xl:h-1.5">
          {Array.from({ length: 2 }).map((_, i) => {
            return (
              <React.Fragment key={i}>
                {currentStep > i && (
                  <div className="h-full basis-1/2 bg-primary-2-600"></div>
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default Stepper;
