import React, { useEffect, useState } from "react";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { api } from "~/trpc/react";
import GoogleAutocompleteInput from "@repo/ui/components/shared/google-autocomplete-input";
import Image from "next/image";
import { cn } from "@repo/ui/lib/utils";
import type { City } from "@repo/database";
import type { LocationPoint } from "../types";
import { CustomerOnboadingStep2Schema } from "@repo/validators";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter } from "next/navigation";

const Step2 = () => {
  const router = useRouter();
  const [key, setKey] = useState(0); // for explicit re-mount of input box
  const [selectedCity, setSelectedCity] = useState<City | null>(null);
  const [locationRestriction, setLocationRestriction] = useState<
    LocationPoint[]
  >([]);

  const form = useForm<z.infer<typeof CustomerOnboadingStep2Schema>>({
    resolver: zodResolver(CustomerOnboadingStep2Schema),
    defaultValues: {
      location: "",
    },
  });

  const { mutate: skipOnboarding } =
    api.onboarding.updateOnboardingStatus.useMutation();
  const { mutate: updateStep2 } = api.onboarding.step2.useMutation();
  const { data } = api.onboarding.getCitites.useQuery();
  const cities = data ?? [];

  useEffect(() => {
    if (!selectedCity) return;

    form.reset({
      location: "",
      latitude: "",
      longitude: "",
      cityId: selectedCity.id,
    });

    const cityMarkersLatLng = selectedCity.cityMarkersLatLng as LocationPoint[];

    setLocationRestriction(cityMarkersLatLng);

    setKey((prev) => prev + 1);
  }, [selectedCity, form]);

  const handleCitySelect = (city: City) => {
    setSelectedCity(city);
    form.setValue("cityId", city.id);
  };

  const onSubmit = (data: z.infer<typeof CustomerOnboadingStep2Schema>) => {
    if (!selectedCity) return toast.warning("Please select a city.");

    updateStep2(data, {
      onSuccess: (opts) => {
        toast.success(opts.message);
        router.push(`/`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  const handleSkip = () => {
    skipOnboarding(undefined, {
      onSuccess: () => {
        router.push("/");
      },
    });
  };

  console.log(form.getValues());

  return (
    <>
      <div className="flex w-full flex-col items-center gap-10 md:max-w-[638px] md:gap-[50px] xl:max-w-[814px] xl:gap-[70px] 2xl:max-w-[856px] 2xl:gap-20">
        {/* main */}
        <div className="flex w-full flex-col items-center">
          {/* heading */}
          <div className="flex flex-col items-center gap-1 xl:gap-2">
            <h1 className="bg-[linear-gradient(90deg,_#A30000_0%,_#F15F3A_100%)] bg-clip-text font-airbnb_w_blk text-2xl font-black text-transparent md:text-3xl xl:text-4xl 2xl:text-[40px] 2xl:leading-[48px]">
              Choose Your Location
            </h1>
            <h2 className="text-center font-airbnb_w_bk text-xs text-text-550 md:text-sm xl:text-lg 2xl:text-2xl">
              Choose your location to get customized suggestions
            </h2>
          </div>

          {/* cities */}
          {cities.length > 0 ? (
            <div className="mt-8 flex w-full cursor-pointer items-center gap-3 overflow-x-auto md:mt-10 xl:mt-[60px]">
              {cities.map((item) => (
                <div
                  className={cn(
                    "flex w-[120px] flex-col items-center justify-center gap-[3.78px] rounded-md border border-black px-2 py-1",
                    selectedCity === item && "rounded-md bg-secondary-2-700",
                  )}
                  onClick={() => handleCitySelect(item)}
                >
                  <Image
                    src="/images/city.jpg"
                    height={500}
                    width={500}
                    alt={item.name}
                    className={cn(
                      "size-[30px] rounded-full object-cover md:size-[50px] xl:size-[64px]",
                      selectedCity === item &&
                        "border-2 border-secondary-2-700 backdrop-blur-md",
                    )}
                  />
                  <p
                    className={cn(
                      "text-wrap text-center text-xs md:text-sm xl:text-lg",
                      selectedCity === item && "text-white",
                    )}
                  >
                    {item.name}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="mt-8 w-full text-sm font-semibold text-primary-700 md:mt-10 md:text-lg xl:mt-[60px] xl:text-xl">
              No Cities Found
            </p>
          )}

          {/* form */}
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="mt-3 flex w-full flex-col gap-10 md:mt-4 md:gap-[50px] xl:mt-5 xl:gap-[70px] 2xl:gap-20"
              key={selectedCity?.id}
            >
              <div className="flex flex-col gap-6 md:gap-[30px] xl:gap-10">
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="font-airbnb_w_md text-base font-medium text-text-600 md:text-lg xl:text-xl">
                        Your Location
                      </FormLabel>
                      <FormControl>
                        <GoogleAutocompleteInput
                          key={key}
                          className="mt-2"
                          placeholder="Enter your location"
                          isDisabled={selectedCity ? false : true}
                          onLocationSelect={(e) => {
                            field.onChange(e.fullAddress);
                            form.setValue("latitude", e.latitude);
                            form.setValue("longitude", e.longitude);
                          }}
                          showSearchIcon={true}
                          showAutoDetectLocationIcon={
                            selectedCity ? true : false
                          }
                          onUserLocationDetect={(e) => {
                            form.setValue("latitude", e.latitude.toString());
                            form.setValue("longitude", e.longitude.toString());
                            if (e.address?.display) {
                              form.setValue(
                                "location",
                                String(e.address.display),
                              );
                            }
                          }}
                          searchBounds={{
                            point1: locationRestriction[0],
                            point2: locationRestriction[1],
                            point3: locationRestriction[2],
                            point4: locationRestriction[3],
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-full justify-between">
                <div
                  onClick={handleSkip}
                  className="flex w-[100px] cursor-pointer items-center justify-center rounded-xl border border-text-400 bg-text-40 font-airbnb_w_md font-medium text-text-600 md:w-[120px] xl:w-40 xl:text-lg"
                >
                  Skip
                </div>
                <Button className="w-[100px] bg-secondary-2-700 font-airbnb_w_md font-medium text-white md:w-[120px] xl:w-40 xl:text-lg">
                  Continue
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </>
  );
};

export default Step2;
