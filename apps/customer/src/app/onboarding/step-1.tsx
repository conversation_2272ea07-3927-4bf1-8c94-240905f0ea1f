import { Button } from "@repo/ui/components/ui/button";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";
import React, { useState } from "react";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/ui/radio-group";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { OnboadingCards, ONBOARDING_PARAM_NAME } from "../utils/constants";
import { CustomerOnboadingStep1Schema } from "@repo/validators";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter } from "next/navigation";

const Step1 = () => {
  const router = useRouter();
  const [selectedCard, setSelectedCard] = useState<number>(0);

  const form = useForm<z.infer<typeof CustomerOnboadingStep1Schema>>({
    resolver: zodResolver(CustomerOnboadingStep1Schema),
  });

  const { mutate: skipOnboarding } =
    api.onboarding.updateOnboardingStatus.useMutation();
  const { mutate: updateStep1 } = api.onboarding.step1.useMutation();

  const onSubmit = (data: z.infer<typeof CustomerOnboadingStep1Schema>) => {
    updateStep1(data, {
      onSuccess: (opts) => {
        toast.success(opts.message);
        router.push(`/onboarding?${ONBOARDING_PARAM_NAME}=2`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  const handleSkip = () => {
    skipOnboarding(undefined, {
      onSuccess: () => {
        router.push("/");
      },
    });
  };

  const handleClick = (id: number) => {
    setSelectedCard(id);
  };

  return (
    <>
      <div className="flex w-full flex-col items-center gap-10 md:gap-[50px] xl:gap-[70px] 2xl:gap-20">
        {/* main */}
        <div className="flex w-full flex-col items-center gap-8 md:gap-10 xl:gap-[60px]">
          {/* heading */}
          <h1 className="bg-[linear-gradient(90deg,_#A30000_0%,_#F15F3A_100%)] bg-clip-text font-airbnb_w_blk text-2xl font-black text-transparent md:text-3xl xl:text-4xl 2xl:text-[40px] 2xl:leading-[48px]">
            Your Interest
          </h1>
          {/* main content */}
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex w-full flex-col items-center gap-10 md:gap-[50px] xl:gap-[70px] 2xl:gap-20"
            >
              <FormField
                control={form.control}
                name="onboardingPreference"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex w-full flex-col items-center justify-center gap-6 md:flex-row xl:gap-10"
                      >
                        {OnboadingCards.map((card) => {
                          return (
                            <FormItem className="">
                              <FormControl>
                                <RadioGroupItem
                                  value={card.for}
                                  hidden
                                  id={card.for}
                                />
                              </FormControl>
                              <FormLabel htmlFor={card.for}>
                                <div
                                  key={card.id}
                                  className={cn(
                                    "flex w-[200px] cursor-pointer flex-col items-center gap-5 rounded-3xl border-2 border-secondary-2-200 p-5 backdrop-blur-[20px] transition-all duration-300 md:gap-6 md:p-6 xl:w-[340px] xl:gap-8 2xl:p-8",
                                    {
                                      "border-secondary-2-700 bg-secondary-2-100":
                                        selectedCard === card.id,
                                    },
                                  )}
                                  onClick={() => handleClick(card.id)}
                                >
                                  <div className="relative aspect-square w-[60px] rounded-xl bg-white xl:w-[112px]">
                                    <Image
                                      src={card.image}
                                      alt={card.alt}
                                      fill
                                      className="relative object-cover p-2 xl:p-4"
                                    ></Image>
                                  </div>

                                  <div className="flex flex-col items-center gap-2">
                                    <h2
                                      className={cn(
                                        "text-center font-airbnb_w_xbd text-sm font-extrabold text-text-600 transition-all duration-300 xl:text-2xl 2xl:text-3xl",
                                        {
                                          "text-secondary-2-700":
                                            selectedCard === card.id,
                                        },
                                      )}
                                    >
                                      {card.title}
                                    </h2>
                                    <p className="text-center font-airbnb_w_bk text-xs text-text-550 xl:text-base 2xl:text-lg">
                                      {card.text}
                                    </p>
                                  </div>
                                </div>{" "}
                              </FormLabel>
                            </FormItem>
                          );
                        })}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex w-full justify-between">
                <div
                  onClick={handleSkip}
                  className="flex w-[100px] cursor-pointer items-center justify-center rounded-xl border border-text-400 bg-text-40 font-airbnb_w_md font-medium text-text-600 md:w-[120px] xl:w-40 xl:text-lg"
                >
                  Skip
                </div>
                <Button
                  className="w-[100px] bg-secondary-2-700 font-airbnb_w_md font-medium text-white md:w-[120px] xl:w-40 xl:text-lg"
                  type="submit"
                >
                  Continue
                </Button>
              </div>
            </form>
          </Form>
        </div>
        {/* buttons */}
      </div>
    </>
  );
};

export default Step1;
