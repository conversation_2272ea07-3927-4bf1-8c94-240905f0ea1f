import Image from "next/image";
import Link from "next/link";

const terms = [
  { name: "Use Caution", id: "use-caution" },
  { name: "How can real estate fraud be prevented?", id: "real-estate-fraud" },
  { name: "Investor/Buyer", id: "investor-buyer" },
  { name: "<PERSON><PERSON>", id: "renter" },
  { name: "Disclaimer", id: "disclaimer" },
];

const SafetyGuide = () => {
  return (
    <>
      <div className="container max-w-full bg-[#FFF6F4]">
        <div className="flex flex-col items-center gap-2.5 py-9 md:gap-3 md:py-[50px] lg:gap-4 lg:py-[55px] xl:py-[65px] 2xl:gap-5 2xl:py-[70px]">
          <Link
            href="/"
            className="font-airbnb_w_md text-sm font-medium text-primary-2-750 lg:text-base 2xl:text-lg"
          >
            /Back to home page
          </Link>
          <div className="flex flex-col items-center gap-1 md:gap-1.5 lg:gap-2 2xl:gap-3">
            <div className="flex flex-row items-center gap-2.5 md:gap-3 lg:gap-[14px] xl:gap-4 2xl:gap-[18px]">
              <div className="relative aspect-square w-[30px] md:w-9 lg:w-[40px] xl:w-[48px] 2xl:w-[60px]">
                <Image
                  src="/icons/safety-guide.png"
                  alt="privacy-policy"
                  fill
                />
              </div>
              <div className="font-airbnb_w_xbd text-3xl font-extrabold text-secondary-2-700 md:text-4xl lg:text-[44px] lg:leading-[56px] xl:text-[56px] xl:leading-[69px] 2xl:text-6xl 2xl:leading-[80px]">
                Safety Guide
              </div>
            </div>
            <p className="text-center font-airbnb_w_bk text-base font-normal text-[#1A1A1A] md:text-lg lg:text-xl 2xl:text-2xl">
              Last updated on 10 March 2025
            </p>
          </div>
        </div>
      </div>
      <div className="container flex max-w-full flex-col gap-7 py-9 md:flex-row md:items-start md:gap-9 md:py-10 lg:gap-10 lg:py-[45px] xl:gap-[60px] xl:py-[50px] 2xl:gap-[65px] 2xl:py-[60px]">
        <div className="justify-center font-airbnb_w_lt text-[18px] font-light leading-[30px] text-text-700 md:w-3/5 xl:text-[20px] xl:leading-[34px] 2xl:text-[22px] 2xl:leading-9">
          <section
            id="use-caution"
            className="mb-8 scroll-mt-24 font-airbnb_w_bk font-normal text-text-500"
          >
            <h2 className="mb-4 font-airbnb_w_md text-2xl font-medium text-secondary-2-750 xl:text-[24px] 2xl:text-[26px]">
              Use Caution
            </h2>
            <p className="mb-4">
              A user should use his or her best judgement while evaluating the
              legitimacy of properties and owners. It is thus recommended that a
              user independently confirm the veracity of the claims being made,
              as they may or may not be accurate. In order to help users avoid
              dishonesty, we have created a checklist. Before buying or renting
              a home, we advise all of our users to review the checklist (which
              is shown below).
            </p>
          </section>

          <section
            id="real-estate-fraud"
            className="mb-8 scroll-mt-24 font-airbnb_w_bk font-normal text-text-500"
          >
            <h2 className="mb-4 font-airbnb_w_md text-2xl font-medium text-secondary-2-750 xl:text-[24px] 2xl:text-[26px]">
              How can real estate fraud be prevented?
            </h2>
            <ul className="mb-4 list-disc space-y-2 pl-6">
              <li>
                If you haven't met a "supposed landlord/owner" in person, don't
                pay money to them online via UPI, net banking, debit/credit
                cards, etc.
              </li>
              <li>
                We advise against paying any fees associated with a property
                visit, gate pass, reservation, etc. over the phone.
              </li>
              <li>
                Make sure you meet the owner or have a web session with him if a
                broker is showing you the property.
              </li>
              <li>Don't give the broker any portion of the rent. </li>
              <li>Watch out for contested land titles. </li>
              <li>
                Real estate ventures that promise large profits with little to
                no risk (due to infra construction) should invite caution.
              </li>
              <li>
                Make a due diligence visit to the property to inspect its
                exterior and interior conditions.{" "}
              </li>
              <li>How Can Real Estate Fraud Be Prevented?</li>
              <li>
                Look for homes with at least three or four crisp pictures of the
                kitchen, bathroom and bedrooms.
              </li>
              <li>Get to know the area before relocating there. </li>
              <li>
                Verify that the person showing you the home is the real owner by
                speaking with some of the neighbours.{" "}
              </li>
            </ul>
          </section>

          <section
            id="investor-buyer"
            className="mb-8 scroll-mt-24 font-airbnb_w_bk font-normal text-text-500"
          >
            <h2 className="mb-4 font-airbnb_w_md text-2xl font-medium text-secondary-2-750 xl:text-[24px] 2xl:text-[26px]">
              Investor/Buyer
            </h2>
            <p className="mb-4 font-airbnb_w_bk font-normal">
              Since{" "}
              <Link href="/" target="_blank">
                mydeer.net
              </Link>{" "}
              is an advertising platform, it cannot and will not attest to the
              authenticity of the content on its website. The buyer should
              verify the authenticity of any property or project, as well as its
              title, built-up area (in standardised units), and suitability for
              purchase in a form and manner deemed appropriate at his or her
              COST. The sponsor uploads a lot of stuff, and we don't filter or
              check it. In the event of a complaint, corrective action will be
              taken.{" "}
              <b className="font-airbnb_w_md font-medium text-text-550">
                Some things to consider are:
              </b>
            </p>
            <ul className="mb-4 list-disc space-y-2 pl-6 font-airbnb_w_bk font-normal">
              <li>
                Verify the credentials of developers and owners by doing a
                thorough check of ownership.
              </li>
              <li>
                Make a personal visit to the project site or property to confirm
                that the developer's or broker's claim is accurate and that the
                property isn’t “imaginary."
              </li>
              <li>
                Select a constructor with a solid track record; it might be
                useful to look into the promoters’ prior performance.
              </li>
              <li>
                Watch out for phoney title paperwork. It's possible that loans
                were secured against properties that are under dispute or have
                been unoccupied for an extended period of time.
              </li>
              <li>
                Before engaging in a real estate transaction, it is usually
                advised to perform appropriate due diligence on the property.
              </li>
              <li>
                Remember that the Floor Space Index (FSI) and Floor Area Ratio
                (FAR) do not include open spaces such as the lobby, park,
                terrace, pool, gym or lift/elevator.
              </li>
              <li>
                The provisions of the{" "}
                <strong className="font-airbnb_w_md font-medium">
                  Real Estate (Regulation and Development) Act of 2016
                </strong>{" "}
                and how they relate to the project.
              </li>
            </ul>
          </section>

          <section id="renter" className="mb-8 scroll-mt-24">
            <h2 className="mb-4 font-airbnb_w_md text-2xl font-medium text-secondary-2-750 xl:text-[24px] 2xl:text-[26px]">
              Renter
            </h2>
            <p className="mb-4 font-airbnb_w_md font-medium text-text-550">
              Some things to consider are:
            </p>
            <ul className="mb-4 list-disc space-y-2 pl-6 font-airbnb_w_bk font-normal text-text-500">
              <li>
                Visiting the lodging or property and getting to know the
                landlord.
              </li>
              <li>
                We firmly advise against paying any fees associated with
                property visits, gate entrance fees, reservations, etc.
              </li>
              <li>
                Inspect every room, including the kitchen and bathroom, for
                plumbing issues, such as seepages and leaks from fixtures,
                blocked drains and pipes.
              </li>
              <li>
                Before paying, find out how much the security deposit is at a
                certain location. People who are dishonest may demand excessive
                sums of money.
              </li>
              <li>Verify safety, security and access.</li>
              <li>Make sure that every region is standardised units.</li>
            </ul>
          </section>

          <section
            id="disclaimer"
            className="mb-8 scroll-mt-24 font-airbnb_w_bk font-normal text-text-500"
          >
            <h2 className="mb-4 font-airbnb_w_md text-2xl font-medium text-secondary-2-750 xl:text-[24px] 2xl:text-[26px]">
              Disclaimer
            </h2>
            <p className="mb-4">
              The platform{" "}
              <Link href="/" target="_blank">
                mydeer.net
              </Link>{" "}
              serves as a conduit for those with overlapping interests in real
              estate transactions, specifically the buyer/tenant and
              owner/broker.
            </p>
            <p className="mb-4">
              Before engaging in any real estate transactions, users are highly
              encouraged to get independent third-party verifications, whether
              or not they are listed as verified, as{" "}
              <Link href="/" target="_blank">
                mydeer.net
              </Link>{" "}
              is only a preliminary means of interaction and information
              exchange. It is the user's responsibility to locate a legitimate
              property, whether for purchase or renting.
            </p>
            <p className="mb-4">
              ​The terms and conditions governing the usage of{" "}
              <Link href="/" target="_blank">
                mydeer.net
              </Link>{" "}
              should be reviewed for further details.
            </p>
          </section>
        </div>
        {/* sticky navigation*/}
        <div className="sticky top-28 hidden max-h-[500px] max-w-full flex-col gap-4 overflow-y-auto rounded-lg bg-text-30 px-3 py-6 shadow-[0_1px_4px_1px_rgba(148,148,148,0.06)] md:flex md:w-[38%] lg:gap-6 xl:gap-8 xl:px-6 xl:py-8 2xl:p-10">
          <div className="font-airbnb_w_md text-sm font-medium text-text-300 lg:text-base 2xl:text-lg">
            On This Page
          </div>

          <div className="space-y-2 lg:space-y-3 2xl:space-y-4">
            {terms.map((term) => (
              <div
                key={term.id}
                className="font-airbnb_w_bk text-base font-normal text-text-400 lg:text-lg 2xl:text-xl"
              >
                <Link href={`#${term.id}`}>{term.name}</Link>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default SafetyGuide;
