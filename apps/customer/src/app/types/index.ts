import type { Prisma } from "@repo/database";

export type TStatus = "loading" | "error" | "idle";

export type TPropertyCategories = {
  id: string;
  name: string;
};

export type Points = {
  lat: number;
  lng: number;
};
export type LocationPoint = {
  lat: number;
  lng: number;
};

export type PropertiesWithMediaIncluded = Prisma.PropertyGetPayload<{
  include: {
    utilities: true;
    amenities: true;
    user: {
      include: {
        company: true;
        companyDetails: true;
      };
    };
    comments: {
      include: {
        user: true;
      };
    };
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
          };
        };
      };
    };
  };
}>;

export type TopBarProps = {
  points: Points[];
  properties: PropertiesWithMediaIncluded[] | [];
  className?: string;
};

export type ForSaleFilterProps = {
  selectedValue: string | undefined;
  setSelectedValue: (v: string | undefined) => void;
};

export type AllHomeTypeFilterProps = {
  propertyCategoryId: string | null;
  selectedValues: string[];
  setSelectedValues: (v: string[]) => void;
};

export type PropertyCategoryFilterProps = {
  selectedValue: string | null;
  setSelectedValue: (v: string) => void;
};

export type PropertyListResultProps = {
  properties: PropertiesWithMediaIncluded[];
};

export type PaginationBarProps = {
  totalPages: number;
  selectedPage: number;
  setSelectedPage: (v: string) => void;
};

export type TConversationWithAgentCustomerMessages =
  Prisma.CustomerAgentConnectionsGetPayload<{
    include: {
      agent: {
        select: {
          id: true;
          name: true;
          filePublicUrl: true;
          cloudinaryProfileImageUrl: true;
          isOnline: true;
          rating: true;
        };
      };
      messages: {
        orderBy: {
          createdAt: "desc";
        };
        take: 1;
      };
      rating: true;
    };
  }>;

export type Rating = {
  agentStars: number;
  agentMessage: string;
  propertyStars: number;
  propertyMessage: string;
};

export type TAgentProfileWithExtraDetails = Prisma.UserGetPayload<{
  include: {
    properties: {
      include: {
        areaUnit: true;
        utilities: true;
        amenities: true;
        user: {
          include: {
            company: true;
            companyDetails: true;
          };
        };
        customerFavourites: true;
        comments: {
          include: {
            user: true;
          };
        };
        mediaSections: {
          include: {
            media: {
              select: {
                id: true;
                fileKey: true;
                filePublicUrl: true;
              };
            };
          };
        };
      };
    };
    company: true;
    operationArea: true;
  };
}>;
export type TAgentProfile = Prisma.UserGetPayload<{
  include: {
    properties: {
      include: {
        areaUnit: true;
        utilities: true;
        amenities: true;
        user: {
          include: {
            company: true;
            companyDetails: true;
          };
        };
        // customerFavourites: true;
        comments: {
          include: {
            user: true;
          };
        };
        mediaSections: {
          include: {
            media: {
              select: {
                id: true;
                fileKey: true;
                filePublicUrl: true;
              };
            };
          };
        };
      };
    };
    company: true;
    operationArea: true;
  };
}>;

export type TCustomerProfile = Prisma.CustomerGetPayload<{
  include: {
    connections: true;
  };
}>;

export type TAgentSoldPropertiesWithExtraDetails = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      include: {
        company: true;
        companyDetails: true;
      };
    };
    comments: {
      include: {
        user: true;
      };
    };
    customerFavourites: true;
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
            cloudinaryUrl: true;
            cloudinaryId: true;
            mediaType: true;
          };
        };
      };
    };
  };
}>;

export type TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded =
  Prisma.PropertyGetPayload<{
    include: {
      areaUnit: true;
      utilities: true;
      amenities: true;
      PropertyCategory: true;
      user: {
        select: {
          id: true;
          company: true;
          companyDetails: true;
        };
      };
      customerFavourites: true;
      mediaSections: {
        include: {
          media: {
            select: {
              id: true;
              fileKey: true;
              filePublicUrl: true;
              cloudinaryUrl: true;
              mediaType: true;
            };
          };
        };
      };
    };
  }>;

export type TAgent = Prisma.UserGetPayload<{
  select: {
    name: true;
    id: true;
    createdAt: true;
    verifiedAgent: true;
    experience: true;
    propertiesSold: true;
    rating: true;
    filePublicUrl: true;
    cloudinaryProfileImageUrl: true;
  };
}>;

export type AgentDetails = Prisma.UserGetPayload<{
  select: {
    name: true;
    id: true;
    verifiedAgent: true;
    experience: true;
    propertiesSold: true;
    rating: true;
    filePublicUrl: true;
    cloudinaryProfileImageUrl: true;
    latitude: true;
    longitude: true;
    companyDetails: true;
    createdAt: true;
  };
}>;

export type AgentListResultProps = {
  setPoints: (v: Points[]) => void;
  setAgents: (v: AgentDetails[] | []) => void;
  setTotalResults: (v: number) => void;
};
