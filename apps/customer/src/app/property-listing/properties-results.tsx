"use client";

import React from "react";
import PropertyListResult from "./properties-list-result";

import { useRouter, useSearchParams } from "next/navigation";
import type { PropertiesWithMediaIncluded } from "./page";
import PaginationBar from "../components/shared/pagination-bar";
import PropertyCardSkeleton from "@repo/ui/components/skeleton/property-card-skeleton";

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerTitle,
  DrawerTrigger,
} from "@repo/ui/components/ui/drawer";
import { ChevronUp, XIcon } from "lucide-react";
import { useMediaQuery } from "@uidotdev/usehooks";
import { cn } from "@repo/ui/lib/utils";

type PropertiesResultsProps = {
  isLoading: boolean;
  totalResults: number;
  totalPages: number;
  properties: PropertiesWithMediaIncluded[] | [];
};

const PropertiesResults = ({ ...props }: PropertiesResultsProps) => {
  const isSmallDevice = useMediaQuery("only screen and (max-width : 768px)");
  const isMediumDevice = useMediaQuery(
    "only screen and (min-width : 769px) and (max-width : 1023px)",
  );

  return (
    <>
      {isSmallDevice || isMediumDevice ? (
        <ResultsWithBottomDrawer {...props} />
      ) : (
        <ResultsWithoutDrawer {...props} />
      )}
    </>
  );
};

export default React.memo(PropertiesResults);

const ResultsWithBottomDrawer = ({
  isLoading,
  totalResults,
  totalPages,
  properties,
}: PropertiesResultsProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const open = searchParams.get("sheet");

  const handlePageChange = (pageNumber: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", pageNumber);
    params.set("sheet", "open");
    router.push(`?${params}`);
  };

  const handleOpenChange = (val: boolean) => {
    if (val) {
      const params = new URLSearchParams(searchParams);
      params.set("sheet", "open");
      router.push(`?${params.toString()}`);
    } else {
      const params = new URLSearchParams(searchParams);
      params.delete("sheet");
      router.push(`?${params.toString()}`);
    }
  };

  return (
    <>
      {isLoading || totalResults === 0 ? null : (
        <Drawer open={!!open} onOpenChange={handleOpenChange}>
          <DrawerTrigger className="absolute bottom-10 left-1/2 z-50 flex -translate-x-1/2 transform items-center gap-2 text-nowrap rounded-full bg-secondary-2-800 px-6 py-3 text-white shadow-lg md:bottom-32 lg:hidden">
            <ChevronUp className="size-5" /> View {totalResults} Properties
          </DrawerTrigger>
          <DrawerContent>
            <div className="flex h-[85vh] flex-col">
              <div className="border-b bg-white px-4 py-3">
                <div className="flex items-center justify-between">
                  <DrawerTitle>{totalResults} Properties Found</DrawerTitle>
                  <DrawerClose className="rounded-full p-2 hover:bg-gray-100">
                    <XIcon className="h-5 w-5" />
                  </DrawerClose>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto">
                <div className="px-4 py-4">
                  {isLoading ? (
                    <div className="grid gap-4 md:grid-cols-2">
                      {Array.from({ length: 4 }).map((_, idx) => (
                        <PropertyCardSkeleton key={idx} />
                      ))}
                    </div>
                  ) : properties.length > 0 ? (
                    <PropertyListResult properties={properties} />
                  ) : (
                    <div className="flex h-40 items-center justify-center text-center text-lg font-medium text-gray-500">
                      No properties found in this area
                    </div>
                  )}
                </div>
              </div>

              {/* Footer - fixed at bottom */}
              <div className="border-t bg-white px-4 py-3">
                <PaginationBar
                  selectedPage={Number(searchParams.get("page") ?? 1)}
                  setSelectedPage={handlePageChange}
                  totalPages={totalPages}
                />
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
};

const ResultsWithoutDrawer = ({
  isLoading,
  totalResults,
  totalPages,
  properties,
}: PropertiesResultsProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const isExtraExtraLargeDevice = useMediaQuery(
    "only screen and (min-width : 2560px)",
  );

  const handlePageChange = (pageNumber: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", pageNumber);
    router.push(`?${params}`);
  };

  return (
    <div className="flex w-full flex-col justify-center gap-3 lg:min-w-[375px] xl:min-w-[750x] 2xl:justify-start">
      {/* Sort by and Results count bar with a title */}
      <div className="flex flex-col py-2 lg:py-[10px]">
        {/*<p className="text-lg font-medium text-text-600 lg:text-xl 2xl:text-2xl">*/}
        {/*  Real Estate Home*/}
        {/*</p>*/}
        <div className="flex items-center justify-between">
          <p className="font-airbnb_w_bk text-sm text-text-600 md:px-5 xl:px-2 2xl:px-10 2xl:text-lg">
            Total {totalResults} results
          </p>
        </div>
      </div>

      {isLoading ? (
        <div
          className={cn(
            "grid gap-5 md:grid-cols-2 md:px-5 lg:grid-cols-1 xl:grid-cols-2",
            isExtraExtraLargeDevice && "2xl:grid-cols-3",
          )}
        >
          {Array.from({ length: 20 }).map((_, idx) => (
            <PropertyCardSkeleton className="min-w-min max-w-full" key={idx} />
          ))}
        </div>
      ) : properties.length > 0 ? (
        <PropertyListResult properties={properties} />
      ) : (
        <div className="flex min-h-[70vh] w-full items-center justify-center text-2xl font-bold text-primary-600">
          No property found.
        </div>
      )}
      <PaginationBar
        selectedPage={Number(searchParams.get("page") ?? 1)}
        setSelectedPage={handlePageChange}
        totalPages={totalPages}
      />
    </div>
  );
};
