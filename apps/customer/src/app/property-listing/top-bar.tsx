"use client";

import React, { Suspense, useEffect, useState } from "react";

import AllFilters from "./all-filters";
import type { Points, PropertiesWithMediaIncluded } from "./page";
import GoogleMap from "@repo/ui/components/shared/google-map";
import ContactCheckResponsesButton from "../components/shared/contact-checkresponses-button";
import EditPropertyButton from "../components/shared/edit-property-button";
import LikePropertyButton from "../components/shared/like-property-button";
import SharePropertyButton from "../components/shared/share-property-button";

import { useSession } from "next-auth/react";
import { Loader2 } from "lucide-react";
import useUserLocation from "../hooks/use-user-location";

type TopBarProps = {
  points: Points[];
  properties: PropertiesWithMediaIncluded[] | [];
  className?: string;
  onBoundsChanged?: (bounds: {
    ne: { lat: number; lng: number };
    sw: { lat: number; lng: number };
  }) => void;
  isLoading?: boolean;
  loadingMessage?: string;
};

const TopBar = ({
  points,
  properties,
  onBoundsChanged,
  isLoading,
  loadingMessage,
}: TopBarProps) => {
  const [selectedProperty, setSelectedProperty] =
    useState<PropertiesWithMediaIncluded | null>(null);
  const { data: userLoginInfo } = useSession();
  const { lat, lng } = useUserLocation();
  const [userLocation, setUserLocation] = useState<
    { lat: number; lng: number } | undefined
  >(undefined);

  useEffect(() => {
    if (lat && lng) {
      setUserLocation({ lat, lng });
    }
  }, [lat, lng]);

  return (
    <Suspense fallback={null}>
      <div className="relative flex flex-col gap-3 py-[10px] sm:gap-4 sm:py-3 2xl:py-4">
        <div className="h-[85vh] w-full md:h-[75vh] lg:hidden">
          <GoogleMap
            points={points}
            properties={properties}
            mapClassName="max-w-full"
            className="h-full w-full"
            userLocation={userLocation}
            selectedProperty={selectedProperty}
            // @ts-expect-error: This is just an types issue here, becoz if i change the type in google-map it will change for all and in customer app 1 extra field i am fetching which is customerFavourites so that's the issue which will not break the web at any point.
            setSelectedProperty={setSelectedProperty}
            locationIcon="/icons/location.svg"
            contactOrCheckResponsesButton={
              selectedProperty ? (
                <ContactCheckResponsesButton
                  propertyId={selectedProperty.id}
                  propertyOwnerId={selectedProperty.user.id}
                  soldAt={selectedProperty.soldAt}
                />
              ) : null
            }
            editPropertyButton={
              selectedProperty ? (
                <EditPropertyButton propertyId={selectedProperty.id} />
              ) : null
            }
            likePropertyButton={
              selectedProperty ? (
                <LikePropertyButton
                  propertyId={selectedProperty.id}
                  isPropertyLiked={true}
                />
              ) : null
            }
            sharePropertyButton={
              selectedProperty ? (
                <SharePropertyButton propertyId={selectedProperty.id} />
              ) : null
            }
            userId={userLoginInfo?.user?.id}
            onBoundsChanged={onBoundsChanged}
          />

          {isLoading && (
            <div className="absolute left-1/2 top-24 z-50 -translate-x-1/2 rounded-md bg-black/80 px-2 py-2 text-xs text-white sm:px-4 sm:text-sm">
              <div className="flex items-center justify-center gap-2">
                <Loader2 className="h-4 w-4 min-w-4 animate-spin" />
                <span className="text-xs md:text-sm">
                  Looking for properties...
                </span>
              </div>
            </div>
          )}

          {loadingMessage && (
            <div className="absolute left-1/2 top-24 z-50 -translate-x-1/2 rounded-md bg-black/80 px-4 py-2 text-xs text-white md:text-sm">
              {loadingMessage}
            </div>
          )}
        </div>

        <AllFilters />
      </div>
    </Suspense>
  );
};

export default React.memo(TopBar);
