import React from "react";
import { Label } from "@repo/ui/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/ui/radio-group";
import type { ForSaleFilterProps } from "../types";

const Tags = ["SALE", "RENT"];

const ForSaleFilter = ({
  selectedValue,
  setSelectedValue,
}: ForSaleFilterProps) => {
  return (
    <div>
      <RadioGroup defaultValue={selectedValue} onValueChange={setSelectedValue}>
        {Tags.map((item, idx) => (
          <div
            className="flex cursor-pointer items-center gap-3 py-2"
            key={idx}
          >
            <RadioGroupItem
              value={item}
              id={item}
              className="peer data-[state=checked]:text-secondary-2-750"
            />
            <Label
              htmlFor={item}
              className="cursor-pointer capitalize text-text-500 peer-data-[state=checked]:text-secondary-2-750"
            >
              {item.toLowerCase()}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default ForSaleFilter;
