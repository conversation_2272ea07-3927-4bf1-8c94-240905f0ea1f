import React from "react";
import { Label } from "@repo/ui/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/ui/radio-group";
import type { PropertyCategoryFilterProps } from "../types";
import { api } from "~/trpc/react";
import Loading from "../components/shared/loading";

// const TAGS = ["RESIDENTIAL", "COMMERCIAL"] as const;

const PropertyCategoryFilter = ({
  selectedValue,
  setSelectedValue,
}: PropertyCategoryFilterProps) => {
  const { data: propertyCategories, isLoading } =
    api.property.getPropertyCategories.useQuery();

  return (
    <div>
      <RadioGroup
        className="flex flex-col"
        defaultValue={selectedValue ?? ""}
        onValueChange={setSelectedValue}
      >
        {isLoading ? (
          <Loading></Loading>
        ) : (
          propertyCategories?.length &&
          propertyCategories.map((item) => (
            <div
              className="flex cursor-pointer items-center gap-3 py-2"
              key={item.id}
            >
              <RadioGroupItem
                value={item.id}
                id={item.id}
                className="peer data-[state=checked]:text-secondary-2-750"
              />
              <Label
                htmlFor={item.name}
                className="cursor-pointer capitalize peer-data-[state=checked]:text-secondary-2-750 text-text-500"
              >
                {item.name}
              </Label>
            </div>
          ))
        )}
      </RadioGroup>
    </div>
  );
};

export default PropertyCategoryFilter;
