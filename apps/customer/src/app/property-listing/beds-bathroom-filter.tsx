import { cn } from "@repo/ui/lib/utils";
import React from "react";

type BedsBathroomFiltersProps = {
  numberOfBeds: string | null;
  numberOfBathrooms: string | null;
  setNumberOfBeds: (v: string | null) => void;
  setNumberOfBathrooms: (v: string | null) => void;
};

const BedsBathroomFilters = ({
  numberOfBathrooms,
  numberOfBeds,
  setNumberOfBathrooms,
  setNumberOfBeds,
}: BedsBathroomFiltersProps) => {
  return (
    <div className="flex flex-col gap-3 space-y-4">
      <div>
        <p className="font-semibold text-text-500">Number of Bedrooms</p>
        <ul className="flex items-center justify-center rounded-sm border-2 border-primary-500 overflow-hidden">
          <li
            className={cn(
              "flex w-full cursor-pointer items-center justify-center  border-r border-primary-500 p-2 xs:p-2",
              numberOfBeds === "any" && "bg-primary-600 text-white",
            )}
            onClick={() => setNumberOfBeds("any")}
          >
            Any
          </li>
          {Array.from({ length: 5 }).map((_, idx) => (
            <li
              key={idx}
              className={cn(
                "flex w-full cursor-pointer items-center justify-center border-r border-primary-500 p-2 xs:p-2",
                numberOfBeds === String(idx + 1) && "bg-primary-600 text-white",
                idx===4 && "border-r-0"
              )}
              onClick={() => setNumberOfBeds(String(idx + 1))}
            >
              {idx + 1}+
            </li>
          ))}
        </ul>
      </div>
      <div>
        <p className="font-semibold text-text-500">Number of Bathrooms</p>
        <ul className="flex items-center justify-center rounded-sm border-2 border-primary-500 overflow-hidden">
          <li
            className={cn(
              "flex w-full cursor-pointer items-center justify-center  border-r border-primary-500 p-2 xs:p-2",
              numberOfBathrooms === "any" && "bg-primary-600 text-white",
            )}
            onClick={() => setNumberOfBathrooms("any")}
          >
            Any
          </li>
          {Array.from({ length: 5 }).map((_, idx) => (
            <li
              key={idx}
              className={cn(
                "flex w-full cursor-pointer items-center justify-center border-r border-r-primary-500 p-2 xs:p-2",
                numberOfBathrooms === String(idx + 1) &&
                  "bg-primary-600 text-white",
                  idx===4 && "border-r-0"
              )}
              onClick={() => setNumberOfBathrooms(String(idx + 1))}
            >
              {idx + 1}+
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default BedsBathroomFilters;
