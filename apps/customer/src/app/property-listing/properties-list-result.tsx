import React from "react";
import PropertyCard from "@repo/ui/components/shared/property-card";
import { useSession } from "next-auth/react";
import EditPropertyButton from "../components/shared/edit-property-button";
import LikePropertyButton from "../components/shared/like-property-button";
import SharePropertyButton from "../components/shared/share-property-button";
import ContactAgentButton from "../components/shared/contact-agent-button";
import { api } from "~/trpc/react";
import type { PropertiesWithMediaIncluded } from "./page";
import { useMediaQuery } from "@uidotdev/usehooks";
import { cn } from "@repo/ui/lib/utils";

type PropertyListResultProps = {
  properties: PropertiesWithMediaIncluded[];
};

const PropertyListResult = ({ properties }: PropertyListResultProps) => {
  const { data: userLoginInfo } = useSession();
  const customerDetail = api.user.getProfile.useQuery();
  const connectdAgentId = customerDetail.data?.connections[0]?.agentId;
  const isExtraExtraLargeDevice = useMediaQuery(
    "only screen and (min-width : 2560px)",
  );

  return (
    <div
      className={cn(
        "grid gap-5 md:grid-cols-2 md:px-5 lg:grid-cols-1 xl:grid-cols-2",
        isExtraExtraLargeDevice && "2xl:grid-cols-3",
      )}
    >
      {properties.map((item) => (
        <div key={item.id} className="w-full lg:w-auto">
          <PropertyCard
            locationIcon="/icons/location.svg"
            id={item.id}
            propertyOwnerId={item.user.id}
            key={item.id}
            className="min-w-min max-w-full"
            property={item}
            userId={userLoginInfo?.user?.id}
            contactOrCheckResponsesButton={
              // <ContactCheckResponsesButton
              //   soldAt={item.soldAt}
              //   propertyOwnerId={item.user.id}
              //   propertyId={item.id}
              // />
              <ContactAgentButton
                agentId={item.user.id}
                connectedAgentId={connectdAgentId ?? ""}
              />
            }
            editPropertyButton={<EditPropertyButton propertyId={item.id} />}
            likePropertyButton={
              <LikePropertyButton
                propertyId={item.id}
                isPropertyLiked={!!item.customerFavourites.length}
              />
            }
            sharePropertyButton={<SharePropertyButton propertyId={item.id} />}
          />
        </div>
      ))}
    </div>
  );
};

export default React.memo(PropertyListResult);
