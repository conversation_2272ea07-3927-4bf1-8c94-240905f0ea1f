"use client";

import React, { useEffect, useState } from "react";
import TopBar from "./top-bar";
import PropertiesResults from "./properties-results";
import type {
  FacingEnum,
  FurnishingEnum,
  PossessionStateEnum,
  Prisma,
  PropertyForEnum,
  PropertyStateEnum,
} from "@repo/database";
import GoogleMap from "@repo/ui/components/shared/google-map";
import EditPropertyButton from "../components/shared/edit-property-button";
import LikePropertyButton from "../components/shared/like-property-button";
import SharePropertyButton from "../components/shared/share-property-button";
import { useSession } from "next-auth/react";
import ContactAgentButton from "../components/shared/contact-agent-button";
import { api } from "~/trpc/react";
import { Loader2 } from "lucide-react";
import { useSearchParams } from "next/navigation";
import useUserLocation from "../hooks/use-user-location";

export type Points = {
  lat: number;
  lng: number;
};

export type PropertiesWithMediaIncluded = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      select: {
        id: true;
        company: true;
        companyDetails: true;
      };
    };
    customerFavourites: {
      select: {
        id: true;
      };
    };
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
            mediaType: true;
            cloudinaryId: true;
            cloudinaryUrl: true;
          };
        };
      };
    };
  };
}>;

const PropertyListing = () => {
  const searchParams = useSearchParams();
  const [mapZoom, setMapZoom] = useState<number>(5);
  const { lat, lng } = useUserLocation();
  const [userLocation, setUserLocation] = useState<
    { lat: number; lng: number } | undefined
  >(undefined);
  const [points, setPoints] = useState<Points[]>([]);
  const [selectedProperty, setSelectedProperty] =
    useState<PropertiesWithMediaIncluded | null>(null);
  const [mapBounds, setMapBounds] = useState<{
    ne: { lat: number; lng: number };
    sw: { lat: number; lng: number };
  } | null>(null);
  const searchLat = searchParams.get("lat");
  const searchLng = searchParams.get("long");

  const customerDetail = api.user.getProfile.useQuery();
  const connectdAgentId = customerDetail.data?.connections[0]?.agentId;

  const { data: userLoginInfo } = useSession();

  const params = {
    propertyFor: searchParams.get("propertyFor"),
    minPrice: searchParams.get("minPrice"),
    maxPrice: searchParams.get("maxPrice"),
    beds: searchParams.get("beds"),
    baths: searchParams.get("baths"),
    areaUnit: searchParams.get("areaUnit"),
    minArea: searchParams.get("minArea"),
    maxArea: searchParams.get("maxArea"),
    furnishType: searchParams.get("furnishType"),
    propertyState: searchParams.get("salesType"),
    possessionState: searchParams.get("possessionState"),
    facing: searchParams.get("facing"),
    searchTerm: searchParams.get("searchTerm"),
    propertyCategory: searchParams.get("propertyCategory"),
    homeTypes: searchParams.get("listingTypes"),
    lat: searchParams.get("lat"),
    long: searchParams.get("long"),
  };

  const { data, isPending } =
    api.property.getPropertiesAccordingToFiltersUsingBounds.useQuery(
      {
        bounds: mapBounds!,
        // zoom: mapZoom,
        propertyFor: (params.propertyFor ?? "SALE") as PropertyForEnum,
        minPrice: Number(params.minPrice),
        maxPrice: Number(params.maxPrice),
        beds: params.beds === "any" ? 1 : Number(params.beds),
        baths: params.baths === "any" ? 1 : Number(params.baths),
        minArea: Number(params.minArea),
        maxArea: Number(params.maxArea),
        areaUnitId: params.areaUnit ?? "",
        furnishType: (params.furnishType as FurnishingEnum) ?? undefined,
        propertyState: (params.propertyState as PropertyStateEnum) ?? undefined,
        possessionState:
          (params.possessionState as PossessionStateEnum) ?? undefined,
        facing: (params.facing as FacingEnum) ?? undefined,
        searchQuery: params.searchTerm ?? undefined,
        propertyCategory: params.propertyCategory ?? undefined,
        homeTypes: params.homeTypes?.split(",") ?? undefined,
        take: 20,
        page: Number(searchParams.get("page") ?? 1),
      },
      {
        enabled: !!mapBounds,
        staleTime: 2000,
        refetchOnWindowFocus: false,
      },
    );

  const properties = data?.properties ?? [];
  const totalResults = data?.totalResults ?? 0;
  const totalPages = data?.totalPages ?? 0;

  /**
   * Managing userLocation as a state is crucial for two reasons:
   * 1. To trigger re-renders when location changes, preventing map stiffness
   * 2. To ensure proper map interaction when user location is available
   *
   * Direct passing of lat/lng without state management leads to unresponsive map behavior
   */
  useEffect(() => {
    if (lat && lng) {
      setUserLocation({ lat, lng });
    }
  }, [lat, lng]);

  return (
    <div className="flex flex-col gap-[18px] px-5 sm:gap-6 xl:gap-6">
      <TopBar
        points={points}
        properties={properties}
        onBoundsChanged={(bounds) => {
          setMapBounds(bounds);
        }}
        isLoading={isPending}
        loadingMessage={data?.message}
      />
      <div className="w-full items-start lg:flex lg:justify-between">
        <div className="sticky top-[80px] hidden w-full lg:block">
          <GoogleMap
            points={points}
            properties={properties}
            mapClassName="flex h-full min-h-[80vh] flex-1 lg:min-h-[calc(100vh-12vh)] xl:min-h-[calc(100vh-10vh)] 2xl:min-h-[calc(100vh-8vh)]"
            className="aspect-auto w-full overflow-hidden rounded-xl md:aspect-auto xl:aspect-auto"
            userLocation={userLocation}
            selectedProperty={selectedProperty}
            // @ts-expect-error: This is just an types issue here, becoz if i change the type in google-map it will change for all and in customer app 1 extra field i am fetching which is customerFavourites so that's the issue which will not break the web at any point.
            setSelectedProperty={setSelectedProperty}
            locationIcon="/icons/location.svg"
            onBoundsChanged={(bounds) => {
              setMapBounds(bounds);
            }}
            onZoomChanged={(zoom) => {
              setMapZoom(zoom);
            }}
            searchLat={Number(searchLat)}
            searchLng={Number(searchLng)}
            contactOrCheckResponsesButton={
              selectedProperty ? (
                <ContactAgentButton
                  agentId={selectedProperty.user.id}
                  connectedAgentId={connectdAgentId ?? ""}
                />
              ) : null
            }
            editPropertyButton={
              selectedProperty ? (
                <EditPropertyButton propertyId={selectedProperty.id} />
              ) : null
            }
            likePropertyButton={
              selectedProperty ? (
                <LikePropertyButton
                  propertyId={selectedProperty.id}
                  isPropertyLiked={true}
                />
              ) : null
            }
            sharePropertyButton={
              selectedProperty ? (
                <SharePropertyButton propertyId={selectedProperty.id} />
              ) : null
            }
            userId={userLoginInfo?.user?.id}
          />

          {isPending && (
            <div className="absolute left-[50%] top-10 z-50 flex -translate-x-1/2 items-center gap-2 text-nowrap rounded-sm bg-black px-2 py-1 text-center text-xs text-white lg:top-20 lg:text-sm">
              <Loader2 className="size-4 animate-spin" />
              Looking for properties...
            </div>
          )}
          {data?.message && (
            <div className="absolute left-[50%] top-10 z-50 flex -translate-x-1/2 items-center gap-2 text-nowrap rounded-sm bg-black px-2 py-1 text-center text-xs text-white lg:top-20 lg:text-sm">
              {data.message}
            </div>
          )}
        </div>
        <PropertiesResults
          properties={properties}
          isLoading={isPending}
          totalResults={totalResults}
          totalPages={totalPages}
        />
      </div>
    </div>
  );
};

export default PropertyListing;
