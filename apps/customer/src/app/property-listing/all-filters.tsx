import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import ForSaleFilter from "./for-sale-filter";
import PriceFilter from "./price-filter";
import BedsBathroomFilters from "./beds-bathroom-filter";
import AllHomeTypeFilter from "./all-home-type-filter";
import MoreFilter from "./more-filter";
import { ChevronDown, XIcon } from "lucide-react";
import {
  Dialog,
  DialogTitle,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@repo/ui/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@repo/ui/components/ui/accordion";

import Image from "next/image";
import { cn } from "@repo/ui/lib/utils";
import { Button } from "@repo/ui/components/ui/button";
import PropertyCategoryFilter from "./property-category-filter";
import z from "zod";
import {
  FacingEnum,
  FurnishingEnum,
  PossessionStateEnum,
  PropertyForEnum,
} from "@repo/database";

const AllFilters = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize state from URL parameters
  const propertyForZod = z
    .nativeEnum(PropertyForEnum)
    .safeParse(searchParams.get("propertyFor"));
  const propertyFor = propertyForZod.success ? propertyForZod.data : "SALE";
  // Property category is the category of the property like 'residential' 'commercial'

  //   const propertyCategoryZod = z
  //     .nativeEnum(PropertyCategoryEnum)
  //     .safeParse(searchParams.get("propertyCategory"));
  const propertyCategory = searchParams.get("propertyCategory");
  // Min price is the minimum price of the property
  const minPrice = searchParams.get("minPrice")
    ? Number(searchParams.get("minPrice"))
    : undefined;
  // Max price is the maximum price of the property
  const maxPrice = searchParams.get("maxPrice")
    ? Number(searchParams.get("maxPrice"))
    : undefined;
  //  bedrooms
  const beds = searchParams.get("beds") ? searchParams.get("beds") : "any";
  const baths = searchParams.get("baths") ? searchParams.get("baths") : "any";
  // state refers to allHomeType filters
  //   const areaUnitZod = z
  //     .nativeEnum(AreaInEnum)
  //     .safeParse(searchParams.get("areaUnit"));
  //   const areaUnit = areaUnitZod.success
  //     ? areaUnitZod.data
  //     : AreaInEnum.SQUAREFEET;
  const areaUnit = searchParams.get("areaUnitId");
  const minArea = searchParams.get("minArea")
    ? Number(searchParams.get("minArea"))
    : undefined;
  const maxArea = searchParams.get("maxArea")
    ? Number(searchParams.get("maxArea"))
    : undefined;
  const listingTypes = searchParams.get("listingTypes")?.split(",") ?? [];
  console.log("listing typesssssssss", listingTypes);
  const furnishTypeEnum = z
    .nativeEnum(FurnishingEnum)
    .safeParse(searchParams.get("furnishType"));
  const furnishType = furnishTypeEnum.success
    ? furnishTypeEnum.data
    : undefined;
  const petAllowed =
    (searchParams.get("petAllowed") as "YES" | "NO" | "") || "";
  const salesType = searchParams.get("salesType") ?? undefined;

  const possessionStateEnum = z
    .nativeEnum(PossessionStateEnum)
    .safeParse(searchParams.get("possessionState"));
  const possessionState = possessionStateEnum.success
    ? possessionStateEnum.data
    : undefined;
  const facingEnum = z
    .nativeEnum(FacingEnum)
    .safeParse(searchParams.get("facing"));
  const facing = facingEnum.success ? facingEnum.data : undefined;
  const pointOfInterests =
    searchParams.get("pointOfInterests")?.split(",") ?? [];
  const stayType = searchParams.get("stayType") ?? undefined;
  const amenities = searchParams.get("amenities")?.split(",") ?? [];
  const minSecurityDeposit =
    Number(searchParams.get("minSecurityDeposit")) || undefined;
  const maxSecurityDeposit =
    Number(searchParams.get("maxSecurityDeposit")) || undefined;

  // Wrapper functions to update state and URL
  const handleSelectedValueChange = (
    name: string,
    value: string | undefined,
  ) => {
    if (!value) {
      return;
    }

    const params = new URLSearchParams(searchParams);

    // If changing property category, clear the listingTypes
    if (name === "propertyCategory" || name === "propertyFor") {
      params.delete("listingTypes");
    }

    // If the value is an empty string (which happens when the last item is unchecked),
    // remove the parameter entirely instead of setting it to empty
    if (value === "") {
      params.delete(name);
    } else {
      params.set(name, value);
    }

    router.push(`?${params.toString()}`);
  };

  const resetAllFilters = () => {
    router.push(`?propertyFor=SALE`);
  };

  return (
    <div>
      {/* Mobile view filters */}
      <div className="absolute right-5 top-5 z-40">
        <Dialog>
          <DialogTrigger asChild>
            <div className="rounded-lg bg-primary-2-700 px-3 py-[9px] lg:hidden">
              <Image
                src="/icons/settings.svg"
                alt="filter"
                height={50}
                width={50}
                className="size-6"
              />
            </div>
          </DialogTrigger>
          <DialogContent className="max-h-full overflow-y-scroll">
            <DialogTitle className="hidden">Filters</DialogTitle>
            <DialogClose className="sticky top-0 z-20 flex items-center justify-between p-4 backdrop-blur-lg">
              <h2 className="text-xl">Filters</h2>
              <XIcon className="size-4 text-primary-700" />
            </DialogClose>
            <div className="my-6 xs:px-4">
              <Accordion
                type="multiple"
                defaultValue={[
                  "item-1",
                  "item-2",
                  "item-3",
                  "item-4",
                  "item-4",
                  "item-5",
                ]}
                className="space-y-3"
              >
                <AccordionItem
                  value="item-1"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold capitalize text-primary-2-800 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      {propertyFor}
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="pl-4">
                    <ForSaleFilter
                      selectedValue={propertyFor}
                      setSelectedValue={(e) =>
                        handleSelectedValueChange("propertyFor", e)
                      }
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-2"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-primary-2-800 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      Price
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <PriceFilter
                      minPriceValue={minPrice}
                      maxPriceValue={maxPrice}
                      setMinPriceValue={(value) => {
                        const params = new URLSearchParams(searchParams);
                        if (value === undefined) {
                          params.delete("minPrice");
                        } else {
                          params.set("minPrice", value.toString());
                        }
                        router.push(`?${params.toString()}`);
                      }}
                      setMaxPriceValue={(value) => {
                        const params = new URLSearchParams(searchParams);
                        if (value === undefined) {
                          params.delete("maxPrice");
                        } else {
                          params.set("maxPrice", value.toString());
                        }
                        router.push(`?${params.toString()}`);
                      }}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-3"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-primary-2-800 lg:py-3 lg:text-base xl:text-lg",
                      )}
                      text-primary-0
                    >
                      Beds & Baths
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <BedsBathroomFilters
                      numberOfBeds={beds}
                      numberOfBathrooms={baths}
                      setNumberOfBeds={(e) =>
                        handleSelectedValueChange("beds", e?.toString())
                      }
                      setNumberOfBathrooms={(e) =>
                        handleSelectedValueChange("baths", e?.toString())
                      }
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-4"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-primary-2-800 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      Property Category
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <PropertyCategoryFilter
                      selectedValue={propertyCategory}
                      setSelectedValue={(e) =>
                        handleSelectedValueChange(
                          "propertyCategory",
                          e.toString(),
                        )
                      }
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-5"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-primary-2-800 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      Property Type
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <AllHomeTypeFilter
                      propertyCategoryId={propertyCategory}
                      selectedValues={listingTypes}
                      setSelectedValues={(e) =>
                        handleSelectedValueChange("listingTypes", e.join(","))
                      }
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="item-6"
                  className="rounded-2xl border border-primary-400"
                >
                  <AccordionTrigger className="px-4 py-2">
                    <button
                      className={cn(
                        "w-full whitespace-nowrap py-2 text-sm font-semibold text-primary-2-800 lg:py-3 lg:text-base xl:text-lg",
                      )}
                    >
                      More Filters
                    </button>
                  </AccordionTrigger>
                  <AccordionContent className="px-4">
                    <MoreFilter
                      areaUnitId={areaUnit}
                      listingTypes={listingTypes}
                      furnishType={furnishType}
                      minAreaUnit={minArea}
                      maxAreaUnit={maxArea}
                      amenities={amenities}
                      stayType={stayType}
                      salesType={salesType}
                      propertyFacing={facing}
                      possessionState={possessionState}
                      pointOfInterests={pointOfInterests}
                      minSecurityDeposit={minSecurityDeposit}
                      maxSecurityDeposit={maxSecurityDeposit}
                      showExceptionalFilters={propertyFor === "RENT"}
                      setAmenities={(e) =>
                        handleSelectedValueChange("amenities", e.join(","))
                      }
                      setAreaUnit={(e) =>
                        handleSelectedValueChange("areaUnit", e)
                      }
                      setTypeOfListings={() => {
                        handleSelectedValueChange(
                          "listingTypes",
                          listingTypes.join(","),
                        );
                      }}
                      setFurnishType={(e) =>
                        handleSelectedValueChange("furnishType", e)
                      }
                      setMinAreaUnit={(e) =>
                        handleSelectedValueChange("minAreaUnit", e?.toString())
                      }
                      setMaxAreaUnit={(e) =>
                        handleSelectedValueChange("maxAreaUnit", e?.toString())
                      }
                      setStayType={(e) =>
                        handleSelectedValueChange("stayType", e)
                      }
                      setSalesType={(e) =>
                        handleSelectedValueChange("salesType", e)
                      }
                      setPropertyFacing={(e) =>
                        handleSelectedValueChange("facing", e)
                      }
                      setPossessionState={(e) =>
                        handleSelectedValueChange("possessionState", e)
                      }
                      setPointOfInterests={(e) =>
                        handleSelectedValueChange(
                          "pointOfInterests",
                          e.join(","),
                        )
                      }
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
              <div className="my-3 flex gap-5">
                <Button
                  variant="outline"
                  onClick={resetAllFilters}
                  className="flex w-full items-center gap-2 text-sm"
                >
                  Reset
                </Button>
                <DialogClose asChild>
                  <Button className="flex w-full items-center gap-2">
                    Apply
                  </Button>
                </DialogClose>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Larger screens filters */}
      <div className="no-scrollbar hidden w-full flex-wrap gap-4 md:gap-3 lg:flex">
        {/* For sale filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm capitalize text-primary-0 lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",

                "border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              {propertyFor}
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <ForSaleFilter
              selectedValue={propertyFor}
              setSelectedValue={(e) =>
                handleSelectedValueChange("propertyFor", e)
              }
            />
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Price filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm text-text-550 lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                (minPrice ?? maxPrice) &&
                  "border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              Price
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <PriceFilter
              minPriceValue={minPrice}
              maxPriceValue={maxPrice}
              setMinPriceValue={(value) => {
                const params = new URLSearchParams(searchParams);
                if (value === undefined) {
                  params.delete("minPrice");
                } else {
                  params.set("minPrice", value.toString());
                }
                router.push(`?${params.toString()}`);
              }}
              setMaxPriceValue={(value) => {
                const params = new URLSearchParams(searchParams);
                if (value === undefined) {
                  params.delete("maxPrice");
                } else {
                  params.set("maxPrice", value.toString());
                }
                router.push(`?${params.toString()}`);
              }}
            />
          </DropdownMenuContent>
        </DropdownMenu>

        {/* beds and bathrooms */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "text-primary-0text-text-550 border-primary-100lg:py-3 flex items-center gap-2 whitespace-nowrap rounded-lg border px-3 py-2 text-sm lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                (beds !== "any" || baths !== "any") &&
                  "border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              Beds & Baths
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <BedsBathroomFilters
              numberOfBeds={beds}
              numberOfBathrooms={baths}
              setNumberOfBeds={(e) =>
                handleSelectedValueChange("beds", e?.toString())
              }
              setNumberOfBathrooms={(e) =>
                handleSelectedValueChange("baths", e?.toString())
              }
            />
          </DropdownMenuContent>
        </DropdownMenu>

        {/* property category */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                propertyCategory &&
                  "border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              Property Category
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="p-4">
            <PropertyCategoryFilter
              selectedValue={propertyCategory}
              setSelectedValue={(e) =>
                handleSelectedValueChange("propertyCategory", e.toString())
              }
            />
          </DropdownMenuContent>
        </DropdownMenu>

        {/* all home type */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                listingTypes.length > 1 &&
                  "border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              All Home Type
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="py-5 pl-6 pr-12">
            <AllHomeTypeFilter
              propertyCategoryId={propertyCategory}
              selectedValues={listingTypes}
              setSelectedValues={(values) => {
                // Only join and update if there are values, otherwise pass empty string to remove parameter
                const newValue = values.length > 0 ? values.join(",") : "";
                // const newValue = values.length > 0 ? values : undefined;
                handleSelectedValueChange("listingTypes", newValue);
              }}
            />
          </DropdownMenuContent>
        </DropdownMenu>

        {/* more */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "text-primary-0text-text-550 flex items-center gap-2 whitespace-nowrap rounded-lg border border-primary-100 px-3 py-2 text-sm lg:py-3 lg:text-base xl:px-4 xl:py-2 xl:text-lg",
                (listingTypes.length > 1 ||
                  (furnishType && furnishType.length > 1) ||
                  petAllowed ||
                  (minArea && maxArea) ||
                  amenities.length > 1 ||
                  stayType ||
                  salesType ||
                  facing ||
                  possessionState ||
                  pointOfInterests.length > 1 ||
                  minSecurityDeposit ||
                  maxSecurityDeposit ||
                  salesType) &&
                  "border-secondary-2-700 bg-secondary-2-100 text-secondary-2-700",
              )}
            >
              Filters
              <ChevronDown className="size-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="max-h-[70vh] overflow-y-scroll p-4">
            <MoreFilter
              areaUnitId={areaUnit}
              listingTypes={listingTypes}
              furnishType={furnishType}
              minAreaUnit={minArea}
              maxAreaUnit={maxArea}
              amenities={amenities}
              stayType={stayType}
              salesType={salesType}
              propertyFacing={facing}
              possessionState={possessionState}
              pointOfInterests={pointOfInterests}
              minSecurityDeposit={minSecurityDeposit}
              maxSecurityDeposit={maxSecurityDeposit}
              showExceptionalFilters={propertyFor === "RENT"} // only show expceptional filter if the 'for rent'
              setAmenities={(e) =>
                handleSelectedValueChange("amenities", e.join(","))
              }
              setAreaUnit={(e) => handleSelectedValueChange("areaUnit", e)}
              setTypeOfListings={(e) =>
                handleSelectedValueChange("", e.join(","))
              }
              setFurnishType={(e) =>
                handleSelectedValueChange("furnishType", e)
              }
              setMinAreaUnit={(e) =>
                handleSelectedValueChange("minAreaUnit", e?.toString())
              }
              setMaxAreaUnit={(e) =>
                handleSelectedValueChange("maxAreaUnit", e?.toString())
              }
              setStayType={(e) => handleSelectedValueChange("stayType", e)}
              setSalesType={(e) => handleSelectedValueChange("salesType", e)}
              setPropertyFacing={(e) => handleSelectedValueChange("facing", e)}
              setPossessionState={(e) =>
                handleSelectedValueChange("possessionState", e)
              }
              setPointOfInterests={(e) =>
                handleSelectedValueChange("pointOfInterests", e.join(","))
              }
            />
          </DropdownMenuContent>
        </DropdownMenu>

        {/* reset filters button */}
        <Button
          variant="outline"
          onClick={resetAllFilters}
          className="flex items-center gap-2 border-primary-2-700 bg-primary-2-700 text-[#FFFFFF]"
        >
          {/* <Image
            src="/icons/bin.svg"
            alt="Del"
            height={50}
            width={50}
            className="size-5"
          />{" "} */}
          <span className="font-airbnb_w_md font-medium">Reset</span>
        </Button>
      </div>
    </div>
  );
};

export default React.memo(AllFilters);
