import { Input } from "@repo/ui/components/ui/input";
import React from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { CheckCheck, ChevronDown } from "lucide-react";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { Label } from "@repo/ui/components/ui/label";
import { api } from "~/trpc/react";
import { useSearchParams } from "next/navigation";
import type {
  FurnishingEnum,
  //   PropertyCategoryEnum,
} from "@repo/database";

const TypeOfListings = ["NEW", "RESALE", "UPCOMING"];
export const UNITS = ["SQUAREFEET", "SQUAREMETER", "SQUAREYARD"];
export const FurnishType = ["RAW", "SEMIFURNISHED", "FULLYFURNISHED"];
const PossessionState = [
  "READY_TO_MOVE",
  "UNDER_6_MONTHS",
  "UNDER_1_YEAR",
  "UNDER_3_YEARS",
];
const PropertyFacing = [
  "NORTH",
  "SOUTH",
  "EAST",
  "WEST",
  "NORTH_EAST",
  "NORTH_WEST",
  "SOUTH_EAST",
  "SOUTH_WEST",
];

type MoreFilterProps = {
  areaUnitId: string | null;
  listingTypes: string[];
  furnishType: FurnishingEnum | undefined;
  minAreaUnit: number | undefined;
  maxAreaUnit: number | undefined;
  salesType: string | undefined;
  possessionState: string | undefined;
  amenities: string[];
  propertyFacing: string | undefined;
  pointOfInterests: string[];
  stayType: string | undefined;
  minSecurityDeposit: number | undefined;
  maxSecurityDeposit: number | undefined;
  showExceptionalFilters: boolean;
  setAreaUnit: (v: string | undefined) => void;
  setTypeOfListings: (v: string[]) => void;
  setFurnishType: (v: string) => void;
  setMinAreaUnit: (v: number | undefined) => void;
  setMaxAreaUnit: (v: number | undefined) => void;
  setSalesType: (v: string | undefined) => void;
  setPossessionState: (v: string | undefined) => void;
  setAmenities: (v: string[]) => void;
  setPropertyFacing: (v: string | undefined) => void;
  setPointOfInterests: (v: string[]) => void;
  setStayType: (v: string | undefined) => void;
};

const MoreFilter = ({
  areaUnitId,
  furnishType,
  minAreaUnit,
  maxAreaUnit,
  salesType,
  possessionState,
  amenities,
  propertyFacing,
  setAreaUnit,
  setFurnishType,
  setMinAreaUnit,
  setMaxAreaUnit,
  setSalesType,
  setPropertyFacing,
  setPossessionState,
  setAmenities,
}: MoreFilterProps) => {
  const params = useSearchParams();
  const areaUnitFromParams = params.get("areaUnit");
  const propertyCategory = params.get("propertyCategory");
  const { data } = api.property.getAllDynamicFilters.useQuery({
    propertyCategoryId: propertyCategory ?? "",
  });

  const DynamicAmenities = data?.amenities ?? [];
  const areaUnits = data?.areaUnits ?? [];

  //   const addRemoveItem = (item: string) => {
  //     // if item is already selected then remove that from the array
  //     if (listingTypes.includes(item)) {
  //       setTypeOfListings(listingTypes.filter((value) => value !== item));
  //       return;
  //     }

  //     // if not present simply add that element
  //     setTypeOfListings([...listingTypes, item]);
  //   };

  const addRemoveItemFromAmenities = (item: string) => {
    // if item is already selected then remove that from the array
    if (amenities.includes(item)) {
      setAmenities(amenities.filter((value) => value !== item));
      return;
    }

    // if not present simply add that element
    setAmenities([...amenities, item]);
  };

  return (
    <div className="flex flex-col gap-4">
      {/* type of listings */}
      {/* <div className="space-y-4">
        <p className="font-semibold">Type of listings </p>
        <ul className="space-y-4">
          {TypeOfListings.map((item) => (
            <li className="flex items-center gap-3">
              <Checkbox
                className="rounded-none"
                id={item}
                checked={typeOfListings.includes(item)}
                onCheckedChange={() => addRemoveItem(item)}
              />
              <Label htmlFor={item} className="capitalize">
                {item.replaceAll("_", " ").toLowerCase()}
              </Label>
            </li>
          ))}
        </ul>
      </div> */}

      {/* area in */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <p className="font-semibold">Area In</p>
          <DropdownMenu>
            <DropdownMenuTrigger>
              {areaUnitFromParams ? (
                <Button variant="outline" className="px-2 py-2.5 text-xs">
                  {areaUnitFromParams}
                </Button>
              ) : (
                <Button variant="outline" className="px-2 py-2.5 text-xs">
                  Select Unit <ChevronDown className="size-4"></ChevronDown>
                </Button>
              )}
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Area</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {areaUnits.length
                ? areaUnits.map((item) => (
                    <DropdownMenuItem
                      key={item.id}
                      onClick={() => setAreaUnit(item.name)}
                    >
                      {areaUnitId === item.id ? (
                        <CheckCheck className="size-5 pr-2" />
                      ) : (
                        <span className="size-5 pr-2" />
                      )}
                      {item.name}
                    </DropdownMenuItem>
                  ))
                : "select a category to view associated units"}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-4">
          <Input
            type="number"
            onWheel={() => {
              return false;
            }}
            placeholder="Minimum Unit"
            value={minAreaUnit}
            onChange={(e) => setMinAreaUnit(e.target.valueAsNumber)}
          />
          <span>-</span>
          <Input
            type="number"
            onWheel={() => {
              return false;
            }}
            placeholder="Maximum Unit"
            value={maxAreaUnit}
            onChange={(e) => setMaxAreaUnit(e.target.valueAsNumber)}
          />
        </div>
      </div>

      {/* furnish type */}
      <div className="space-y-4">
        <p className="font-semibold">Furnish type</p>
        <ul className="space-y-4">
          {FurnishType.map((item) => (
            <li className="flex items-center gap-3">
              <Checkbox
                id={item}
                checked={furnishType === item}
                onCheckedChange={() => setFurnishType(item)}
                className="peer data-[state=checked]:text-secondary-2-750"
              />
              <Label
                htmlFor={item}
                className="capitalize text-text-500 peer-data-[state=checked]:text-secondary-2-750"
              >
                {item.replaceAll("_", " ").toLowerCase()}
              </Label>
            </li>
          ))}
        </ul>
      </div>

      {/* sales type */}
      <div className="space-y-4">
        <p className="font-semibold">Sales Type</p>
        <ul className="space-y-4">
          {TypeOfListings.map((item, idx) => (
            <li key={idx} className="flex items-center gap-3">
              <Checkbox
                id={item}
                checked={salesType === item}
                onCheckedChange={() => setSalesType(item)}
                className="peer data-[state=checked]:text-secondary-2-750"
              />
              <Label
                htmlFor={item}
                className="capitalize text-text-500 peer-data-[state=checked]:text-secondary-2-750"
              >
                {item}
              </Label>
            </li>
          ))}
        </ul>
      </div>

      {/* possession state */}
      <div className="space-y-4">
        <p className="font-semibold">Possession State</p>
        <ul className="space-y-4">
          {PossessionState.map((item, idx) => (
            <li key={idx} className="flex items-center gap-3">
              <Checkbox
                id={item}
                checked={possessionState === item}
                onCheckedChange={() => setPossessionState(item)}
                className="peer data-[state=checked]:text-secondary-2-750"
              />
              <Label
                htmlFor={item}
                className="capitalize text-text-500 peer-data-[state=checked]:text-secondary-2-750"
              >
                {item.replaceAll("_", " ").toLocaleLowerCase()}
              </Label>
            </li>
          ))}
        </ul>
      </div>

      {/* amenities */}
      <div className="space-y-4">
        <p className="font-semibold">Amenities</p>
        {DynamicAmenities.length ? (
          <ul className="space-y-4">
            {DynamicAmenities.map((item) => (
              <li className="flex items-center gap-3">
                <Checkbox
                  className="peer rounded-none data-[state=checked]:text-secondary-2-750"
                  id={item.id}
                  checked={amenities.includes(item.name)}
                  onCheckedChange={() => addRemoveItemFromAmenities(item.name)}
                />
                <Label
                  htmlFor={item.id}
                  className="capitalize text-text-500 peer-data-[state=checked]:text-secondary-2-750"
                >
                  {item.name}
                </Label>
              </li>
            ))}
          </ul>
        ) : (
          <>No amenity found</>
        )}
      </div>

      {/* property facing */}
      <div className="space-y-4">
        <p className="font-semibold">Property Facing</p>
        <ul className="space-y-4">
          {PropertyFacing.map((item, idx) => (
            <li key={idx} className="flex items-center gap-3">
              <Checkbox
                id={item}
                checked={propertyFacing === item}
                onCheckedChange={() => setPropertyFacing(item)}
                className="peer data-[state=checked]:text-secondary-2-750"
              />
              <Label
                htmlFor={item}
                className="capitalize text-text-500 peer-data-[state=checked]:text-secondary-2-750"
              >
                {item.replaceAll("_", " ").toLowerCase()}
              </Label>
            </li>
          ))}
        </ul>
      </div>

      {/* point of interest */}
      {/* <div className="space-y-4">
        <p className="font-semibold">Point Of Interest</p>
        <ul className="space-y-4">
          {PointOfInterests.map((item) => (
            <li className="flex items-center gap-3">
              <Checkbox
                className="rounded-none"
                id={"poi" + item}
                checked={pointOfInterests.includes(item)}
                onCheckedChange={() => addRemoveItemFromPointofInterest(item)}
              />
              <Label htmlFor={"poi" + item}>{item}</Label>
            </li>
          ))}
        </ul>
      </div> */}

      {/* stay type, render this element only when 'for rent' is selected */}
      {/* {showExceptionalFilters && (
        <div className="space-y-4">
          <p className="font-semibold">Stay Type</p>
          <ul className="space-y-4">
            {StayType.map((item, idx) => (
              <li key={idx} className="flex items-center gap-3">
                <Checkbox
                  id={"stay" + item}
                  checked={stayType === item}
                  onCheckedChange={() => setStayType(item)}
                />
                <Label htmlFor={"stay" + item}>{item}</Label>
              </li>
            ))}
          </ul>
        </div>
      )} */}

      {/* rent amenities */}
      {/* {showExceptionalFilters && (
        <div className="space-y-4">
          <p className="font-semibold">Rent Amenities</p>
          <ul className="space-y-4">
            {RentAmenities.map((item, idx) => (
              <li key={idx} className="flex items-center gap-3">
                <Checkbox
                  id={"rent" + item}
                  className="rounded-none"
                  checked={rentAmenities.includes(item)}
                  onCheckedChange={() => addRemoveItemFromRentAmenties(item)}
                />
                <Label htmlFor={"rent" + item}>{item}</Label>
              </li>
            ))}
          </ul>
        </div>
      )} */}

      {/* {showExceptionalFilters && (
        <div className="space-y-3">
          <p className="font-semibold">Security Deposit</p>

          <div className="flex items-center gap-4">
            <Input
              type="number"
                        onWheel={() => {
                          return false;
                        }}
              placeholder="Minimum Price"
              className="xs:p-px-5 px-4 py-2 xs:py-[14px]"
              value={minSecurityDeposit}
              onChange={(e) => setMinSecurityDeposit(e.target.valueAsNumber)}
            />
            <span>-</span>
            <Input
              type="number"
                        onWheel={() => {
                          return false;
                        }}
              placeholder="Maximum Price"
              className="xs:p-px-5 px-4 py-2 xs:py-[14px]"
              value={maxSecurityDeposit}
              onChange={(e) => setMaxSecurityDeposit(e.target.valueAsNumber)}
            />
          </div>
        </div>
      )} */}

      {/* pet allowed */}
      {/* <div className="space-y-4">
        <p className="font-semibold">Pet allowed</p>
        <ul className="flex items-center gap-6">
          <li className="flex items-center gap-2">
            <Checkbox
              id="yes"
              checked={petAllowed === "YES"}
              onCheckedChange={() => setPetAllowed("YES")}
            />
            <Label htmlFor="yes">Yes</Label>
          </li>
          <li className="flex items-center gap-2">
            <Checkbox
              id="no"
              checked={petAllowed === "NO"}
              onCheckedChange={() => setPetAllowed("NO")}
            />
            <Label htmlFor="no">No</Label>
          </li>
        </ul>
      </div> */}
    </div>
  );
};

export default MoreFilter;
