import React from "react";

import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { Label } from "@repo/ui/components/ui/label";
import { api } from "~/trpc/react";
import { skipToken } from "@tanstack/react-query";
import Loading from "../components/shared/loading";
import type { AllHomeTypeFilterProps } from "../types";
// import { PropertyCategoryEnum } from "@repo/database";

const AllHomeTypeFilter = ({
  propertyCategoryId,
  selectedValues,
  setSelectedValues,
}: AllHomeTypeFilterProps) => {
  console.log(
    "property categpry goes to api for fetching the data is",
    propertyCategoryId,
  );
  const { data, isLoading } = api.property.getAllDynamicFilters.useQuery(
    propertyCategoryId
      ? {
          propertyCategoryId: propertyCategoryId,
        }
      : skipToken,
  );

  const addRemoveItem = (item: string) => {
    // if item is already selected then remove that from the array
    // Create a new array based on whether the item is being checked or unchecked
    const newSelectedValues = selectedValues.includes(item)
      ? selectedValues.filter((value) => value !== item)
      : [...selectedValues, item];

    // Update the parent component with the new array
    setSelectedValues(newSelectedValues);
  };

  if (isLoading) {
    return <Loading />;
  }

  const propertyTypes = data?.propertyTypes ?? [];

  return (
    <div className="space-y-3">
      <p className="font-semibold">All Home Type</p>
      <ul className="space-y-4">
        {propertyTypes.length ? (
          propertyTypes.map((item) => (
            <li className="flex items-center gap-3">
              <Checkbox
                className="peer rounded-none data-[state=checked]:text-secondary-2-750"
                id={item.id}
                checked={selectedValues.includes(item.name)}
                onCheckedChange={() => addRemoveItem(item.name)}
              />
              <Label
                htmlFor={item.id}
                className="text-text-500 capitalize peer-data-[state=checked]:text-secondary-2-750"
              >
                {item.name}
              </Label>
            </li>
          ))
        ) : (
          <>No Property type found.</>
        )}
      </ul>
    </div>
  );
};

export default AllHomeTypeFilter;
