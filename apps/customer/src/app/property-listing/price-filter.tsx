import { Input } from "@repo/ui/components/ui/input";
import React from "react";

type PriceFilterProps = {
  minPriceValue: number | undefined;
  maxPriceValue: number | undefined;
  setMinPriceValue: (v: number | undefined) => void;
  setMaxPriceValue: (v: number | undefined) => void;
};

const PriceFilter = ({
  minPriceValue,
  maxPriceValue,
  setMinPriceValue,
  setMaxPriceValue,
}: PriceFilterProps) => {
  return (
    <div className="space-y-3">
      <p className="font-semibold text-text-500">Price Range</p>

      <div className="flex items-center gap-4">
        <Input
          type="number"
          onWheel={() => {
            return false;
          }}
          placeholder="Minimum Price"
          className="xs:p-px-5 px-4 py-2 xs:py-[14px]"
          value={minPriceValue || ""}
          onChange={(e) => {
            const value =
              e.target.value === "" ? undefined : Number(e.target.value);
            setMinPriceValue(value);
          }}
        />
        <span>-</span>
        <Input
          type="number"
          onWheel={() => {
            return false;
          }}
          placeholder="Maximum Price"
          className="xs:p-px-5 px-4 py-2 xs:py-[14px]"
          value={maxPriceValue || ""}
          onChange={(e) => {
            const value =
              e.target.value === "" ? undefined : Number(e.target.value);
            setMaxPriceValue(value);
          }}
        />
      </div>
    </div>
  );
};

export default PriceFilter;
