"use client";
import { MapPin } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useState, useCallback } from "react";
import PropertyCarousel from "./property-carousel";
import { api } from "~/trpc/react";
import { MeiliSearch } from "meilisearch";
import { env } from "~/env";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import ContactAgentButton from "../components/shared/contact-agent-button";
import PropertySkeleton from "./property-skeleton";

const meili = new MeiliSearch({
  apiKey: env.NEXT_PUBLIC_MEILI_SEARCH_KEY,
  host: env.NEXT_PUBLIC_MEILI_SEARCH_URL,
});

const Search = () => {
  const { data: customer } = api.user.getProfile.useQuery();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const query = useSearchParams();
  const meiliQuery = query.get("query");
  const { data: allProperties, isLoading: propertiesLoading } =
    api.property.getAllPropertiesForMeiliSearch.useQuery();
  const { data: allAgents, isLoading: agentsLoading } =
    api.agent.getAllAgents.useQuery();

  const [properties, setProperties] = useState<any[]>([]);
  const [agents, setAgents] = useState<any[]>([]);

  const filterProperties = useCallback(
    (propertyHitsId: string[]) => {
      return allProperties?.filter((item) => propertyHitsId.includes(item.id));
    },
    [allProperties],
  );

  const filterAgents = useCallback(
    (agentsHitsId: string[]) => {
      return allAgents?.filter((item) => agentsHitsId.includes(item.id));
    },
    [allAgents],
  );

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (!meiliQuery || meiliQuery.length === 0) {
        setProperties([]);
        setAgents([]);
        return;
      }

      try {
        const [propertyRes, partnerRes] = await Promise.all([
          meili.index(env.NEXT_PUBLIC_PROPERTY_INDEX).search(meiliQuery),
          meili.index(env.NEXT_PUBLIC_PARTNER_INDEX).search(meiliQuery),
        ]);

        const propertyHitsId = propertyRes.hits.map((item) => item.id);
        const agentsHitsId = partnerRes.hits.map((item) => item.id);

        const filteredPropertyData = filterProperties(propertyHitsId) ?? [];
        const filteredAgentsData = filterAgents(agentsHitsId) ?? [];

        setAgents(filteredAgentsData);
        setProperties(filteredPropertyData);
      } catch (error) {
        console.error("Error fetching properties:", error);
      }
    };

    void fetchSearchResults();
  }, [meiliQuery, filterProperties, filterAgents]);

  return (
    <div className="my-10">
      <div className="container mx-auto max-w-full">
        <div className="flex flex-col gap-10">
          {/* filters */}
          <div className="flex items-center justify-between">
            <p className="font-airbnb_w_bd text-xl font-bold text-text-600">
              Filters
            </p>
            <div className="flex gap-4 md:gap-10">
              <Link
                href="#agents"
                className="flex w-fit items-center justify-center gap-2 whitespace-nowrap rounded-xl border border-text-500 bg-white px-3 py-2 font-airbnb_w_bk text-sm font-medium text-text-500 md:px-4 md:py-3 lg:px-5 lg:text-base xl:px-6 xl:py-[14px]"
              >
                <Image
                  src="/icons/shared/user-icon.svg"
                  alt="profile-icon"
                  width={24}
                  height={24}
                />
                Agents
              </Link>
              <Link
                href="#properties"
                className="flex w-fit items-center justify-center gap-2 whitespace-nowrap rounded-xl border border-text-500 bg-white px-2 py-1 font-airbnb_w_bk text-sm font-medium text-text-500 md:px-4 md:py-3 lg:px-5 lg:text-base xl:px-6 xl:py-[14px]"
              >
                <Image
                  src="/icons/shared/search-property.svg"
                  alt="profile-icon"
                  width={24}
                  height={24}
                />
                Properties
              </Link>
            </div>
          </div>

          <div className="flex flex-col gap-[50px]">
            {/* agents */}
            {agentsLoading ? (
              "loading"
            ) : (
              <div
                className="flex scroll-mt-20 flex-col gap-[30px]"
                id="agents"
              >
                <h2 className="font-airbnb_w_bd text-2xl font-bold text-primary-800">
                  Agents
                </h2>
                {/* agents list container*/}
                <div className="flex flex-col gap-6">
                  {/* agents card */}
                  {agents.map((agent) => (
                    <Link
                      href={`?${params.toString()}&viewAgentId=${agent.id}`}
                      key={agent.id}
                      className="flex flex-col gap-4 rounded-xl border-2 border-text-40 p-4 transition-all duration-200 hover:bg-primary-2-100 md:flex-row md:p-5"
                    >
                      {/* Image */}
                      <div className="flex w-full gap-4 rounded-xl hover:bg-primary-2-100 md:flex-row">
                        <div className="relative aspect-square w-[120px] self-start md:w-[86px] xl:w-[100px]">
                          <Image
                            src={
                              agent.filePublicUrl ??
                              "/main/images/agent-fallback/agent-fallback-image.png"
                            }
                            alt="property-owner"
                            className="relative rounded-[18px] object-cover"
                            fill
                          />
                        </div>

                        {/* Details */}
                        <div className="flex w-full flex-col items-start md:flex-row md:justify-between">
                          <div className="w-full md:w-auto md:text-left">
                            <h2 className="font-airbnb_w_xbd text-2xl font-extrabold text-primary-2-850 md:text-xl lg:text-2xl xl:text-3xl">
                              {agent.name}
                            </h2>
                            <h3 className="mt-1 font-airbnb_w_md text-base font-medium text-text-600 md:text-lg">
                              {agent.companyDetails?.companyName}
                            </h3>
                            {agent.userLocation && (
                              <p className="flex gap-1 font-airbnb_w_bk text-text-600 md:mt-3 md:justify-start">
                                <MapPin className="w-4 text-primary-2-750 md:w-auto" />
                                {agent.userLocation}
                              </p>
                            )}
                          </div>

                          <div className="mt-3 hidden w-full justify-center md:mt-0 md:block md:w-auto">
                            <ContactAgentButton
                              agentId={agent.id}
                              connectedAgentId={
                                customer?.connections[0]?.agentId ?? ""
                              }
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex w-full justify-center md:hidden">
                        <ContactAgentButton
                          className="w-full"
                          agentId={agent.id}
                          connectedAgentId={
                            customer?.connections[0]?.agentId ?? ""
                          }
                        />
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* properties */}
            <div className="flex flex-col gap-[30px]" id="properties">
              <h2 className="font-airbnb_w_bd text-2xl font-bold text-primary-800">
                Properties
              </h2>
              {/* properties list container*/}
              {propertiesLoading ? (
                <PropertySkeleton></PropertySkeleton>
              ) : (
                <PropertyCarousel properties={properties} />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Search;
