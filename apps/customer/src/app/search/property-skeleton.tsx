import React from "react";

const PropertyCardSkeleton = () => (
    <div className="w-full min-w-[330px] max-w-[330px] animate-pulse rounded-2xl border-b border-b-text-50 bg-[#FCFFFE] shadow-[0px_1px_4px_0px_rgba(48,48,48,0.25)]">
    <div className="flex aspect-[4/3] flex-col justify-between rounded-2xl bg-gray-200 bg-cover bg-no-repeat p-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-row gap-[15px]">
          <div className="h-5 w-10 rounded-[5.44px] bg-gray-300"></div>
        </div>
        <div className="flex flex-row gap-[15px]">
          <div className="h-8 w-8 rounded-full bg-gray-300"></div>
          <div className="h-8 w-8 rounded-full bg-gray-300"></div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="h-5 w-12 rounded bg-gray-300"></div>
        <div className="h-5 w-12 rounded bg-gray-300"></div>
        <div className="h-5 w-12 rounded bg-gray-300"></div>
      </div>
    </div>

    <div className="px-[14px] py-3">
      {/* Info about property */}
      <div className="mb-3 flex items-center justify-between">
        {/* Title and location */}
        <div className="h-5 w-10 rounded bg-gray-300"></div>
        {/* Area */}
        <div className="h-5 w-16 rounded bg-gray-300"></div>
      </div>
      {/* Description about property */}
      <div className="mb-2 h-8 w-full rounded bg-gray-300"></div>

      <div className="flex items-center justify-between">
        <div className="h-8 w-32 rounded bg-gray-300"></div>
        <div className="h-6 w-20 rounded bg-gray-300"></div>
      </div>
    </div>
  </div>
);

const PropertySkeleton = () => (
  <div className="flex flex-wrap justify-center gap-28">
    {Array.from({ length: 3 }).map((_, index) => (
      <PropertyCardSkeleton key={index} />
    ))}
  </div>
);

export default PropertySkeleton;
