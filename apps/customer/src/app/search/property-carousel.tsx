"use client";
import React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import PropertyCard from "@repo/ui/components/shared/property-card";
import type { Prisma } from "@repo/database";
// import ContactAgentButton from "../agent-detail/[slug]/contact-agent-button";
// import LikePropertyButton from "../_components/shared/like-property-button";
// import SharePropertyButton from "../_components/shared/share-property-button";
import ContactAgentButton from "../components/shared/contact-agent-button";
import LikePropertyButton from "../components/shared/like-property-button";
import SharePropertyButton from "../components/shared/share-property-button";
import { api } from "~/trpc/react";

type PropertyCarouselProps = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      include: {
        company: true;
        companyDetails: true;
      };
    };
    comments: {
      include: {
        user: true;
      };
    };
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
          };
        };
      };
    };
  };
}>;

const PropertyCarousel = ({
  properties,
}: {
  properties: PropertyCarouselProps[];
}) => {
  const { data: customer } = api.user.getProfile.useQuery();
  return (
    <div className="flex flex-wrap justify-center gap-28">
      {properties.map((item) => (
        <PropertyCard
          key={item.id}
          locationIcon="/icons/location.svg"
          id={item.id}
          property={item}
          propertyOwnerId={item.userId}
          contactOrCheckResponsesButton={
            <ContactAgentButton
            //   className="rounded-xl border-2 border-secondary-2-700 bg-white px-6 py-3.5 font-airbnb_w_md text-lg font-medium text-secondary-2-750"
              agentId={item.userId}
              connectedAgentId={customer?.connections[0]?.agentId ?? ""}
            />
          }
          //   likePropertyButton={<LikePropertyButton propertyId={item.id} />}
          sharePropertyButton={<SharePropertyButton propertyId={item.id} />}
        />
      ))}
    </div>
  );
};

export default PropertyCarousel;
