import Image from "next/image";
import Link from "next/link";

const terms = [{ name: "Disclaimer", id: "disclaimer" }];

const Disclaimer = () => {
  return (
    <>
      <div className="container max-w-full bg-[#FFF6F4]">
        <div className="flex flex-col items-center gap-2.5 py-9 md:gap-3 md:py-[50px] lg:gap-4 lg:py-[55px] xl:py-[65px] 2xl:gap-5 2xl:py-[70px]">
          <Link
            href="/"
            className="font-airbnb_w_md text-sm font-medium text-primary-2-750 lg:text-base 2xl:text-lg"
          >
            /Back to home page
          </Link>
          <div className="flex flex-col items-center gap-1 md:gap-1.5 lg:gap-2 2xl:gap-3">
            <div className="flex flex-row items-center gap-2.5 md:gap-3 lg:gap-[14px] xl:gap-4 2xl:gap-[18px]">
              <div className="relative aspect-square w-[30px] md:w-9 lg:w-[40px] xl:w-[48px] 2xl:w-[60px]">
                <Image src="/icons/disclaimer.svg" alt="privacy-policy" fill />
              </div>
              <div className="font-airbnb_w_xbd text-3xl font-extrabold text-secondary-2-700 md:text-4xl lg:text-[44px] lg:leading-[56px] xl:text-[56px] xl:leading-[69px] 2xl:text-6xl 2xl:leading-[80px]">
                Disclaimer
              </div>
            </div>
            <p className="text-center font-airbnb_w_bk text-base font-normal text-[#1A1A1A] md:text-lg lg:text-xl 2xl:text-2xl">
              Last updated on 10 March 2025
            </p>
          </div>
        </div>
      </div>
      <div className="container flex max-w-full flex-col gap-7 py-9 md:flex-row md:items-start md:gap-9 md:py-10 lg:gap-10 lg:py-[45px] xl:gap-[60px] xl:py-[50px] 2xl:gap-[65px] 2xl:py-[60px]">
        <div className="justify-center font-airbnb_w_lt text-[18px] font-light leading-[30px] text-text-700 md:w-3/5 xl:text-[20px] xl:leading-[34px] 2xl:text-[22px] 2xl:leading-9">
          <section
            id="disclaimer"
            className="mb-8 scroll-mt-24 font-airbnb_w_bk font-normal text-text-500"
          >
            <h2 className="mb-4 font-airbnb_w_md text-2xl font-medium text-secondary-2-750 xl:text-[24px] 2xl:text-[26px]">
              Disclaimer
            </h2>
            <p className="">
              Although <Link href="/">mydeer.net</Link> will take every
              precaution to prevent fraud, deception, and illegal or unlawful
              action or inaction by anyone using its services, it cannot
              guarantee the accuracy, correctness, or dependability of any such
              information or content or{" "}
              <Link
                className="font-airbnb_w_md font-medium text-text-550 underline"
                href="/"
              >
                mydeer.net
              </Link>{" "}
              users. It is expected of you as a user to be vigilant and verify
              the correctness of any information that users submit on the{" "}
              <Link href="/">mydeer.net</Link> website. Anything on this website
              is merely for your reference and should not be interpreted as{" "}
              <Link href="/">mydeer.net</Link> advice. You should speak with a
              lawyer before utilising or putting any draft documents or advice
              to use.
            </p>

            <ul className="my-[50px] flex list-disc flex-col gap-3 space-y-2 pl-6 font-airbnb_w_bk font-normal text-text-500 marker:text-secondary-2-750 md:gap-4 lg:gap-10">
              <li>
                You use the <Link href="/">mydeer.net</Link> service at your own
                risk. Please be aware that <Link href="/">mydeer.net</Link> does
                not offer any warranties for the service, and this agreement
                severely restricts our liability. Additionally, the Agreement
                restricts your options for recourse.
              </li>
              <li>
                <Link href="/">mydeer.net</Link> is not liable or responsible
                for confirming the veracity of any content submitted by users on
                the site, including property prices, plans, locations, opinions,
                and ideas. Additionally, <Link href="/">mydeer.net</Link> is not
                liable for any losses or damages incurred by users. Only a
                platform or channel for communication between property
                suppliers, property seekers, and providers of property or
                related services is offered by <Link href="/">mydeer.net</Link>{" "}
                . Neither the behaviour of any User or Users of the{" "}
                <Link href="/">mydeer.net</Link> service, whether online or
                offline, nor any incorrect or inaccurate content posted on the
                site or in connection with the <Link href="/">mydeer.net</Link>{" "}
                service, whether caused by Users or by any programming or
                equipment related to or used in the service, are under{" "}
                <Link href="/">mydeer.net</Link>'s control.
              </li>
              <li>
                Neither the financial capacity of any user or users of
                <Link href="/">mydeer.net</Link>'s services nor the ownership or
                compliance with the obligations of the buyer and seller of real
                estate or the licensor/lessor and the licensee/lessee are
                guaranteed.
              </li>
              <li>
                Whether caused by a user or users, by any programming or
                equipment connected to or used in the service, or by the actions
                of any user or users of the <Link href="/">mydeer.net</Link>{" "}
                service, whether online or offline,{" "}
                <Link href="/">mydeer.net</Link> disclaims all liability for any
                inaccurate or misleading content posted on the website or in
                connection with the <Link href="/">mydeer.net</Link> service.
              </li>
              <li>
                <Link href="/">mydeer.net</Link> disclaims all liability for any
                mistake, omission, disruption, deletion, flaw, delay in
                operation or transmission, failure of the communications line,
                theft, destruction, or unauthorised access to or modification of
                user data. This includes harm or damage to you, other users, or
                any other person's computer resulting from using or downloading
                content from the <Link href="/">mydeer.net</Link> site or
                service
              </li>
              <li>
                In the event that someone uses the website, the service, or any
                data or content that is placed on the website or sent to
                <Link href="/">mydeer.net</Link> users,{" "}
                <Link href="/">mydeer.net</Link> will not be held liable for any
                loss or harm. In no way should the exchange of any information
                or messages via <Link href="/">mydeer.net</Link> be interpreted
                as an offer or suggestion from <Link href="/">mydeer.net</Link>{" "}
                .
              </li>
              <li>
                <Link href="/">mydeer.net</Link> disclaims all liability for any
                harm or loss to any person resulting from or following
                relationships formed via the use of{" "}
                <Link href="/">mydeer.net</Link> . Both the service and the
                website are offered "as-is". <Link href="/">mydeer.net</Link>{" "}
                does not promise or guarantee any certain outcome from using the
                website or <Link href="/">mydeer.net</Link> service.
              </li>
              <li>
                You use the service at your own risk and judgement, and you are
                entirely liable for any data loss or computer system damage that
                arises from downloading any content.
              </li>
              <li>
                Any liability resulting from or related to the services provided
                by <Link href="/">mydeer.net</Link> is disclaimed by{" "}
                <Link href="/">mydeer.net</Link>{" "}
              </li>
              <li>
                The User is responsible for making sure that all current and
                applicable laws, rules, and regulations, whether directly or
                indirectly related to the use of systems, services, or
                equipment, are strictly followed at all times when using the
                Service. The Company will not be held responsible in any way for
                any kind of default on the part of the User or Users.
              </li>
              <li>
                <Link href="/">mydeer.net</Link> will not be held accountable
                for any information disclosed about the user's account or
                personal information, nor will it be held accountable for any
                errors, omissions, or inaccuracies in such information.
                Furthermore, <Link href="/">mydeer.net</Link> will not be held
                responsible for any losses or damages resulting from such
                disclosure, whether deliberate or unintentional.
              </li>
              <li>
                The company makes no guarantees that{" "}
                <Link href="/">mydeer.net</Link> or any of the websites that are
                linked to it will be free of errors or operational dangers, or
                that it will be free of any harmful components, such as viruses
                or bugs
              </li>
              <li>
                <Link href="/">mydeer.net</Link> may only serve as an
                information source and not be involved in any transactions
                between users of our website. There are dangers, which
                theUser/Users assume when dealing with people who might be
                acting under false pretences and the same shall be borne by the
                User/Users. <Link href="/">mydeer.net</Link> does not screen,
                censor, or otherwise control its service users; the website is
                merely a platform and does not screen, censor, or otherwise
                control the listings that are made available to other
                users.{" "}
              </li>
            </ul>

            <p className="mb-4">
              <Link href="/">mydeer.net</Link> has no responsibility for the
              conduct of users on this website. Whether or if users of{" "}
              <Link href="/">mydeer.net</Link> will accomplish the transactions
              they mention on our site is beyond our control. It is crucial that
              the user exercise caution when interacting with other users on
              this website. Except for records that expire or at its own
              discretion.
            </p>
            <p>
              <Link href="/">mydeer.net</Link> does not accept or assume
              responsibility for the context or content of user comment
              sections. Additionally, it will not modify or remove postings to
              public comment areas once they are posted into the service. It
              also does not accept or assume responsibility for the context or
              content of the user comment areas.
            </p>
          </section>
        </div>
        {/* sticky navigation*/}
        <div className="sticky top-20 hidden max-h-[500px] max-w-full flex-col gap-4 overflow-y-auto rounded-lg bg-text-30 px-3 py-6 shadow-[0_1px_4px_1px_rgba(148,148,148,0.06)] md:flex md:w-[38%] lg:top-28 lg:gap-6 xl:gap-8 xl:px-6 xl:py-8 2xl:p-10">
          <div className="font-airbnb_w_md text-sm font-medium text-text-300 lg:text-base 2xl:text-lg">
            On This Page
          </div>

          <div className="space-y-2 lg:space-y-3 2xl:space-y-4">
            {terms.map((term) => (
              <div
                key={term.id}
                className="font-airbnb_w_bk text-base font-normal text-text-400 lg:text-lg 2xl:text-xl"
              >
                <Link href={`#${term.id}`}>{term.name}</Link>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default Disclaimer;
