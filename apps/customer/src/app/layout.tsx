import "@repo/ui/globals.css";

import type { Metadata } from "next";

import { TRPCReactProvider } from "~/trpc/react";
import ClientSessionProvider from "./client-session-provider";
import { auth } from "@repo/customer-auth";

import localFont from "next/font/local";
import Header from "./components/header/header";
import { Toaster } from "@repo/ui/components/ui/sonner";
import Footer from "./components/footer/footer";
import PropertyDialogProvider from "./components/shared/property-dialog-provider";
import { GoogleMapsProvider } from "@repo/ui/components/context/google-maps-provider";
import { env } from "~/env";
import TopNav from "./components/header/top-nav";
import AgentProfileDialogprovider from "./components/shared/agent-dialog-provider";

export const metadata: Metadata = {
  title: "My Deer",
  description: "Find your dream home with My Deer",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const airbnb_w_bd = localFont({
  src: "./fonts/AirbnbCereal_W_Bd.otf",
  variable: "--font-airbnb-w-bd",
});

const airbnb_w_bk = localFont({
  src: "./fonts/AirbnbCereal_W_Bk.otf",
  variable: "--font-airbnb-w-bk",
});

const airbnb_w_blk = localFont({
  src: "./fonts/AirbnbCereal_W_Blk.otf",
  variable: "--font-airbnb-w-blk",
});

const airbnb_w_lt = localFont({
  src: "./fonts/AirbnbCereal_W_Lt.otf",
  variable: "--font-airbnb-w-lt",
});

const airbnb_w_md = localFont({
  src: "./fonts/AirbnbCereal_W_Md.otf",
  variable: "--font-airbnb-w-md",
});

const airbnb_w_xbd = localFont({
  src: "./fonts/AirbnbCereal_W_XBd.otf",
  variable: "--font-airbnb-w-xbd",
});

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await auth();

  return (
    <html
      lang="en"
      className={`${airbnb_w_bd.variable} ${airbnb_w_bk.variable} ${airbnb_w_blk.variable} ${airbnb_w_lt.variable} ${airbnb_w_md.variable} ${airbnb_w_xbd.variable} scroll-smooth font-airbnb_w_md antialiased`}
    >
      <body>
        <TRPCReactProvider>
          <ClientSessionProvider session={session}>
            <GoogleMapsProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
              <TopNav />
              <Header />
              {children}
              <Footer />
              <Toaster richColors />
              <PropertyDialogProvider />
              <AgentProfileDialogprovider />
            </GoogleMapsProvider>
          </ClientSessionProvider>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
