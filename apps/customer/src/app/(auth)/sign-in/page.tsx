"use client";

import React from "react";
import { api } from "~/trpc/react";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { useRouter, useSearchParams } from "next/navigation";
import { CustomerSigninSchema } from "@repo/validators";
import { toast } from "@repo/ui/components/ui/sonner";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { Button } from "@repo/ui/components/ui/button";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

const SigninPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl");
  const { mutate: signIn, isPending } = api.user.signIn.useMutation();

  const form = useForm<z.infer<typeof CustomerSigninSchema>>({
    resolver: zodResolver(CustomerSigninSchema),
    defaultValues: {
      phoneNumber: "",
    },
  });

  const onSubmit = (data: z.infer<typeof CustomerSigninSchema>) => {
    signIn(data, {
      onSuccess: (opts) => {
        if (opts.warning) {
          toast.warning(opts.message);
          return;
        }
        toast.success(opts.message);

        // Create URL for OTP verification with phone number
        const otpUrl = new URLSearchParams();
        otpUrl.set("phoneNumber", data.phoneNumber);

        // Add callback URL if it exists
        if (callbackUrl) {
          otpUrl.set("callbackUrl", callbackUrl);
        }

        router.push(`/otp-verification?${otpUrl.toString()}`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  return (
    <div className="flex flex-col gap-5 md:mx-[74px] md:gap-7 lg:mx-auto lg:gap-8 xl:gap-10 2xl:gap-11">
      {/* title */}
      <div className="flex flex-col items-center justify-center gap-2 lg:gap-3">
        <h1 className="font-airbnb_w_xbd text-2xl font-extrabold text-secondary-2-700 md:text-[28px] lg:text-3xl xl:text-[32px] 2xl:text-[40px]">
          Sign in
        </h1>
        <p className="text-center font-airbnb_w_bk text-sm text-text-600 md:text-base xl:text-lg 2xl:text-2xl">
          Login into your account by entering your registered phone number
        </p>
      </div>

      {/* form */}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-4 md:gap-6"
        >
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem className="md:py-5 lg:py-7 xl:py-8">
                <FormLabel className="text-base text-text-600 md:text-lg">
                  Phone Number
                </FormLabel>
                <FormControl>
                  <Input placeholder="eg: **********" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col items-center justify-center gap-[18px]">
            {isPending ? (
              <LoadingButton
                className="px-[100px] text-base font-medium xl:px-[182px]"
                loading
              >
                Requesting OTP
              </LoadingButton>
            ) : (
              <Button type="submit" className="text-base font-medium">
                <span className="px-[100px] xl:px-[182px]">Get OTP</span>
              </Button>
            )}
            <p className="flex flex-row items-center justify-center gap-4 text-nowrap font-airbnb_w_bk">
              Don't have an account
              <Link
                href="/sign-up"
                className="flex items-center gap-1 font-medium text-secondary-2-700"
              >
                Register now
                <ChevronRight className="size-5" />
              </Link>
            </p>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SigninPage;
