"use client";

import Autoplay from "embla-carousel-autoplay";

import React from "react";
import Image from "next/image";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import { AuthSlides } from "../utils/constants";

const AuthSlider = () => {
  return (
    <Carousel
      opts={{ loop: true }}
      plugins={[Autoplay({ playOnInit: true, delay: 5000 })]}
      className="w-full lg:w-1/2 lg:border-r-2 lg:border-primary-200"
    >
      <CarouselContent className="mb-3">
        {AuthSlides.map((item, index) => (
          <CarouselItem
            key={index}
            className="flex flex-col items-center justify-center gap-4"
          >
            <div className="relative aspect-square w-[305px] lg:w-[316px] xl:w-[392px] 2xl:w-[523px]">
              <Image
                src={item.url}
                alt="login-image"
                className="relative object-contain"
                fill={true}
              />
            </div>
            <div className="flex flex-col items-center justify-center gap-2">
              <h1 className="font-airbnb_w_xbd text-[22px] font-extrabold text-primary-2-700 lg:leading-[29px] xl:text-[28px] xl:leading-[36px] 2xl:text-[37px] 2xl:leading-[48px]">
                {item.heading}
              </h1>
              <p className="text-center font-airbnb_w_bk text-text-700 md:text-sm md:leading-5 lg:text-sm 2xl:text-2xl 2xl:leading-[37px]">
                {item.subHeading}
              </p>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  );
};

export default React.memo(AuthSlider);
