import React from "react";
import AuthSlider from "./auth-slider";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Authentication - My Deer",
  description: "Sign in or create an account on My Deer",
};
import { auth } from "@repo/customer-auth";
import { redirect } from "next/navigation";

const AuthLayout = async ({ children }: { children: React.ReactNode }) => {
  const session = await auth();
  if (session?.user) {
    redirect("/");
  }

  return (
    <main className="bg-[linear-gradient(180deg,_#FFFFFF_58.27%,_#E4CBBD_130.49%)]">
      <div className="container mx-auto flex max-w-full flex-col-reverse items-center py-8 md:py-11 lg:flex-row lg:gap-[50px] lg:py-12 xl:gap-[60px] xl:py-[60px] 2xl:gap-[70px]">
        <AuthSlider />
        {children}
      </div>
    </main>
  );
};

export default AuthLayout;
