"use client";

import { ChevronLeft } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";

import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@repo/ui/components/ui/input-otp";
import { Button } from "@repo/ui/components/ui/button";
import { signIn } from "next-auth/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import type { TStatus } from "~/app/types";
import { api } from "~/trpc/react";

const OtpVerificationPage = () => {
  const router = useRouter();
  const [status, setStatus] = useState<TStatus>("idle");
  const [otp, setOtp] = useState<string>("");
  const searchParams = useSearchParams();
  const phoneNumber = searchParams.get("phoneNumber");
  const callbackUrl = searchParams.get("callbackUrl");

  const { mutate: resendOtp } = api.user.resendOtp.useMutation();

  if (!phoneNumber) {
    return <div>Phone Number not present</div>;
  }

  const onSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (otp.length === 0 || otp.length < 4 || otp.length > 4) {
      return toast.error("Invalid OTP");
    }

    try {
      setStatus("loading");
      const resp = await signIn("credentials", {
        phoneNumber: phoneNumber,
        otp: otp,
        redirect: false,
      });

      if (resp?.error) {
        toast.error("Invalid OTP");
        return;
      }

      if (resp?.ok && resp.status === 200) {
        toast.success("Great to see you again !");

        // Redirect to callback URL if present, otherwise go to home
        if (callbackUrl) {
          router.push(decodeURIComponent(callbackUrl));
        } else {
          router.push("/");
        }
      }
      setStatus("idle");
    } catch (err) {
      console.log(err);
      setStatus("error");
    } finally {
      setStatus("idle");
    }
  };

  const retryOtp = () => {
    resendOtp(
      { phoneNumber: phoneNumber },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <div className="flex w-full flex-col gap-5 md:gap-7 lg:gap-8 xl:gap-10">
      {/* back and change number */}
      <div className="flex flex-row justify-between">
        <p
          onClick={() => router.back()}
          className="flex cursor-pointer flex-row items-center gap-2 text-sm text-text-500 md:text-base"
        >
          <ChevronLeft className="size-5" /> Back
        </p>
        <p
          onClick={() => router.back()}
          className="cursor-pointer text-sm font-medium text-text-500 md:text-base"
        >
          Change Number
        </p>
      </div>

      {/*  title */}
      <div className="flex flex-col items-center justify-center gap-2 xl:gap-3 2xl:gap-[18px]">
        <h1 className="font-airbnb_w_xbd text-2xl font-extrabold text-secondary-2-700 md:text-[28px] lg:text-3xl xl:text-[32px] 2xl:text-[40px]">
          Enter Verification Code
        </h1>
        <p className="text-center font-airbnb_w_bk text-sm text-text-600 md:max-w-[512px] md:text-base xl:text-lg 2xl:max-w-full 2xl:text-2xl">
          Enter the 4 digit otp that has been sent to your phone number{" "}
          <span className="font-semibold"> +91 {phoneNumber}</span> via message
          or sms
        </p>
      </div>

      {/* form */}
      <form onSubmit={onSubmit} className="flex flex-col gap-4">
        {/* input fields */}
        <div className="flex items-center justify-center">
          <InputOTP
            maxLength={4}
            value={otp}
            onChange={(value) => setOtp(value)}
          >
            <InputOTPGroup className="space-x-4 sm:space-x-8">
              <InputOTPSlot
                index={0}
                className="size-[50px] border-text-100 md:size-[64px] lg:size-16 xl:size-[82px] xl:text-[32px]"
              />
              <InputOTPSlot
                index={1}
                className="size-[50px] border-text-100 md:size-[64px] lg:size-16 xl:size-[82px] xl:text-[32px]"
              />
              <InputOTPSlot
                index={2}
                className="size-[50px] border-text-100 md:size-[64px] lg:size-16 xl:size-[82px] xl:text-[32px]"
              />
              <InputOTPSlot
                index={3}
                className="size-[50px] border-text-100 md:size-[64px] lg:size-16 xl:size-[82px] xl:text-[32px]"
              />
            </InputOTPGroup>
          </InputOTP>
        </div>

        {/* buttons */}
        <div className="flex flex-col items-center justify-center gap-2 xl:gap-[18px]">
          <Button
            variant="link"
            type="button"
            className="w-[332px]  text-base text-secondary-2-700 hover:bg-secondary-2-100 lg:px-[110px] lg:text-lg xl:px-[110px] xl:text-lg"
            onClick={retryOtp}
          >
            Resend OTP
          </Button>
          {status === "loading" ? (
            <LoadingButton className="text-base" loading>
              <span className="w-[332px]">Verifying</span>
            </LoadingButton>
          ) : (
            <Button className="w-[332px] text-base lg:text-lg" type="submit">
              Verify
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default OtpVerificationPage;
