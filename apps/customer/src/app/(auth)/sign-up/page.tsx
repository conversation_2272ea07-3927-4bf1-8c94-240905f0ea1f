"use client";

import React, { useCallback, useEffect } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";

import { toast } from "@repo/ui/components/ui/sonner";
import { Button } from "@repo/ui/components/ui/button";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";

import { CustomerSignupSchema } from "@repo/validators";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { api } from "~/trpc/react";
import { useRouter } from "next/navigation";

const SignupPage = () => {
  const router = useRouter();
  const { mutate: signUp, isPending } = api.user.signUp.useMutation();

  const form = useForm<z.infer<typeof CustomerSignupSchema>>({
    resolver: zodResolver(CustomerSignupSchema),
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
    },
  });

  // below two functions to format the adhaar in such a way like xxxx xxxx xxxx visually adds space for better user experience.
  const formatAadhaar = useCallback((value: string | undefined) => {
    const cleaned = value?.replace(/\s/g, "");
    const formatted = cleaned?.replace(/(\d{4})/g, "$1 ").trim();
    return formatted;
  }, []);
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value.replace(/\D/g, "");
      const truncated = rawValue.slice(0, 12);
      if (truncated === "") {
        form.setValue("adharcardNumber", undefined);
      } else {
        const formatted = formatAadhaar(truncated);
        if (formatted) {
          e.target.value = formatted;
        }
        form.setValue("adharcardNumber", truncated);
      }
    },
    [form, formatAadhaar],
  );

  const adharcardField = form.watch("adharcardNumber");

  useEffect(() => {
    if (adharcardField === "") {
      form.setValue("adharcardNumber", undefined);
    }
  }, [adharcardField, form]);

  const onSubmit = async (data: z.infer<typeof CustomerSignupSchema>) => {
    signUp(data, {
      onSuccess: (opts) => {
        if (opts.warning) {
          toast.warning(opts.message);
          return;
        }

        toast.success(opts.message);
        router.push(`/otp-verification?phoneNumber=${data.phoneNumber}`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  return (
    <div className="flex w-full flex-col gap-5 md:gap-7 lg:gap-8 xl:gap-10 2xl:gap-11">
      {/* title and desc */}
      <div className="flex flex-col items-center justify-center gap-2 md:items-start lg:gap-3 2xl:gap-[18px]">
        <h1 className="font-airbnb_w_xbd text-2xl font-extrabold text-secondary-2-700 md:text-[28px] lg:text-3xl xl:text-[32px] 2xl:text-[40px]">
          Sign Up
        </h1>
        <p className="font-airbnb_w_bk text-sm text-text-600 md:text-base lg:text-lg 2xl:text-2xl">
          Create account to continue with My Deer
        </p>
      </div>

      {/* form */}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-4 lg:gap-[18px] xl:gap-6"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base text-text-600 lg:text-lg">
                  Name
                </FormLabel>
                <FormControl>
                  <Input placeholder="eg: john doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex flex-col gap-4 md:flex-row lg:flex-col xl:flex-row 2xl:gap-6">
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel className="text-base text-text-600 lg:text-lg">
                    Phone Number
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 9898989898" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel className="text-base text-text-600 lg:text-lg">
                    Email
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="eg: <EMAIL>"
                      value={field.value}
                      onChange={(e) =>
                        field.onChange(e.target.value.toLowerCase())
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="adharcardNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base text-text-600 lg:text-lg">
                  Adhar Card Number (optional)
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="eg: xxxx xxxx xxxx"
                    {...field}
                    value={field.value ? formatAadhaar(field.value) : ""}
                    onChange={handleChange}
                    maxLength={14}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex flex-col items-center justify-center gap-[18px] lg:gap-4">
            {isPending ? (
              <LoadingButton
                className="px-[100px] text-base font-medium sm:px-[143px] xl:px-[182px]"
                loading
              >
                Requesting OTP
              </LoadingButton>
            ) : (
              <Button type="submit" className="text-base font-medium">
                <span className="px-[100px] sm:px-[143px] lg:px-[85px] xl:px-[182px]">
                  Get OTP
                </span>
              </Button>
            )}
            <p className="flex flex-row items-center justify-center gap-4 font-airbnb_w_bk">
              Already have a account?
              <Link
                href="/sign-in"
                className="flex items-center gap-1 font-medium text-secondary-2-700 lg:text-lg"
              >
                Login
                <ChevronRight className="size-5" />
              </Link>
            </p>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SignupPage;
