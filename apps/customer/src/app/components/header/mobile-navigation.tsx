"use client";

import React, { useRef, useState } from "react";

import {
  Sheet,
  She<PERSON><PERSON><PERSON>,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@repo/ui/components/ui/sheet";
import { Plus, Search, X } from "lucide-react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import {
  NavLinks,
  ProfileSidebarReferralsAndFeedbackLinks,
  ProfileSidebarReportAndHelpLinks,
  ProfileSidebarSettingsLinks,
  ProfileSidebarTermsAndConditionsLinks,
} from "~/app/utils/constants";
import NavLink from "@repo/ui/components/shared/nav-link";
import type { INavLinkProps } from "@repo/ui/components/shared/nav-link";
import ProfileSideBar from "@repo/ui/components/shared/profile-side-bar";
import Link from "next/link";
import { Button } from "@repo/ui/components/ui/button";
import LogoutButton from "../shared/logout-button";
import ProfileCard from "../shared/profile-card";
import { useSearchParams } from "next/navigation";
import { env } from "~/env";
import { instantMeiliSearch } from "@meilisearch/instant-meilisearch";
import {
  HitsPerPageProps,
  HitsProps,
  InfiniteHits,
  InstantSearch,
  SearchBox,
  useSearchBox,
} from "react-instantsearch";
import { useRouter } from "next/navigation";
import type { Property, User } from "@repo/database";
import { useOnClickOutside } from "usehooks-ts";
import DeleteButton from "../shared/delete-button";

const NavbarNavLink = ({ children, ...props }: INavLinkProps) => {
  return (
    <NavLink
      className="rounded-md font-airbnb_w_bk text-text-500"
      activeClassName="font-airbnb_w_md rounded-[12px] bg-secondary-2-100 text-secondary-2-700 px-2 py-[6px]"
      {...props}
    >
      {children}
    </NavLink>
  );
};

const { searchClient } = instantMeiliSearch(
  env.NEXT_PUBLIC_MEILI_SEARCH_URL,
  env.NEXT_PUBLIC_MEILI_SEARCH_KEY,
  { placeholderSearch: false, keepZeroFacets: true, finitePagination: false },
);

const SearchBar = ({
  setSearchSheetOpen,
}: {
  setSearchSheetOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const router = useRouter();
  const inputRef = useRef<HTMLFormElement | null>(null);
  const resultRef = useRef(null);

  const SearchComponent = () => {
    const { refine, query } = useSearchBox();
    const handleClickOutside = () => {
      // Your custom logic here
      refine("");
      //   console.log("clicked outside");
    };
    // TODO: fix this and remove the ts-ignore
    // @ts-expect-error : implemented as done in official docs  */
    useOnClickOutside(resultRef, handleClickOutside);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Check if the pressed key is Enter
      if (e.key === "Enter" && inputRef.current !== null) {
        console.log("when e.key === Enter && inputRef.current !== null");
        // Get the input element
        const inputElement = inputRef.current.querySelector("input");

        // Check if input value exists and is not empty
        if (inputElement && inputElement.value.length > 0) {
          console.log("when inputElement && inputElement.value.length > 0 ");
          // Navigate to search page
          router.push(`/search/?query=${inputElement.value}`);

          // Clear the input value
          inputElement.value = "";

          // Reset the query in InstantSearch
          refine("");
          setSearchSheetOpen && setSearchSheetOpen(false);
        }
      }
    };

    return (
      <SearchBox
        formRef={inputRef}
        classNames={{
          input: `flex w-full p-2 px-3 items-center gap-2 rounded-lg border border-[#F4F0EE] bg-white py-2 px-3 pl-10 `,
          submitIcon: "absolute left-2.5 top-1/2 size-5 -translate-y-1/2",
          form: "flex ",
          resetIcon: "hidden",
        }}
        className=""
        placeholder="Search for something"
        onKeyDown={handleKeyDown}
      />
    );
  };

  return (
    <div className="relative z-20">
      <InstantSearch
        indexName={env.NEXT_PUBLIC_PARTNER_INDEX}
        /* @ts-expect-error : implemented as done in official docs  */
        searchClient={searchClient}
      >
        <SearchComponent />
        <InfiniteHits
          ref={resultRef}
          classNames={{
            loadMore: "hidden",
            item: "hover:bg-secondary-2-500 w-full py-2 px-1 rounded-xl border-b-2",
          }}
          showPrevious={false}
          className="absolute left-0 w-full rounded-2xl bg-white/90 bg-opacity-40 shadow-[0px_1px_8px_0px_rgba(168,137,121,0.2)] backdrop-blur-3xl"
          hitComponent={Hit}
        />
      </InstantSearch>
    </div>
  );
};

type Hit = Pick<User, "id" | "name" | "filePublicUrl" | "userLocation"> & {
  mediaSections: {
    media: {
      filePublicUrl: string;
    }[];
  }[];
};

const Hit = ({ hit }: { hit: Hit }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  return (
    <div
      onClick={() => {
        const params = new URLSearchParams(searchParams);
        params.set("viewAgentId", hit.id);
        router.push(`?${params}`);
      }}
      className="relative z-[12] flex w-full cursor-pointer items-center gap-4"
    >
      <div className="relative aspect-square min-w-10">
        <Image
          src={hit.filePublicUrl ?? "/images/placeholder-user-image.jpg"}
          fill
          className="relative rounded-xl object-cover"
          alt={hit.name}
        />
      </div>
      <div>
        <p>{hit.name}</p>
        <p className="line-clamp-2 text-xs text-text-600">{hit.userLocation}</p>
      </div>
    </div>
  );
};

const MobileNavigation = () => {
  const [searchSheetOpen, setSearchSheetOpen] = useState(false);
  const [open, setOpen] = useState<boolean>(false);
  const [openSidebar, setOpenSidebar] = useState(false);
  const { status } = useSession();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);

  const handlePropertyEnquiryForm = () => {
    params.set("propertyEnquiryForm", "true");
    window.history.pushState(null, "", `?${params.toString()}`);
  };

  return (
    <div className="flex items-center gap-4">
      {/* search for mobile */}
      {status === "authenticated" && (
        <Sheet open={searchSheetOpen} onOpenChange={setSearchSheetOpen}>
          <SheetTrigger className="lg:hidden" asChild>
            <Search
              strokeWidth={1.5}
              className="size-5 text-[#5F2800] lg:size-6 xl:size-[30px]"
            ></Search>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader className="">
              <SheetTitle className="mb-4 text-center">Search</SheetTitle>
            </SheetHeader>
            <SearchBar setSearchSheetOpen={setSearchSheetOpen}></SearchBar>
          </SheetContent>
        </Sheet>
      )}

      {status === "authenticated" ? (
        <Sheet open={openSidebar} onOpenChange={setOpenSidebar}>
          <SheetTrigger asChild>
            <button className="flex items-center gap-1 rounded-xl bg-[#FFFEFD] px-1 py-[4px] text-sm font-medium text-nohighlight sm:text-base">
              <Image
                src="/icons/user.svg"
                alt="icon"
                height={50}
                width={50}
                className="size-5"
              />
              Profile
            </button>
          </SheetTrigger>
          <SheetContent className="h-full min-h-full overflow-y-scroll">
            <ProfileSideBar
              open={openSidebar}
              setOpen={setOpenSidebar}
              profileCard={
                <ProfileCard open={openSidebar} setOpen={setOpenSidebar} />
              }
              logoutButton={<LogoutButton />}
              linksArray1={ProfileSidebarSettingsLinks}
              linksArray2={ProfileSidebarReportAndHelpLinks}
              linksArray3={ProfileSidebarReferralsAndFeedbackLinks}
              //   linksArray4={ProfileSidebarTermsAndConditionsLinks}
              deleteAccount={<DeleteButton></DeleteButton>}
            />
          </SheetContent>
        </Sheet>
      ) : (
        <Button
          className="cursor-pointer px-6 py-2.5 font-airbnb_w_md text-xs font-medium sm:text-sm"
          asChild
        >
          <Link href="/sign-in">Login</Link>
        </Button>
      )}

      {/* enquiry button */}
      {status === "authenticated" && (
        <Button
          onClick={() => handlePropertyEnquiryForm()}
          className="flex items-center gap-[2px] p-2 sm:px-4 sm:py-3"
        >
          <Plus className="size-4" /> <span>Enquiry</span>
        </Button>
      )}

      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <div className="relative aspect-square size-6 sm:size-8 lg:hidden">
            <Image
              src="/icons/hamburger.svg"
              alt="hamburger-menu"
              fill
              className="relative object-cover"
            ></Image>
          </div>
        </SheetTrigger>
        <SheetContent className="overflow-y-auto px-4 py-8">
          <SheetClose className="absolute left-4 top-8 size-[26px]">
            <X />
          </SheetClose>
          <ul className="flex flex-col items-center gap-5 pb-6 pt-[50px]">
            {NavLinks.map((item, idx) => (
              <li key={`${item.text}-${idx + 1}`}>
                <NavbarNavLink href={item.link} onClick={() => setOpen(!open)}>
                  {item.text}
                </NavbarNavLink>
              </li>
            ))}
          </ul>
          <div className="relative mx-auto size-[26px] h-[84.28px] w-[105px]">
            <Image src={"/logo.svg"} alt="logo" fill={true} />
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default MobileNavigation;
