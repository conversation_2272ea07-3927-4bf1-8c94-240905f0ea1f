import React from "react";
import Image from "next/image";
import Link from "next/link";
import MobileNavigation from "./mobile-navigation";
import DesktopNavigation from "./desktop-navigation";
import { api } from "~/trpc/server";
import PropertyEnquiryForm from "./property-enquiry-form";
import { auth } from "@repo/customer-auth";

const Header = async () => {
  const session = await auth();
  const propertyCategories = session?.user
    ? await api.user.getPropertyCategories()
    : [];

  const cities = session?.user ? await api.user.getCities() : [];

  return (
    <nav className="sticky left-0 top-0 z-50 bg-primary-0 bg-opacity-40 shadow-[0px_1px_8px_0px_rgba(168,137,121,0.2)] backdrop-blur-[54.7px]">
      <div className="container mx-auto flex max-w-full items-center justify-between py-[10px] lg:px-[50px] lg:py-[12px] xl:px-[80px] xl:py-4 2xl:gap-[84px]">
        <Link
          href="/"
          className="relative aspect-[5/3] w-[77px] lg:w-[80px] xl:w-[115px] "
        >
          <Image
            src="/logo.svg"
            fill
            alt="logo-main"
            className="relative object-cover"
          />
        </Link>

        <div className="flex items-center gap-2 sm:gap-[18px] xl:gap-5 2xl:gap-6">
          {/* Visible on Desktop Screens Only !  */}
          <div className="hidden lg:block">
            <DesktopNavigation />
          </div>

          {/* Visible on Mobile Screens Only ! */}
          <div className="block lg:hidden">
            <MobileNavigation />
          </div>
        </div>
      </div>

      {session?.user && (
        <PropertyEnquiryForm
          propertyCategories={propertyCategories}
          cities={cities}
        />
      )}
    </nav>
  );
};

export default Header;
