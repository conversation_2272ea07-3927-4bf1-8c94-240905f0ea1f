"use client";

import React, { useRef, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";

import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@repo/ui/components/ui/sheet";

import ProfileSideBar from "@repo/ui/components/shared/profile-side-bar";
import type { INavLinkProps } from "@repo/ui/components/shared/nav-link";
import NavLink from "@repo/ui/components/shared/nav-link";
import DeleteButton from "../shared/delete-button";

import {
  NavLinks,
  ProfileSidebarReferralsAndFeedbackLinks,
  ProfileSidebarReportAndHelpLinks,
  ProfileSidebarSettingsLinks,
} from "~/app/utils/constants";
import LogoutButton from "../shared/logout-button";
import { Button } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import ProfileCard from "../shared/profile-card";
import { useSearchParams } from "next/navigation";
import { env } from "~/env";
import { instantMeiliSearch } from "@meilisearch/instant-meilisearch";
import { useRouter } from "next/navigation";
import {
  InfiniteHits,
  InstantSearch,
  SearchBox,
  useSearchBox,
} from "react-instantsearch";
import type { User } from "@repo/database";
import { useOnClickOutside } from "usehooks-ts";

const { searchClient } = instantMeiliSearch(
  env.NEXT_PUBLIC_MEILI_SEARCH_URL,
  env.NEXT_PUBLIC_MEILI_SEARCH_KEY,
  { placeholderSearch: false, keepZeroFacets: true, finitePagination: false },
);

const SearchBar = ({
  setSearchSheetOpen,
}: {
  setSearchSheetOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const router = useRouter();
  const inputRef = useRef<HTMLFormElement | null>(null);
  const resultRef = useRef(null);

  const SearchComponent = () => {
    const { refine, query } = useSearchBox();
    const handleClickOutside = () => {
      // Your custom logic here
      refine("");
      //   console.log("clicked outside");
    };
    // TODO: fix this and remove the ts-ignore
    // @ts-expect-error : implemented as done in official docs  */
    useOnClickOutside(resultRef, handleClickOutside);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Check if the pressed key is Enter
      if (e.key === "Enter" && inputRef.current !== null) {
        console.log("when e.key === Enter && inputRef.current !== null");
        // Get the input element
        const inputElement = inputRef.current.querySelector("input");

        // Check if input value exists and is not empty
        if (inputElement && inputElement.value.length > 0) {
          console.log("when inputElement && inputElement.value.length > 0 ");
          // Navigate to search page
          router.push(`/search/?query=${inputElement.value}`);

          // Clear the input value
          inputElement.value = "";

          // Reset the query in InstantSearch
          refine("");
          setSearchSheetOpen && setSearchSheetOpen(false);
        }
      }
    };

    return (
      <SearchBox
        formRef={inputRef}
        classNames={{
          input: `flex w-full p-2 px-3 items-center gap-2 rounded-lg border border-[#F4F0EE] bg-white py-2 px-3 pl-10 `,
          submitIcon: "absolute left-2.5 top-1/2 size-5 -translate-y-1/2",
          form: "flex ",
          resetIcon: "hidden",
        }}
        className=""
        placeholder="Search for something"
        onKeyDown={handleKeyDown}
      />
    );
  };

  return (
    <div className="relative z-20">
      <InstantSearch
        indexName={env.NEXT_PUBLIC_PARTNER_INDEX}
        /* @ts-expect-error : implemented as done in official docs  */
        searchClient={searchClient}
      >
        <SearchComponent />
        <InfiniteHits
          ref={resultRef}
          classNames={{
            loadMore: "hidden",
            item: "hover:bg-secondary-2-500 w-full py-2 px-1 rounded-xl border-b-2",
          }}
          showPrevious={false}
          className="absolute left-0 w-full rounded-2xl bg-white/90 bg-opacity-40 shadow-[0px_1px_8px_0px_rgba(168,137,121,0.2)] backdrop-blur-3xl"
          hitComponent={Hit}
        />
      </InstantSearch>
    </div>
  );
};

type Hit = Pick<User, "id" | "name" | "filePublicUrl" | "userLocation"> & {
  mediaSections: {
    media: {
      filePublicUrl: string;
    }[];
  }[];
};

const Hit = ({ hit }: { hit: Hit }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  return (
    <div
      onClick={() => {
        const params = new URLSearchParams(searchParams);
        params.set("viewAgentId", hit.id);
        router.push(`?${params}`);
      }}
      className="relative z-[12] flex w-full cursor-pointer items-center gap-4"
    >
      <div className="relative aspect-square min-w-10">
        <Image
          src={hit.filePublicUrl ?? "/images/placeholder-user-image.jpg"}
          fill
          className="relative rounded-xl object-cover"
          alt={hit.name}
        />
      </div>
      <div>
        <p>{hit.name}</p>
        <p className="line-clamp-2 text-xs text-text-600">{hit.userLocation}</p>
      </div>
    </div>
  );
};

const DesktopNavLink = ({ children, ...props }: INavLinkProps) => {
  return (
    <NavLink
      className="px-2 py-[6px] font-airbnb_w_bk font-normal text-text-500 md:text-sm lg:text-base xl:px-3 xl:text-lg"
      activeClassName="font-medium text-secondary-2-700 bg-secondary-2-100 rounded-[12px] font-airbnb_w_md"
      {...props}
    >
      {children}
    </NavLink>
  );
};

const DesktopNavigation = () => {
  const { data: session, status } = useSession();
  const [openSidebar, setOpenSidebar] = useState(false);

  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);

  const handlePropertyEnquiryForm = () => {
    params.set("propertyEnquiryForm", "true");
    window.history.pushState(null, "", `?${params.toString()}`);
  };

  return (
    <div className="flex items-center justify-between lg:gap-5 xl:gap-[30px]">
      {/* desktop search only for authenticated users*/}
      {status === "authenticated" && session.user && (
        <div className="hidden md:block">
          <SearchBar></SearchBar>
        </div>
      )}

      <ul className="flex items-center justify-center gap-3">
        {NavLinks.map((item, idx) => (
          <li key={`${item.text}-${idx + 1}`}>
            <DesktopNavLink href={item.link} prefetch={true}>
              {item.text}
            </DesktopNavLink>
          </li>
        ))}
      </ul>

      <div className="flex items-center gap-4 xl:gap-5 2xl:gap-6">
        {status === "authenticated" && session.user ? (
          <Sheet open={openSidebar} onOpenChange={setOpenSidebar}>
            <SheetTrigger asChild>
              <button className="flex items-center gap-1 rounded-xl bg-[#FFFEFD] px-2 py-[6px] font-medium text-nohighlight">
                <Image
                  src="/icons/user.svg"
                  alt="icon"
                  height={50}
                  width={50}
                  className="size-4"
                />
                Profile
              </button>
            </SheetTrigger>
            <SheetContent className="h-full min-h-full overflow-y-scroll">
              <ProfileSideBar
                open={openSidebar}
                setOpen={setOpenSidebar}
                profileCard={
                  <ProfileCard open={openSidebar} setOpen={setOpenSidebar} />
                }
                logoutButton={<LogoutButton />}
                linksArray1={ProfileSidebarSettingsLinks}
                linksArray2={ProfileSidebarReportAndHelpLinks}
                linksArray3={ProfileSidebarReferralsAndFeedbackLinks}
                // linksArray4={ProfileSidebarTermsAndConditionsLinks}
                deleteAccount={<DeleteButton />}
              />
            </SheetContent>
          </Sheet>
        ) : (
          <div className="flex flex-row gap-4">
            <Link
              href="/sign-in"
              className="cursor-pointer font-airbnb_w_md text-lg font-normal text-secondary-2-700 xl:text-lg"
            >
              Login
            </Link>
            <Link
              href="/sign-up"
              className="cursor-pointer font-airbnb_w_md text-lg font-normal text-secondary-2-700 xl:text-lg"
            >
              Signup
            </Link>
          </div>
        )}
      </div>

      {/* enquiry button */}
      {status === "authenticated" && (
        <Button
          onClick={() => handlePropertyEnquiryForm()}
          className="mr-4 flex items-center gap-3 px-5"
        >
          <Plus className="size-5" /> <span>Enquiry</span>
        </Button>
      )}
    </div>
  );
};

export default DesktopNavigation;
