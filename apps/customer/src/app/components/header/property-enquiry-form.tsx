"use client";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { DualRangeSlider } from "@repo/ui/components/ui/dual-range-slider";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@repo/ui/components/ui/sheet";
import { PossessionStateEnum } from "@repo/database";
import CustomBadge from "./custom-badge";

import { Slider } from "@repo/ui/components/ui/slider";

import { Textarea } from "@repo/ui/components/ui/textarea";
import { Button } from "@repo/ui/components/ui/button";
import type { TPropertyCategories } from "~/app/types";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { CustomerPropertyEnquirySchema } from "@repo/validators";
import { skipToken } from "@tanstack/react-query";
import { SliderThumb } from "@radix-ui/react-slider";
const PropertyQuestion = ({ question }: { question: string }) => {
  return (
    <>
      <div className="text-start font-airbnb_w_md text-sm font-medium leading-[30px] text-text-600 md:text-base xl:text-xl">
        {question}
      </div>
    </>
  );
};

const PropertyEnquiryForm = ({
  propertyCategories,
  cities,
}: {
  propertyCategories: TPropertyCategories[];
  cities: TPropertyCategories[];
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const form = useForm<z.infer<typeof CustomerPropertyEnquirySchema>>({
    defaultValues: {
      propertyPrice: [0, 100000],
    },
    resolver: zodResolver(CustomerPropertyEnquirySchema),
  });

  const { mutate: createProfile } =
    api.user.createPropertyEnquiry.useMutation();

  const parCategId = searchParams.get("propertyCategoryId");

  const onSubmit = (data: z.infer<typeof CustomerPropertyEnquirySchema>) => {
    const params = new URLSearchParams(searchParams);

    createProfile(data, {
      onSuccess: (opts) => {
        if (opts.warning) {
          toast.warning(opts.message);
          return;
        }
        toast.success(opts.message);
        form.reset();
        params.delete("propertyCategoryId");
        params.delete("propertyEnquiryForm");
        router.replace(`?${params.toString()}`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  useEffect(() => {
    if (parCategId) {
      form.setValue("propertyCategoryId", parCategId);
    }
  }, [form, parCategId]);

  const { data: propertyTypes } = api.user.getPropertyTypes.useQuery(
    parCategId
      ? {
          id: parCategId,
        }
      : skipToken,
  );

  const handlePropertyCategory = (value: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("propertyCategoryId", value);
    router.push(`${pathname}?${params.toString()}`);
    form.setValue("propertyCategoryId", value);
    form.setValue("propertyType", "");
  };

  const handlePropertyType = (item: string) => {
    form.setValue("propertyType", item);
  };
  const handlePossessionProperty = (item: PossessionStateEnum) => {
    form.setValue("propertyPossession", item);
  };
  const closeSheet = () => {
    const params = new URLSearchParams(searchParams);
    params.delete("propertyEnquiryForm");
    params.delete("propertyCategoryId");
    form.reset();
    router.replace(`?${params.toString()}`);
  };

  const isSheetOpen = searchParams.get("propertyEnquiryForm");

  const PossessionPropertyOptions = Object.keys(PossessionStateEnum).map(
    (key) => ({
      label: key,
      value: PossessionStateEnum[key as keyof typeof PossessionStateEnum],
    }),
  );

  return (
    <>
      <Sheet open={!!isSheetOpen}>
        <SheetContent
          side={"properyEnquiry"}
          className="w-full overflow-hidden overflow-y-auto rounded-bl-2xl rounded-tl-2xl p-0 sm:max-w-[430px] md:max-w-[614px] lg:max-w-[819px] xl:max-w-[1065px] 2xl:max-w-[1536px]"
        >
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="overflow-hidden pb-20 md:pb-36 lg:pb-40"
            >
              <SheetHeader>
                <SheetTitle className="flex items-center justify-between bg-text-30 px-[20px] py-3 md:px-[80px] md:py-4 lg:rounded-tl-[16px]">
                  <div onClick={() => closeSheet()} className="cursor-pointer">
                    <svg
                      className="size-5"
                      width="5"
                      height="10"
                      viewBox="0 0 20 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Vector"
                        d="M12 22L2 12L12 2"
                        stroke="#3A3A3A"
                        stroke-width="2.8"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                  <div className="font-airbnb_w_xbd text-lg font-extrabold text-secondary-2-700 md:text-3xl">
                    Property Enquiry
                  </div>
                  <div onClick={() => closeSheet()} className="cursor-pointer">
                    <svg
                      className="size-5"
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M16 32C7.17725 32 0 24.8228 0 16C0 7.17725 7.17725 0 16 0C24.8228 0 32 7.17725 32 16C32 24.8228 24.8228 32 16 32ZM16 2C8.28003 2 2 8.28003 2 16C2 23.72 8.28003 30 16 30C23.72 30 30 23.72 30 16C30 8.28003 23.72 2 16 2Z"
                        fill="#3A3A3A"
                      />
                      <path
                        d="M11.0505 21.9497C10.7944 21.9497 10.5383 21.8525 10.3438 21.6565C9.95312 21.2659 9.95312 20.6326 10.3438 20.2419L20.2437 10.3418C20.6345 9.95117 21.2678 9.95117 21.6584 10.3418C22.0491 10.7324 22.0491 11.3657 21.6584 11.7566L11.7583 21.6565C11.561 21.8525 11.3052 21.9497 11.0505 21.9497Z"
                        fill="#3A3A3A"
                      />
                      <path
                        d="M20.9504 21.9504C20.6945 21.9504 20.4384 21.8532 20.2438 21.6572L10.3439 11.7585C9.95306 11.3679 9.95306 10.7346 10.3439 10.3439C10.7346 9.95306 11.3679 9.95306 11.7585 10.3439L21.6584 20.2438C22.0493 20.6345 22.0493 21.2678 21.6584 21.6584C21.4626 21.8532 21.2065 21.9504 20.9504 21.9504Z"
                        fill="#3A3A3A"
                      />
                    </svg>
                  </div>
                </SheetTitle>
                {/* <SheetDescription className="flex flex-col justify-start gap-4 px-[20px] pt-8 md:px-[80px]"></SheetDescription> */}
              </SheetHeader>
              <div className="container mx-auto flex max-w-full flex-col justify-start gap-6 pt-8 md:gap-[30px] lg:gap-9 2xl:gap-10">
                <FormField
                  control={form.control}
                  name="propertyCategoryId"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex w-full flex-col gap-1">
                        <PropertyQuestion question="Which type of property are you interested in ?" />
                        <Select
                          value={field.value}
                          defaultValue={parCategId ?? ""}
                          onValueChange={(e) => {
                            field.onChange(e);
                            handlePropertyCategory(e);
                          }}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue
                                className="font-airbnb_w_bk text-[18px] font-normal leading-[28px] text-text-600"
                                placeholder="Select the property"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectGroup>
                              {propertyCategories.map((item) => {
                                return (
                                  <>
                                    <SelectItem value={item.id}>
                                      {item.name}
                                    </SelectItem>
                                  </>
                                );
                              })}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>

                      {!form.getValues("propertyCategoryId") && (
                        <FormMessage className="text-start" />
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="propertyType"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex w-full flex-col gap-1">
                          <PropertyQuestion question="Are you looking to _ _ _ _ _ _ the property?" />
                          <div className="flex flex-wrap gap-4">
                            {propertyTypes?.map((item) => {
                              return (
                                <>
                                  <div
                                    onClick={() => handlePropertyType(item.id)}
                                  >
                                    <CustomBadge
                                      {...field}
                                      badgeName={item.name}
                                      isSelected={
                                        form.getValues("propertyType") ===
                                        item.id
                                          ? true
                                          : false
                                      }
                                    />
                                  </div>
                                </>
                              );
                            })}
                          </div>
                        </div>
                      </FormControl>

                      {(!form.getValues("propertyType") ||
                        form.getValues("propertyType") === "") && (
                        <FormMessage className="text-start" />
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex w-full flex-col gap-1">
                          <PropertyQuestion question="In which city or area are you interested?" />
                          <Select
                            value={field.value}
                            onValueChange={(e) => {
                              field.onChange(e);
                            }}
                          >
                            <FormControl>
                              <SelectTrigger className="w-full text-sm md:text-base xl:text-lg">
                                <SelectValue
                                  className="font-airbnb_w_bk text-sm font-normal leading-[28px] text-text-600"
                                  placeholder="Select the city"
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectGroup>
                                {cities.map((item) => {
                                  return (
                                    <>
                                      <SelectItem value={item.id}>
                                        {item.name}
                                      </SelectItem>
                                    </>
                                  );
                                })}
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                      </FormControl>
                      <FormMessage className="text-start" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="propertyPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex flex-col gap-1">
                          <PropertyQuestion question="Are you looking to _ _ _ _ _ _ the property?" />
                          <DualRangeSlider
                            className="secondary-2-700 my-5 rounded-[8px] bg-text-40"
                            label={(value) => <span>{value}</span>}
                            value={field.value}
                            onValueChange={(values) => field.onChange(values)}
                            min={0}
                            max={100000}
                            step={1}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="propertyPossession"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div>
                          <PropertyQuestion question="When do you need possession of the property?" />
                          <div className="flex flex-wrap gap-4">
                            {PossessionPropertyOptions.map((item) => {
                              return (
                                <>
                                  <div
                                    onClick={() =>
                                      handlePossessionProperty(item.value)
                                    }
                                  >
                                    <CustomBadge
                                      {...field}
                                      badgeName={item.label}
                                      isSelected={
                                        form.getValues("propertyPossession") ===
                                        item.value
                                          ? true
                                          : false
                                      }
                                    />
                                  </div>
                                </>
                              );
                            })}
                          </div>
                        </div>
                      </FormControl>
                      {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
                      {!form.getValues("propertyPossession") && (
                        <FormMessage className="text-start" />
                      )}
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="propertyRequirement"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex w-full flex-col gap-1">
                          <PropertyQuestion question="Is there anything else, you would like to share regarding your property requirement?" />
                          <Textarea {...field} rows={6} />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="fixed bottom-0 w-full overflow-hidden rounded-bl-2xl bg-primary-0/30 py-3 backdrop-blur-[15px] sm:max-w-[430px] md:max-w-[614px] md:py-9 lg:max-w-[819px] xl:max-w-[1065px] 2xl:max-w-[1536px]">
                <div className="container mx-auto max-w-full">
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      className="w-full text-[#FFFFFF] text-sm md:w-fit lg:text-base xl:text-sm "
                      variant={"sendEnquiry"}
                    >
                      Send Enquiry
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </>
  );
};
export default PropertyEnquiryForm;
