import React from "react";
import { Mail, Phone } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { SocialMediaLinks } from "~/app/utils/constants";

const TopNav = () => {
  return (
    <div className="bg-primary-2-600 py-2">
      <div className="container mx-auto max-w-full">
        <div className="flex items-center justify-between">
          {/* left div email address and phone number */}
          <div className="flex flex-col gap-2 font-airbnb_w_bk text-xs text-secondary-2-900 md:flex-row md:gap-4 md:text-sm lg:text-base xl:gap-6 xl:text-lg">
            {/* email */}
            <div className="flex items-center gap-1">
              <Mail
                className="size-[10.8px] md:size-[18px] xl:size-[21.6px]"
                strokeWidth={1.2}
              ></Mail>
              <Link href="mailto:<EMAIL>"><EMAIL></Link>
            </div>
            {/* phone */}
            <Link href="tel:7043807438" className="flex items-center gap-1">
              <Phone
                className="size-[10.8px] md:size-[18px] xl:size-[21.6px]"
                strokeWidth={1.2}
              ></Phone>{" "}
              +91 7043807438
            </Link>
          </div>
          {/* right div social icons */}
          <div className="flex gap-3 xl:gap-5">
            {SocialMediaLinks.map((item) => {
              return (
                <Link
                  href={item.link}
                  key={item.id}
                  target="_blank"
                  className="rounded-[2.4px] bg-secondary-2-900 p-[4.8px] lg:p-1.5"
                >
                  <Image
                    src={item.src}
                    alt={item.alt}
                    width={50}
                    height={50}
                    className="size-[11.2px] lg:size-3.5"
                  ></Image>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopNav;
