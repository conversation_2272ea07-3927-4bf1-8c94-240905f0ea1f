const CustomBadge = ({
  badgeName,
  isSelected,
}: {
  badgeName: string;
  isSelected: boolean;
}) => {
  return (
    <>
      <div
        className={`cursor-pointer px-[18px] py-[10px] font-airbnb_w_bk text-sm md:text-base xl:text-lg font-normal capitalize ${isSelected ? "rounded-[12px] bg-primary-2-700 text-text-40" : "text-text-600"}`}
      >
        {badgeName.replaceAll("_", " ").toLocaleLowerCase()}
      </div>
    </>
  );
};
export default CustomBadge;
