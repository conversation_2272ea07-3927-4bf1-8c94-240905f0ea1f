"use client";

import { Badge } from "@repo/ui/components/ui/badge";
import { Button } from "@repo/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";
import { DialogClose } from "@repo/ui/components/ui/dialog";
import { toast } from "@repo/ui/components/ui/sonner";
import { cn } from "@repo/ui/lib/utils";
import { MoveDown } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
// import { TAgentProfile } from "~/app/types";
import { api } from "~/trpc/react";
import { useSession } from "next-auth/react";

type ContactAgentButtonProps = {
  agentId: string;
  className?: string;
  connectedAgentId: string;
  propertyId?: string;
};

const ContactAgentButton = ({
  agentId,
  className,
  propertyId,
  connectedAgentId,
}: ContactAgentButtonProps) => {
  const { data: session } = useSession();
  const router = useRouter();
  const trpcUtils = api.useUtils();
  const [open, setOpen] = useState(false);
  // Check if connectedAgentId exists (not empty string) and is different from current agentId
  const hasConnectedAgent = connectedAgentId !== "";

  // Only fetch connected agent data if there is a connectedAgentId
  const { data: connectedAgent } = api.agent.getProfileById.useQuery(
    {
      agentId: connectedAgentId,
    },
    {
      // Skip the query if connectedAgentId is empty
      enabled: hasConnectedAgent,
    },
  );
  const { data: agent } = api.agent.getProfileById.useQuery({
    agentId: agentId,
  });
  const { mutate: sendConnectionRequest } =
    api.chat.sendNewConnectionRequest.useMutation();
  const { mutate: updateConnectionRequest } =
    api.chat.updateConnectionRequest.useMutation();
  const connectionId = connectedAgent?.agent?.coustomerConnections[0]?.id;

  const handleContactClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    sendConnectionRequest(
      { agentId: agentId, propertyId: propertyId },
      {
        onSuccess: (opts) => {
          if (opts.warning) {
            toast.warning(opts.message);
            return;
          }
          router.refresh();
          trpcUtils
            .invalidate()
            .then(() => console.log("refetch"))
            .catch((e) => console.log(e));
          toast.success(opts.message);
          setOpen(false);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  const handleUpdateContactClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!connectionId) return;
    updateConnectionRequest(
      { agentId: agentId, connectionId: connectionId },
      {
        onSuccess: (_opts) => {
          router.refresh();
          //   void trpcUtils.chat.invalidate();
          trpcUtils
            .invalidate()
            .then(() => console.log("refetch"))
            .catch((e) => console.log(e));

          toast.success("done");
          setOpen(false);
          //   window.location.reload();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  if (!session) {
    return (
      <Button
        className={"cursor-pointer"}
        onClick={() => {
          router.push("/sign-in");
          toast.error("You must login to contact an agent!");
        }}
      >
        Contact Agent
      </Button>
    );
  }

  //  If already connected to this specific agent, show "Connected" button and disable it
  if (hasConnectedAgent && agentId === connectedAgentId) {
    return (
      <Button variant="outline" disabled className={className}>
        Connected
      </Button>
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger
          asChild
          onClick={(e) => {
            e.preventDefault();
            setOpen(true);
          }}
        >
          <Button className={className}>Contact Agent</Button>
        </DialogTrigger>

        <DialogContent
          onClick={(e) => {
            e.preventDefault(); // Prevent default behavior
            e.stopPropagation(); // Stop the event from propagating
          }}
          className="w-[312px] p-6 md:w-[500px] lg:w-[576px] lg:p-8 xl:min-w-[680px] xl:p-10"
        >
          {/*  If user has a connected agent and it's different from current agent, show "Switch Your Current Agent" */}
          {hasConnectedAgent ? (
            <div className="flex flex-col gap-5 md:gap-6 lg:gap-8 xl:gap-10">
              {/* confirmation text */}
              <div className="flex flex-col items-center gap-2">
                <h2 className="text-center font-airbnb_w_xbd text-lg font-extrabold text-primary-2-750 md:text-xl lg:text-2xl xl:text-3xl">
                  Switch Your Current Agent
                </h2>
                <p className="text-center font-airbnb_w_bk text-sm text-text-500 md:text-base lg:text-lg xl:text-xl">
                  Do you want to Replace your current Residential Agent with
                  this Agent?
                </p>
              </div>

              <div className="flex flex-col gap-2">
                {/* agent details */}
                {/* existing agent card */}
                <div className="flex flex-col gap-1">
                  <Badge className="w-fit text-xs lg:text-sm">
                    Current Agent
                  </Badge>
                  <div className="flex items-center gap-2 rounded-sm border border-text-100 p-2 md:gap-4 md:p-4">
                    <div className="relative aspect-square w-10 md:w-[60px] xl:w-20">
                      <Image
                        src={
                          connectedAgent?.agent?.filePublicUrl ??
                          "/images/agent-fallback.png"
                        }
                        alt="agent-image"
                        fill
                        className="relative object-cover"
                      ></Image>
                    </div>
                    <div className="flex w-full justify-between gap-2 md:gap-0">
                      <div className="flex flex-col gap-1">
                        <h2 className="font-airbnb_w_xbd font-extrabold text-primary-2-850 md:text-xl lg:text-2xl xl:text-3xl">
                          {connectedAgent?.agent?.name}
                        </h2>
                        <h3 className="whitespace-nowrap font-airbnb_w_bk text-sm text-text-600 md:text-base lg:text-xl xl:text-2xl">
                          {connectedAgent?.agent?.company?.companyName}
                        </h3>
                      </div>
                      <div className="flex flex-col gap-1">
                        <p className="flex items-center gap-1 font-airbnb_w_bd text-xs font-bold text-text-600 md:text-sm lg:text-base xl:text-xl">
                          <div className="relative aspect-square w-4 xl:w-5">
                            <Image src={"/icons/star.svg"} alt="rating" fill />
                          </div>{" "}
                          {connectedAgent?.agent?.rating ?? 0}
                        </p>
                        <p className="font-airbnb_w_md text-xs font-medium text-text-600 md:text-sm lg:text-base xl:text-xl">
                          (
                          {connectedAgent?.agent?.customerRatingsToAgents
                            .length ?? "0"}{" "}
                          Reviews)
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <MoveDown className="self-center"></MoveDown>
                {/* new agent card */}

                <div className="flex items-center gap-2 rounded-sm border border-secondary-2-750 p-2 md:gap-4 md:p-4">
                  <div className="relative aspect-square w-10 md:w-[60px] xl:w-20">
                    <Image
                      src={
                        agent?.agent?.filePublicUrl ??
                        "/images/agent-fallback.png"
                      }
                      alt="agent-image"
                      fill
                      className="relative object-cover"
                    ></Image>
                  </div>
                  <div className="flex w-full justify-between gap-2 md:gap-0">
                    <div className="flex flex-col gap-1">
                      <h2 className="font-airbnb_w_xbd font-extrabold text-primary-2-850 md:text-xl lg:text-2xl xl:text-3xl">
                        {agent?.agent?.name}
                      </h2>
                      <h3 className="whitespace-nowrap font-airbnb_w_bk text-sm text-text-600 md:text-base lg:text-xl xl:text-2xl">
                        {agent?.agent?.company?.companyName}
                      </h3>
                    </div>
                    <div className="flex flex-col gap-1">
                      <p className="flex items-center gap-1 font-airbnb_w_bd text-xs font-bold text-text-600 md:text-sm lg:text-base xl:text-xl">
                        <div className="relative aspect-square w-4 xl:w-5">
                          <Image src={"/icons/star.svg"} alt="rating" fill />
                        </div>{" "}
                        {agent?.agent?.rating ?? 0}
                      </p>
                      <p className="font-airbnb_w_md text-xs font-medium text-text-600 md:text-sm lg:text-base xl:text-xl">
                        ({agent?.agent?.customerRatingsToAgents.length ?? "0"}{" "}
                        Reviews)
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* buttons */}
              <div className="flex flex-col gap-2 md:flex-row-reverse md:justify-end md:gap-8">
                <Button
                  onClick={handleUpdateContactClick}
                  className={cn("w-full md:w-1/2", className)}
                >
                  Yes Change
                </Button>
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="w-full md:w-1/2"
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default behavior
                      e.stopPropagation(); // Stop the event from propagating
                      setOpen(false); // Close the dialog
                    }}
                  >
                    Cancel
                  </Button>
                </DialogClose>
              </div>
            </div>
          ) : (
            /* If isNewAgent is true and connectedAgentId is empty, show "Connect with an agent" */
            <div className="flex flex-col gap-5 md:gap-6 lg:gap-8 xl:gap-10">
              {/* confirmation text */}
              <div className="flex flex-col items-center gap-2">
                <h2 className="text-center font-airbnb_w_xbd text-lg font-extrabold text-primary-2-750 md:text-xl lg:text-2xl xl:text-3xl">
                  Connect with an agent
                </h2>
                <p className="text-center font-airbnb_w_bk text-sm text-text-500 md:text-base lg:text-lg xl:text-xl">
                  Do you want to Add this Agent as your Representative
                  Residential Agent?
                </p>
              </div>
              {/* agent details */}
              <div className="flex items-center gap-2 rounded-sm border border-text-100 p-2 md:gap-4 md:p-4">
                <div className="relative aspect-square w-10 md:w-[60px] xl:w-20">
                  <Image
                    src={
                      agent?.agent?.filePublicUrl ??
                      "/images/agent-fallback.png"
                    }
                    alt="agent-image"
                    fill
                    className="relative object-cover"
                  ></Image>
                </div>
                <div className="flex w-full justify-between gap-2 md:gap-0">
                  <div className="flex flex-col gap-1">
                    <h2 className="font-airbnb_w_xbd font-extrabold text-primary-2-850 md:text-xl lg:text-2xl xl:text-3xl">
                      {agent?.agent?.name}
                    </h2>
                    <h3 className="whitespace-nowrap font-airbnb_w_bk text-sm text-text-600 md:text-base lg:text-xl xl:text-2xl">
                      {agent?.agent?.company?.companyName}
                    </h3>
                  </div>
                  <div className="flex flex-col gap-1">
                    <p className="flex items-center gap-1 font-airbnb_w_bd text-xs font-bold text-text-600 md:text-sm lg:text-base xl:text-xl">
                      <div className="relative aspect-square w-4 xl:w-5">
                        <Image src={"/icons/star.svg"} alt="rating" fill />
                      </div>{" "}
                      {agent?.agent?.rating ?? 0}
                    </p>
                    <p className="font-airbnb_w_md text-xs font-medium text-text-600 md:text-sm lg:text-base xl:text-xl">
                      ({agent?.agent?.reviews ?? 0} Reviews)
                    </p>
                  </div>
                </div>
              </div>

              {/* buttons */}
              <div className="flex flex-col gap-2 md:flex-row-reverse md:justify-end md:gap-8">
                <Button
                  onClick={handleContactClick}
                  className={cn("w-full md:w-1/2", className)}
                >
                  Yes
                </Button>
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    className="w-full md:w-1/2"
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default behavior
                      e.stopPropagation(); // Stop the event from propagating
                      setOpen(false); // Close the dialog
                    }}
                  >
                    Cancel
                  </Button>
                </DialogClose>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ContactAgentButton;
