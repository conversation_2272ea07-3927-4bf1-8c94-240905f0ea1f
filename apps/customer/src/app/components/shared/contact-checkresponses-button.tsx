"use client";

import React from "react";
import type { MouseEvent } from "react";

import { useSession } from "next-auth/react";
import { Button } from "@repo/ui/components/ui/button";
import { useRouter } from "next/navigation";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "@repo/ui/components/ui/sonner";

const ContactCheckResponsesButton = ({
  soldAt,
  buttonClassName,
  propertyOwnerId,
  propertyId,
}: {
  soldAt: Date | null;
  buttonClassName?: string;
  propertyOwnerId: string;
  propertyId: string;
}) => {
  const router = useRouter();
  const { data } = useSession();

  const onClickReqButton = (
    e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>,
  ) => {
    e.stopPropagation();
    if (!data?.user?.id) {
      toast.warning("Unable to send connection request.");
      return;
    }
  };

  return (
    <>
      {soldAt !== null ? (
        <Button variant="outline" className="z-50">
          Property Sold
        </Button>
      ) : data?.user?.id === propertyOwnerId ? (
        <Button
          variant="outline"
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/profile/listings/${propertyId}`);
          }}
          className={cn(
            "z-40 border-secondary-2-700 text-secondary-2-700",
            buttonClassName,
          )}
        >
          Check Responses
        </Button>
      ) : (
        <Button
          variant={"default"}
          onClick={(e) => onClickReqButton(e)}
          className={cn(buttonClassName)}
        >
          Contact Agent
        </Button>
      )}
    </>
  );
};

export default ContactCheckResponsesButton;
