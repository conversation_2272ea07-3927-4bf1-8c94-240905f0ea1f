import { cn } from "@repo/ui/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import React from "react";
import type { PaginationBarProps } from "~/app/types";

const PaginationBar = ({
  selectedPage,
  setSelectedPage,
  totalPages,
}: PaginationBarProps) => {
  return (
    <div className="flex w-full items-center justify-center py-2">
      <div
        className="flex items-center justify-center p-[14px]"
        onClick={() => {
          if (selectedPage === 1) return;
          setSelectedPage((selectedPage - 1).toString());
        }}
      >
        <ChevronLeft
          className={cn(
            "size-4 cursor-pointer",
            selectedPage === 1 && "hidden",
          )}
        />
      </div>
      <div className="no-scrollbar flex cursor-pointer items-center justify-between gap-2 overflow-x-scroll py-2 sm:max-w-screen-sm">
        {Array.from({ length: totalPages }).map((_, idx) => (
          <span
            onClick={() => setSelectedPage((idx + 1).toString())}
            className={cn(
              "flex size-11 items-center justify-center rounded-full border-2 border-transparent px-4 py-[10px] transition-all duration-300",
              selectedPage === idx + 1 &&
                "border-2 border-secondary-550 border-opacity-70 bg-secondary-30",
            )}
          >
            {idx + 1}
          </span>
        ))}
      </div>
      <div
        className="flex items-center justify-center p-[14px]"
        onClick={() => {
          if (selectedPage === totalPages) return;
          setSelectedPage((selectedPage + 1).toString());
        }}
      >
        <ChevronRight
          className={cn(
            "size-4 cursor-pointer",
            selectedPage === totalPages && "hidden",
          )}
        />
      </div>
    </div>
  );
};

export default PaginationBar;
