"use client";

import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

type Props = {
  title: string;
};

const ProfilePageTitleWithBackButton = ({ title }: Props) => {
  const router = useRouter();

  return (
    <p
      className="flex w-fit cursor-pointer items-center gap-2 font-medium text-text-550 md:gap-3 lg:text-lg xl:gap-4 xl:text-xl 2xl:text-2xl"
      onClick={() => router.back()}
    >
      <ChevronLeft className="size-[18px] text-text-500 lg:size-6" />
      {title}
    </p>
  );
};

export default ProfilePageTitleWithBackButton;
