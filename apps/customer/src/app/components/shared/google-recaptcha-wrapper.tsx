"use client";

import React from "react";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";
import { env } from "~/env";

const GoogleRecaptchaWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  return (
    <GoogleReCaptchaProvider reCaptchaKey={env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}>
      {children}
    </GoogleReCaptchaProvider>
  );
};

export default GoogleRecaptchaWrapper;
