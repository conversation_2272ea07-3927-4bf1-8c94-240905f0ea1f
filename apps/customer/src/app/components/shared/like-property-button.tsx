"use client";

import React from "react";
import { HeartIcon } from "lucide-react";
import { api } from "~/trpc/react";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

type LikePropertyButtonProps = {
  propertyId: string;
  isPropertyLiked: boolean;
  likeBtnClassname?: string;
  parentWrapperDivClassName?: string;
};

const LikePropertyButton = ({
  isPropertyLiked,
  propertyId,
  likeBtnClassname,
  parentWrapperDivClassName,
}: LikePropertyButtonProps) => {
  const router = useRouter();
  const trpcUtils = api.useUtils();
  const session = useSession();
  const { mutate: handleAddRemoveFavouriteProperty, isPending } =
    api.user.handleAddRemoveFavouriteProperty.useMutation();

  const handleLikeProperty = () => {
    handleAddRemoveFavouriteProperty(
      { propertyId },
      {
        onSuccess: (opts) => {
          router.refresh();
          trpcUtils.invalidate().catch((e) => console.log(e));

          toast.success(opts.message);
        },
        onError: (opts) => {
          toast.error(opts.message);
          trpcUtils.invalidate().catch((e) => console.log(e));
          router.push("/login");
        },
      },
    );
  };

  return (
    <div
      className={cn(
        "group cursor-pointer rounded-full bg-white p-[6px]",
        parentWrapperDivClassName,
      )}
      onClick={handleLikeProperty}
    >
      <HeartIcon
        className={cn(
          `size-5 text-secondary-2-700 transition-all duration-300 ease-in-out ${isPropertyLiked ? "scale-110" : "scale-100"} ${isPending ? "opacity-50" : "opacity-100"}`,
          likeBtnClassname,
        )}
        fill={isPropertyLiked && session.data?.user ? "currentColor" : "none"}
      />{" "}
    </div>
  );
};

export default LikePropertyButton;
