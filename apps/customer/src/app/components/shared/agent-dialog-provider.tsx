"use client";

import { useRouter, useSearchParams } from "next/navigation";
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
} from "@repo/ui/components/ui/dialog";
import { api } from "~/trpc/react";

import AgentDetail from "@repo/ui/components/shared/agent-detail";
import ContactAgentButton from "./contact-agent-button";

import AgentPropertyCardsSwiper from "../agents/agent-property-cards-swiper";
import AgentPostCardsSwiper from "../agents/agent-post-cards-swiper";
import LikeAgentButton from "../agents/agent-like-button";
import AgentTestimonialsSwiper from "../agents/agents-testimonials-swiper";
import ShareAgentButton from "./share-agent-button";

const AgentProfileDialogprovider = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);
  const agentId = searchParams.get("viewAgentId") ?? "";
  const { data } = api.agent.getAgentDetails.useQuery({
    id: agentId,
  });

  const { data: customerDetail } = api.user.getProfile.useQuery();
  const agent = data;

  if (!agent) {
    return null;
  }

  const handleClose = () => {
    params.delete("viewAgentId");
    router.push(`?${params}`);
  };

  return (
    <>
      <Dialog
        open={agentId ? true : false}
        onOpenChange={(open) => {
          if (!open) handleClose();
        }}
      >
        <DialogContent
          onEscapeKeyDown={() => {
            handleClose();
          }}
        >
          <DialogTitle className="hidden">Agent Details</DialogTitle>
          <AgentDetail
            agent={agent}
            contactAgent={
              <ContactAgentButton
                agentId={agent.id}
                connectedAgentId={customerDetail?.connections[0]?.agentId ?? ""}
              />
            }
            likeAgentBtn={<LikeAgentButton agentId={agentId} />}
            shareAgentProfileButton={<ShareAgentButton agentId={agentId} />}
            agentPostsCardSwiper={<AgentPostCardsSwiper posts={data.posts} />}
            agentPropertyCardsSwiper={
              <AgentPropertyCardsSwiper
                properties={data.properties}
                connectedAgentId={customerDetail?.connections[0]?.agentId ?? ""}
              />
            }
            videoReviews={<AgentTestimonialsSwiper agentId={agentId} />}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgentProfileDialogprovider;
