"use client";

import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import React from "react";

const EditPropertyButton = ({ propertyId }: { propertyId: string }) => {
  const router = useRouter();
  const pathname = usePathname();

  const handleClick = () => {
    const searchParams = new URLSearchParams();
    searchParams.set("step", "1");
    searchParams.set("postPropertyFormOpen", "true");
    searchParams.set("propertyId", propertyId);
    router.push(`${pathname}/?${searchParams.toString()}`);
  };
  return (
    <div
      className="cursor-pointer rounded-full bg-white p-[6px]"
      onClick={handleClick}
    >
      <Image
        src="/icons/shared/edit.svg"
        height={50}
        width={50}
        alt="edit"
        className="size-[18px]"
      />
    </div>
  );
};

export default EditPropertyButton;
