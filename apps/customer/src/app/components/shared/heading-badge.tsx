import React from "react";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";

type IHeadingBadgeProps = {
  content: string;
  className?: string;
  iconClassName?: string;
  fileUrl?: string;
};

const HeadingBadge = ({
  content,
  className,
  iconClassName,
  fileUrl,
}: IHeadingBadgeProps) => {
  return (
    <div
      className={cn(
        "flex max-w-fit items-center justify-center gap-2.5 rounded-sm bg-secondary-2-100 px-4 py-2.5 text-xs font-medium text-secondary-2-700 md:text-base lg:text-lg 2xl:text-[20px] 2xl:leading-[30px]",
        className,
      )}
    >
      <Image
        src={fileUrl ?? "/icons/grid-icon.svg"}
        height={50}
        width={50}
        className={cn("size-4 lg:size-5 2xl:size-6", iconClassName)}
        alt="Grid"
      />
      {content}
    </div>
  );
};

export default HeadingBadge;
