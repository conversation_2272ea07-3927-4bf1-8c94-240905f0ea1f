"use client";

import Image from "next/image";
import { Badge } from "@repo/ui/components/ui/badge";
import React, { useMemo } from "react";
import {
  calcPerUnitPriceAccordingToAreaUnit,
  FormatFacing,
  FormatFurnishing,
  FormatPossesionState,
  formatPriceAddLabels,
} from "@repo/ui/lib/formatTerm";
import type { TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded } from "~/app/types";
import LikePropertyButton from "./like-property-button";
import SharePropertyButton from "./share-property-button";
import ContactAgentButton from "./contact-agent-button";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "~/trpc/react";
import {
  PostPropertyFormFieldStatusEnum,
  PropertyMediaTypeEnum,
} from "@repo/database";
const PropertyCardHorizontal = ({
  property,
}: {
  property: TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const customerDetail = api.user.getProfile.useQuery();
  const connectdAgentId = customerDetail.data?.connections[0]?.agentId;
  //   const facilities = [
  //     {
  //       icon: "/icons/bed.svg",
  //       item: `${property.bedrooms} Bedrooms`,
  //     },
  //     {
  //       icon: "/icons/bathroom.svg",
  //       item: `${property.bathrooms} Bathrooms`,
  //     },
  //     {
  //       icon: "/icons/area.svg",
  //       item: `${property.area} /${property.areaUnit?.shortForm}`,
  //     },
  //   ];

  const handleOpen = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("viewPropertyId", property.id);
    router.push(`?${params}`, { scroll: false });
  };

  const coverImage = useMemo(() => {
    const flattenedMediaArray = property.mediaSections.flatMap(
      (section) => section.media,
    );

    const img = flattenedMediaArray.find(
      (item) =>
        item.mediaType === PropertyMediaTypeEnum.IMAGE ||
        item.mediaType === null,
    );
    return img?.cloudinaryUrl ?? img?.filePublicUrl;
  }, [property.mediaSections]);

  return (
    <>
      <div
        className="flex max-h-[228px] min-h-[228px] w-full cursor-pointer gap-3 rounded-2xl border-b border-b-text-50 bg-[#fcfffe] p-3 drop-shadow-[0_1px_4px_0px_rgba(97,97,97,0.15)] 2xl:max-h-full"
        onClick={handleOpen}
      >
        <div
          style={{
            backgroundImage: `url(${coverImage})`,
          }}
          className="flex aspect-[4/3] min-w-[296px] flex-col justify-between rounded-[12.67px] bg-cover bg-no-repeat p-[13px]"
        >
          <div className="flex items-center justify-between">
            {property.propertyState && (
              <div className="flex flex-row gap-[12.67px]">
                {property.propertyState === "NEW" ? (
                  <Badge className="rounded-[4.75px] bg-primary-800 px-1.5 py-1 text-[10px] font-medium leading-[15px] text-primary-0">
                    {property.propertyState}
                  </Badge>
                ) : (
                  <Badge className="rounded-[4.75px] bg-text-40 px-1.5 py-1 text-[10px] font-medium leading-[15px] text-secondary-2-700">
                    {property.propertyState}
                  </Badge>
                )}
              </div>
            )}

            <div
              className="flex items-center gap-[12.6px]"
              onClick={(e) => e.stopPropagation()}
            >
              <LikePropertyButton
                propertyId={property.id}
                isPropertyLiked={!!property.customerFavourites.length}
              />
              <SharePropertyButton propertyId={property.id} />
            </div>
          </div>

          <div className="flex items-center justify-between">
            {property.furnishing && (
              <Badge className="p-[4.75px] text-[9.5px] font-medium leading-[12.67px]">
                {FormatFurnishing(property.furnishing)}
              </Badge>
            )}
            {property.facing && (
              <Badge className="p-[4.75px] text-[9.5px] font-medium leading-[12.67px]">
                {FormatFacing(property.facing)}
              </Badge>
            )}
            {property.possessionState && (
              <Badge className="p-[4.75px] text-[9.5px] font-medium leading-[12.67px]">
                {FormatPossesionState(property.possessionState)}
              </Badge>
            )}
          </div>
        </div>

        <div className="flex w-full flex-col px-[14px]">
          {/* title and location */}
          <div className="mb-3 flex flex-col gap-1">
            {/* title */}
            <div className="font-airbnb_w_bd text-2xl font-bold leading-[30px] text-text-main xl:text-[20px]">
              {property.propertyTitle}
            </div>
            {/* address */}
            {property.propertyAddress && (
              <div className="flex items-center gap-1">
                <div className="relative size-4">
                  <Image src="/icons/location.svg" alt="location" fill />
                </div>
                <div className="line-clamp-1 font-airbnb_w_bk text-sm font-normal text-text-600 2xl:text-base">
                  {property.propertyAddress}
                </div>
              </div>
            )}
          </div>

          {/* facilities in the property */}
          <div className="flex items-center justify-between xl:mb-2.5">
            {/* {facilities.map((f, index) => (
              <div key={index} className="flex items-center gap-1">
                <div className="relative aspect-square w-4 2xl:w-6">
                  <Image src={f.icon} alt="facility" fill />
                </div>
                <div className="text-xs font-medium text-primary-600 2xl:text-sm">
                  {f.item}
                </div>
              </div>
            ))} */}
            {property.PropertyCategory?.showBedrooms !==
              PostPropertyFormFieldStatusEnum.HIDE &&
              property.bedrooms && (
                <div className="flex items-center gap-1">
                  <div className="relative aspect-square w-6">
                    <Image src="/icons/bed.svg" alt="facility" fill />
                  </div>
                  <div className="text-base text-primary-600">
                    {property.bedrooms} bedrooms
                  </div>
                </div>
              )}
            {property.PropertyCategory?.showBathrooms !==
              PostPropertyFormFieldStatusEnum.HIDE &&
              property.bathrooms && (
                <div className="flex items-center gap-1">
                  <div className="relative aspect-square w-6">
                    <Image src="/icons/bathroom.svg" alt="facility" fill />
                  </div>
                  <div className="text-base text-primary-600">
                    {property.bathrooms} bathrooms
                  </div>
                </div>
              )}
            <div className="flex items-center gap-1">
              <div className="relative aspect-square w-6">
                <Image src="/icons/area.svg" alt="facility" fill />
              </div>
              <div className="text-base text-primary-600">
                {`${property.area} /${property.areaUnit?.shortForm}`}
              </div>
            </div>
          </div>

          {/* description about property */}
          <div className="line-clamp-2 font-airbnb_w_bk text-sm font-normal text-text-600 2xl:text-base">
            {property.aboutProperty}
          </div>

          <div className="my-3 border-b border-secondary-2-300" />

          <div className="flex flex-row items-center justify-between">
            <div onClick={(e) => e.stopPropagation()}>
              <ContactAgentButton
                agentId={property.userId}
                connectedAgentId={connectdAgentId ?? ""}
              />
            </div>

            <div className="flex flex-col items-end">
              <div className="z-40 font-airbnb_w_xbd text-2xl font-extrabold text-primary-2-700">
                ₹{formatPriceAddLabels(property.propertyPrice)}
              </div>
              <div className="z-40 font-airbnb_w_xbd font-semibold text-primary-2-700">
                ₹
                {formatPriceAddLabels(
                  calcPerUnitPriceAccordingToAreaUnit({
                    area: property.area,
                    propertyPrice: property.propertyPrice,
                  }),
                )}
                /{property.areaUnit?.shortForm}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PropertyCardHorizontal;
