"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Trigger,
} from "@repo/ui/components/ui/default-dialog";
import {
  FacebookShareButton,
  WhatsappShareButton,
  WhatsappIcon,
  FacebookIcon,
  EmailShareButton,
  EmailIcon,
  TelegramShareButton,
  TelegramIcon,
} from "react-share";
import { CopyIcon, Share2 } from "lucide-react";
import { toast } from "@repo/ui/components/ui/sonner";
import { Button } from "@repo/ui/components/ui/button";
import { cn } from "@repo/ui/lib/utils";

export function ShareIcons({ shareUrl }: { shareUrl: string }) {
  return (
    <div className="flex justify-center gap-2 sm:gap-4">
      <FacebookShareButton url={shareUrl}>
        <FacebookIcon className="size-10 sm:size-16" round={true} />
      </FacebookShareButton>
      <WhatsappShareButton url={shareUrl}>
        <WhatsappIcon className="size-10 sm:size-16" round={true} />
      </WhatsappShareButton>
      <EmailShareButton url={shareUrl}>
        <EmailIcon className="size-10 sm:size-16" round={true} />
      </EmailShareButton>
      <TelegramShareButton url={shareUrl}>
        <TelegramIcon className="size-10 sm:size-16" round={true} />
      </TelegramShareButton>
    </div>
  );
}

const ShareAgentButton = ({
  agentId,
  shareBtnClassname,
  mainClassname,
}: {
  agentId: string;
  shareBtnClassname?: string;
  mainClassname?: string;
}) => {
  const generateShareUrl = () => {
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split("?")[0];

    return `${baseUrl}?viewAgentId=${agentId}`;
  };

  const handleCopyLink = async () => {
    const shareUrl = generateShareUrl();
    await navigator.clipboard.writeText(shareUrl);
    toast.success("Agent link has been copied to your clipboard");
  };

  return (
    <>
      <Dialog>
        <DialogTrigger>
          <Button
            className={cn(
              "rounded-lg border-[1.5px] border-primary-2-750 bg-text-20 font-airbnb_w_md font-medium text-primary-2-750 hover:bg-transparent md:rounded-xl",
              mainClassname,
            )}
          >
            <Share2
              className={cn("mr-3 size-[18px] lg:size-6", shareBtnClassname)}
              color="#784100"
            />
            Share
          </Button>
        </DialogTrigger>
        <DialogContent className="w-[300px] rounded-2xl bg-white p-10 sm:w-[400px] sm:rounded-2xl md:w-[512px]">
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between">
              <p className="font-raleway text-2xl font-bold -tracking-[0.23px] text-primary-2-800">
                Share Agent
              </p>
            </div>
            <div>
              <ShareIcons shareUrl={generateShareUrl()} />
            </div>
            <Button
              className="flex w-full items-center justify-between rounded-xl bg-white px-4 py-[11px] hover:bg-primary-2-400 active:bg-white"
              onClick={handleCopyLink}
            >
              <p className="text-lg text-primary-2-800">Copy Agent Link</p>
              <CopyIcon className="text-primary-2-800" />
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ShareAgentButton;
