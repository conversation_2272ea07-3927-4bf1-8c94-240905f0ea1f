"use client";

import Image from "next/image";
import { Badge } from "@repo/ui/components/ui/badge";
import {
  calcPerUnitPriceAccordingToAreaUnit,
  FormatFacing,
  FormatFurnishing,
  FormatPossesionState,
  formatPriceAddLabels,
} from "@repo/ui/lib/formatTerm";
import type { TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded } from "~/app/types";
import LikePropertyButton from "./like-property-button";
import SharePropertyButton from "./share-property-button";
import { useRouter, useSearchParams } from "next/navigation";
import ContactAgentButton from "./contact-agent-button";
import { api } from "~/trpc/react";
import {
  PostPropertyFormFieldStatusEnum,
  PropertyMediaTypeEnum,
} from "@repo/database";
import { useMemo } from "react";

const PropertyCardBig = ({
  property,
}: {
  property: TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const customerDetail = api.user.getProfile.useQuery();
  const connectdAgentId = customerDetail.data?.connections[0]?.agentId;

  //   const facilities = [
  //     {
  //       icon: "/icons/bed.svg",
  //       item: `${property.bedrooms} Bedrooms`,
  //     },
  //     {
  //       icon: "/icons/bathroom.svg",
  //       item: `${property.bathrooms} Bathrooms`,
  //     },
  //     {
  //       icon: "/icons/area.svg",
  //       item: `${property.area} /${property.areaUnit?.shortForm}`,
  //     },
  //   ];

  const handleOpen = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("viewPropertyId", property.id);
    router.push(`?${params}`, { scroll: false });
  };

  const coverImage = useMemo(() => {
    const flattenedMediaArray = property.mediaSections.flatMap(
      (section) => section.media,
    );

    const img = flattenedMediaArray.find(
      (item) =>
        item.mediaType === PropertyMediaTypeEnum.IMAGE ||
        item.mediaType === null,
    );
    return img?.cloudinaryUrl ?? img?.filePublicUrl;
  }, [property.mediaSections]);

  return (
    <>
      <div
        className="cursor-pointer rounded-2xl border-b border-b-text-50 bg-[#fbfffe] xl:min-w-[550px] xl:max-w-[550px] 2xl:min-w-full"
        onClick={handleOpen}
      >
        <div
          style={{
            backgroundImage: `url(${coverImage})`,
          }}
          className="flex aspect-[4/3] flex-col justify-between rounded-2xl bg-cover bg-no-repeat xl:p-[26px] 2xl:p-8"
        >
          <div className="flex items-center justify-between">
            {property.propertyState && (
              <div className="flex flex-row gap-[23.47px] 2xl:gap-[36px]">
                {property.propertyState === "NEW" ? (
                  <Badge className="rounded-[8.8px] bg-text-800 px-[11.73px] py-[8.8px] font-airbnb_w_md text-lg font-medium text-primary-0 2xl:rounded-[13.57px] 2xl:px-[18px] 2xl:py-[13px] 2xl:text-[24px] 2xl:leading-[30px]">
                    {property.propertyState}
                  </Badge>
                ) : (
                  <Badge className="rounded-[8.8px] bg-text-40 px-[11.73px] py-[8.8px] font-airbnb_w_md text-lg font-medium text-secondary-2-700 2xl:rounded-[13.57px] 2xl:px-[18px] 2xl:py-[13px] 2xl:text-[24px] 2xl:leading-[30px]">
                    {property.propertyState}
                  </Badge>
                )}
              </div>
            )}

            <div
              className="flex items-center gap-[23.47px] 2xl:gap-[36.18px]"
              onClick={(e) => e.stopPropagation()}
            >
              <LikePropertyButton
                propertyId={property.id}
                isPropertyLiked={!!property.customerFavourites.length}
                likeBtnClassname="xl:size-6 2xl:size-[37px]"
                parentWrapperDivClassName="xl:p-[10px] 2xl:p-[15px]"
              />
              <SharePropertyButton
                propertyId={property.id}
                mainClassname="xl:p-[10px] 2xl:p-[15px]"
                shareBtnClassname="xl:size-6 2xl:size-[37px]"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            {property.furnishing && (
              <Badge className="rounded-[5.87px] bg-text-40 p-[9px] text-sm font-medium text-primary-2-700 2xl:rounded-[9.05px] 2xl:p-[14px] 2xl:text-[20px] 2xl:leading-[30px]">
                {FormatFurnishing(property.furnishing)}
              </Badge>
            )}
            {property.facing && (
              <Badge className="rounded-[5.87px] bg-text-40 p-[9px] text-sm font-medium text-primary-2-700 2xl:rounded-[9.05px] 2xl:p-[14px] 2xl:text-[20px] 2xl:leading-[30px]">
                {FormatFacing(property.facing)}
              </Badge>
            )}
            {property.possessionState && (
              <Badge className="rounded-[5.87px] bg-text-40 p-[9px] text-sm font-medium text-primary-2-700 2xl:rounded-[9.05px] 2xl:p-[14px] 2xl:text-[20px] 2xl:leading-[30px]">
                {FormatPossesionState(property.possessionState)}
              </Badge>
            )}
          </div>
        </div>

        <div className="p-6">
          {/* title and location */}
          <div className="mb-5 flex flex-col gap-1 2xl:mb-6">
            {/* title */}
            <div className="font-airbnb_w_bd text-2xl font-bold text-text-main 2xl:text-3xl">
              {property.propertyTitle}
            </div>
            {/* address */}
            {property.propertyAddress && (
              <div className="flex items-center gap-0.5">
                <div className="relative aspect-square size-4">
                  <Image src="/icons/location.svg" alt="location" fill />
                </div>
                <div className="line-clamp-1 font-airbnb_w_bk text-base font-normal text-text-600 2xl:text-lg">
                  {" "}
                  {property.propertyAddress}
                </div>
              </div>
            )}
          </div>

          {/* facilities in the property */}
          <div className="mb-3 flex items-center justify-between 2xl:mb-4">
            {/* {facilities.map((f, index) => (
              <div key={index} className="flex items-center gap-1">
                <div className="relative aspect-square w-6">
                  <Image src={f.icon} alt="facility" fill />
                </div>
                <div className="text-base text-primary-600">{f.item}</div>
              </div>
            ))} */}
            {property.PropertyCategory?.showBedrooms !==
              PostPropertyFormFieldStatusEnum.HIDE &&
              property.bedrooms && (
                <div className="flex items-center gap-1">
                  <div className="relative aspect-square w-6">
                    <Image src="/icons/bed.svg" alt="facility" fill />
                  </div>
                  <div className="text-base text-primary-600">
                    {property.bedrooms} bedrooms
                  </div>
                </div>
              )}
            {property.PropertyCategory?.showBathrooms !==
              PostPropertyFormFieldStatusEnum.HIDE &&
              property.bathrooms && (
                <div className="flex items-center gap-1">
                  <div className="relative aspect-square w-6">
                    <Image src="/icons/bathroom.svg" alt="facility" fill />
                  </div>
                  <div className="text-base text-primary-600">
                    {property.bathrooms} bathrooms
                  </div>
                </div>
              )}
            <div className="flex items-center gap-1">
              <div className="relative aspect-square w-6">
                <Image src="/icons/area.svg" alt="facility" fill />
              </div>
              <div className="text-base text-primary-600">
                {`${property.area} /${property.areaUnit?.shortForm}`}
              </div>
            </div>
          </div>

          {/* description about property */}
          <div className="line-clamp-2 font-airbnb_w_bk text-lg font-normal text-text-600">
            {property.aboutProperty}
          </div>

          <div className="mb-6 mt-7 border-b border-secondary-2-300 2xl:mb-7" />

          <div className="flex flex-row items-center justify-between gap-6">
            <div onClick={(e) => e.stopPropagation()} className="flex-1">
              {/* <ContactAgentButton
                agentId={property.userId}
                className="w-full"
              /> */}
              <ContactAgentButton
                agentId={property.userId}
                connectedAgentId={connectdAgentId ?? ""}
              />
            </div>

            <div className="flex flex-col items-end">
              <div className="z-40 font-airbnb_w_xbd text-2xl font-extrabold text-primary-2-700">
                ₹{formatPriceAddLabels(property.propertyPrice)}
              </div>
              <div className="z-40 font-airbnb_w_xbd font-semibold text-primary-2-700">
                ₹
                {formatPriceAddLabels(
                  calcPerUnitPriceAccordingToAreaUnit({
                    area: property.area,
                    propertyPrice: property.propertyPrice,
                  }),
                )}
                /{property.areaUnit?.shortForm}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PropertyCardBig;
