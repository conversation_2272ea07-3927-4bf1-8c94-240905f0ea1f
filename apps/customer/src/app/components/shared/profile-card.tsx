"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";
import { api } from "~/trpc/react";
import ProfileCardSkeleton from "../skeleton/profile-card-skeleton";

type profileCardProps = {
  open?: boolean;
  setOpen?: (v: boolean) => void;
};

const ProfileCard = ({ open, setOpen }: profileCardProps) => {
  const { data: profile, isLoading } = api.user.getProfile.useQuery();

  return (
    <>
      {isLoading ? (
        <ProfileCardSkeleton />
      ) : (
        <Link
          href="/profile/edit"
          className="cursor-pointer"
          onClick={() => setOpen?.(!open)}
        >
          <div className="flex flex-col gap-[14px] rounded-xl bg-[linear-gradient(90deg,_#F04D24_0%,_#C33D12_50%,_#962C00_100%)] p-3 text-white backdrop-blur-xl xl:py-4">
            <div className="flex items-start gap-2">
              <div className="relative aspect-square size-10 xl:size-[62px] 2xl:size-[100px]">
                <Image
                  src={
                    profile?.cloudinaryImagePublicUrl ??
                    profile?.profileImagePublicUrl ??
                    "/images/placeholder-user-image.jpg"
                  }
                  alt="profile"
                  fill
                  className="relative rounded-[6px] object-cover"
                />
              </div>
              <div className="line-clamp-1 flex flex-col gap-0.5">
                <p className="truncate font-airbnb_w_xbd text-lg lg:max-w-[200px] xl:text-xl 2xl:max-w-[290px]">
                  {profile?.name}
                </p>
                <p className="no-scrollbar overflow-scroll font-airbnb_w_bk text-sm">
                  {profile?.email}
                </p>
              </div>
            </div>
            <div className="flex items-center justify-end">
              <div className="flex items-center gap-6">
                <div className="relative aspect-square cursor-pointer">
                  <Image
                    src="/icons/edit-icon.svg"
                    alt="edit"
                    height={50}
                    width={50}
                    className="size-5 xl:size-6"
                  />
                </div>
              </div>
            </div>
          </div>
        </Link>
      )}
    </>
  );
};

export default ProfileCard;
