'use client'
import React from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/ui/alert-dialog";
import { signOut } from "next-auth/react";

const LogoutButton = () => {
  const handleLogout = async () => {
    await signOut();
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <button className="px-3 py-[10px] text-left text-sm font-medium text-text-600">
          Logout
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent className="w-[300px] md:w-[398px] lg:w-[576px] xl:w-[600px] xl:p-8 2xl:w-[680px]">
        <AlertDialogHeader className="mb-3 lg:mb-5">
          <AlertDialogTitle className="mb-2 text-center font-airbnb_w_xbd text-2xl font-extrabold text-text-600 md:text-3xl lg:mb-4 lg:text-4xl 2xl:text-[40px] 2xl:leading-[48px]">
            Logout
          </AlertDialogTitle>
          <AlertDialogDescription className="text-center font-airbnb_w_bk text-base font-normal md:text-lg lg:text-xl 2xl:text-2xl">
            Logging out will end your current session. Do you want to continue?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="">
          <AlertDialogCancel className="w-full border border-transparent bg-secondary-2-100 text-secondary-2-700">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction onClick={handleLogout} className="w-full">
            Yes, logout
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default LogoutButton;
