"use client";

import React from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/ui/button";
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Textarea } from "@repo/ui/components/ui/textarea";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";

const formSchema = z.object({
  reason: z
    .string()
    .min(5, { message: "Text should be more than 5 characters" }),
});

const ReportPostDialog = ({ postId }: { postId: string }) => {
  const reportPost = api.social.reportPost.useMutation();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reason: "",
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    const { reason } = values;
    reportPost.mutate(
      { postId, reason },
      {
        onSuccess: (opts) => toast.success(opts.message),
        onError: (opts) => {
          toast.error(opts.message);
          console.log("error is", opts.data);
        },
      },
    );
    form.reset();
  };
  return (
    <>
      <DialogHeader className="flex flex-col xl:gap-2">
        <DialogTitle className="text-[15px] md:text-base lg:text-lg">
          Please state your reason for reporting this post
        </DialogTitle>
        <DialogDescription>
          Type in your response and click on submit to report this post
        </DialogDescription>
      </DialogHeader>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4 xl:space-y-5"
        >
          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea placeholder="type here..." {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit">Submit</Button>
        </form>
      </Form>
    </>
  );
};

export default ReportPostDialog;
