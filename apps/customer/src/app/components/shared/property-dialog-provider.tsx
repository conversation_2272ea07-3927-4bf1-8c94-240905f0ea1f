"use client";

import PropertyDetail from "@repo/ui/components/shared/property-detail";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@repo/ui/components/ui/dialog";
import { api } from "~/trpc/react";
import { useRouter, useSearchParams } from "next/navigation";
import { skipToken } from "@tanstack/react-query";

import { useSession } from "next-auth/react";
import ContactAgentButton from "./contact-agent-button";
import LikePropertyButton from "./like-property-button";
import SharePropertyButton from "./share-property-button";

const PropertyDialogProvider = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);

  const propertyId = searchParams.get("viewPropertyId");
  const customerDetail = api.user.getProfile.useQuery();
  const connectdAgentId = customerDetail.data?.connections[0]?.agentId;

  const { mutate: addToViewingHistory } =
    api.user.addPropertyToCustomerViewingHistory.useMutation();
  const { data } = api.property.getPropertyDetails.useQuery(
    propertyId ? { id: propertyId } : skipToken,
  );

  const property = data;

  if (!property) {
    return null;
  }

  const addToHistory = (propertyId: string) => {
    addToViewingHistory({ propertyId: propertyId });
  };

  const userProfileClick = (agentId: string) => {
    params.set("viewAgentId", agentId);
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleClose = () => {
    params.delete("viewPropertyId");
    router.push(`?${params}`, { scroll: false });
  };

  return (
    <>
      <Dialog
        open={propertyId ? true : false}
        onOpenChange={(open) => {
          if (!open) handleClose();
        }}
      >
        <DialogContent
          onEscapeKeyDown={() => {
            handleClose();
          }}
        >
          <DialogTitle className="hidden">Property Details</DialogTitle>
          <PropertyDetail
            property={property}
            locationIcon="/icons/location.svg"
            bedroomIcon="/icons/bedroom.svg"
            bathroomIcon="/icons/bathroom.svg"
            areaIcon="/icons/area.svg"
            userId={session?.user?.id}
            contactOrCheckResponsesButton={
              <ContactAgentButton
                agentId={property.userId}
                propertyId={property.id}
                connectedAgentId={connectdAgentId ?? ""}
              />
            }
            likePropertyButton={
              <LikePropertyButton
                isPropertyLiked={!!property.customerFavourites.length}
                propertyId={property.id}
              />
            }
            sharePropertyButton={
              <SharePropertyButton propertyId={property.id} />
            }
            // editPropertyButton={<EditPropertyButton propertyId={property.id} />}
            addToUserHistory={() => addToHistory(property.id)}
            userProfileClick={userProfileClick}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PropertyDialogProvider;
