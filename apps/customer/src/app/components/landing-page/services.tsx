import { ServicesCards } from "~/app/utils/constants";
import HeadingBadge from "../shared/heading-badge";
import Image from "next/image";
import { Button } from "@repo/ui/components/ui/button";
import Link from "next/link";

const Services = () => {
  return (
    <>
      <div className="bg-[url('/main/images/services/services-bg.png')]">
        <div className="container mx-auto max-w-full py-9 md:py-[40px] lg:py-[55px] xl:py-[65px] 2xl:py-[70px]">
          <div className="flex flex-col items-center">
            {/* Heading badge */}
            <HeadingBadge
              content="My Deer Services"
              fileUrl="/icons/grid-icon.svg"
              className="mb-1 md:mb-2 lg:mb-2.5 xl:mb-[14px]"
            />

            {/* section heading */}
            <div className="mb-3 text-center font-airbnb_w_xbd text-3xl font-semibold text-text-700 md:mb-[14px] lg:mb-4 lg:text-4xl xl:mb-[18px] xl:text-[40px] xl:leading-[48px] 2xl:text-[48px] 2xl:leading-[56px]">
              Your Go-To Guide for Real Estate
            </div>

            {/* sub-heading */}
            <div className="mb-8 text-center font-airbnb_w_md text-lg font-medium text-primary-600 md:mb-9 md:gap-8 lg:mb-[45px] lg:text-[20px] lg:leading-[30px] xl:mb-[50px] 2xl:mb-[60px]">
              Whether you're buying, selling, or renting, we’ve got you covered
            </div>

            {/* service cards */}
            <ServiceCards />
          </div>
        </div>
      </div>
    </>
  );
};

export default Services;

const ServiceCards = () => {
  return (
    <div className="grid gap-5 lg:grid-cols-3 lg:gap-6">
      {ServicesCards.map((service, i) => (
        <div
          key={i}
          className="w-full rounded-2xl bg-text-20 bg-opacity-60 p-4 backdrop-blur-[40px] md:rounded-3xl md:p-5 xl:p-6 2xl:w-[400px] 2xl:p-8"
        >
          <div className="flex flex-row items-start gap-5 lg:flex-col lg:items-center xl:gap-6 2xl:gap-8">
            {/* service card image */}
            <div className="relative aspect-square w-[74px] md:w-[112px]">
              <Image src={service.image} fill alt="service-card-image" />
            </div>

            {/* bottom part of card */}
            <div className="flex flex-col lg:items-center">
              {/* title */}
              <div className="mb-1 font-airbnb_w_xbd text-base font-extrabold text-text-600 md:mb-2 md:text-xl xl:text-2xl 2xl:text-3xl">
                {service.title}
              </div>

              {/* description */}
              <div className="mb-3 font-airbnb_w_bk text-sm font-normal text-text-500 md:mb-4 md:text-base lg:h-[48px] lg:text-center 2xl:mb-5 2xl:h-[56px] 2xl:text-lg">
                {service.desc}
              </div>

              {/* button */}
              <Link target="_blank" href={service.link}>
                <Button
                  className="w-[191px] px-6 md:w-[240px] md:text-base lg:w-[245.33px] xl:w-[274px] xl:py-3.5"
                  variant="default"
                >
                  {service.button}
                </Button>
              </Link>
              {/* <GreenBtn title={service.button} /> */}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
