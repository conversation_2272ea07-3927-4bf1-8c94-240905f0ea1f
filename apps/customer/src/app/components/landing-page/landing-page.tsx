import React from "react";
import HeroSection from "./hero";
import { api } from "~/trpc/server";
import FeaturedListing from "./featured-listing";
import Services from "./services";
import PopularHomes from "./popular-homes";
import RealEstateAdvantage from "./real-estate-advantage";
import Testimonials from "./testimonials";

const LandingPage = async () => {
  const propertyTypes = await api.property.getPropertyTypes();
  const featuredProperties = await api.property.getFeaturedProperties({
    propertyType: undefined,
  });
  const popularProperties = await api.property.getPopularProperties();
  const testimonials = await api.customerTestimonials.getHomePageTestimonials();

  return (
    <>
      <HeroSection />
      <FeaturedListing
        propertyTypes={propertyTypes}
        properties={featuredProperties}
      />
      <Services />
      <RealEstateAdvantage />
      <PopularHomes properties={popularProperties} />
      <Testimonials testimonials={testimonials} />
    </>
  );
};

export default LandingPage;
