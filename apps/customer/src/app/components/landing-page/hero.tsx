"use client";

import Image from "next/image";
import { useState } from "react";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter } from "next/navigation";
import HeadingBadge from "../shared/heading-badge";
import GoogleAutocompleteInput from "@repo/ui/components/shared/google-autocomplete-input";

const HeroSection = () => {
  const router = useRouter();
  const [propertyFor, setPropertyFor] = useState<"SALE" | "RENT">("SALE");
  const [address, setAddress] = useState<string>("");
  const [latitude, setLatitude] = useState<string>("");
  const [longitude, setLongitude] = useState<string>("");

  const handleUrlParams = () => {
    if (!address || !latitude || !longitude) {
      toast.error("Please select an address to search");
    }
    const params = new URLSearchParams();
    params.set("search", address);
    params.set("lat", latitude);
    params.set("long", longitude);
    params.set("propertyFor", propertyFor);
    router.push(`/property-listing/?${params.toString()}`);
  };

  const handlePropertyType = (type: "SALE" | "RENT") => {
    setPropertyFor(type);
  };

  return (
    <>
      <div className="relative bg-primary-0">
        <div className="container mx-auto flex max-w-full flex-row items-center gap-[50px] 2xl:gap-[60px]">
          {/* left side portion */}
          <div className="flex w-full flex-col items-center justify-center gap-5 py-[31px] md:py-[44px] lg:gap-[45px] lg:py-[72.5px] xl:w-[55%] xl:items-start xl:gap-10 xl:py-0">
            {/* Badge */}
            <div className="flex flex-col items-center gap-2.5 md:gap-3 lg:gap-4 xl:items-start xl:gap-[18px]">
              <HeadingBadge
                fileUrl={"/icons/white-hut.svg"}
                content="Real Estate at Your Fingertips"
                className="bg-primary-2-750 text-text-20"
                iconClassName="size-4 2xl:size-6"
              />
              {/* main heading */}
              <div className="text-center font-airbnb_w_blk text-3xl font-black text-text-800 md:text-4xl lg:text-[40px] lg:leading-[48px] xl:text-left xl:text-[48px] xl:leading-[56px] 2xl:text-[52px] 2xl:leading-[64px]">
                Find Your Dream Home. Empower Your <br />
                <span className="text-primary-600"> Real Estate Journey </span>
              </div>
              {/* sub-heading */}
              <div className="text-center font-airbnb_w_md text-sm font-medium text-text-550 md:text-base lg:text-xl xl:text-left xl:text-2xl 2xl:text-3xl">
                Deer connects buyers, sellers, and agents for a seamless and
                stress-free experience.
              </div>
            </div>

            {/* Search filter */}
            <div className="z-10 w-full rounded-[24px] bg-[rgba(246,_148,_124,_0.20)] bg-opacity-20 p-2 backdrop-blur-lg lg:w-[70%] lg:rounded-[32px] lg:p-2.5 xl:w-[90%] xl:p-4 2xl:p-6">
              <div className="rounded-[12px] bg-white py-3 lg:rounded-[20px] xl:py-5">
                <div className="flex flex-row px-5 md:justify-start md:gap-10 xl:gap-2.5 xl:px-8">
                  <button
                    onClick={() => handlePropertyType("SALE")}
                    className={`${propertyFor === "SALE" ? `border-b-[2px] border-primary-600 bg-white font-airbnb_w_bd font-bold text-primary-600` : ``} px-[21px] pb-1 font-airbnb_w_bk text-sm font-normal text-text-600 lg:text-base xl:px-[43px] 2xl:text-lg`}
                  >
                    Buy
                  </button>
                  <button
                    onClick={() => handlePropertyType("RENT")}
                    className={`${propertyFor === "RENT" ? `border-b-[2px] border-primary-600 bg-white font-airbnb_w_bd font-bold text-primary-600` : ``} px-[21px] pb-1 font-airbnb_w_bk text-sm font-normal text-text-600 lg:text-base xl:px-[43px] 2xl:text-lg`}
                  >
                    Rent
                  </button>
                  {/* <button onClick={()=>handlePropertyType("Lease")}
                    className={`${propertyFor === "Lease" ? `border-b-[2px] border-primary-600 bg-white font-bold font-airbnb_w_bd text-primary-600`:`` } px-[21px] pb-1 font-airbnb_w_bk text-sm font-normal text-text-600 lg:text-base xl:px-[43px] 2xl:text-lg`}>
                    Lease
                  </button> */}
                </div>
                <div className="border-b border-primary-250"></div>
                <div className="mt-2 flex flex-row items-center px-5 lg:mt-2.5 xl:mt-4 xl:px-8">
                  <div className="flex w-full items-center justify-between gap-4">
                    <div className="w-full">
                      <GoogleAutocompleteInput
                        className="ring-offset-none w-full border-0 bg-white placeholder:font-normal placeholder:text-text-400 focus:border-none focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                        placeholder="Search by location"
                        onLocationSelect={(e) => {
                          setAddress(e.fullAddress);
                          setLatitude(e.latitude);
                          setLongitude(e.longitude);
                        }}
                        showSearchIcon={true}
                      />
                    </div>

                    <div className="flex flex-row gap-1.5 self-stretch lg:gap-2">
                      <button
                        onClick={handleUrlParams}
                        className="rounded-lg bg-primary-2-750 px-3 py-1.5 font-airbnb_w_bk text-sm font-normal text-white hover:bg-primary-2-750/80 lg:px-4 lg:text-base xl:px-6 xl:text-lg 2xl:py-2"
                      >
                        Search
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* right side portion till 1440*/}
          <div className="relative z-10 hidden aspect-square xl:mt-[49px] xl:block xl:w-[568px] 2xl:mt-[43px] 2xl:w-[780px]">
            <Image src={"/images/bg-hero.png"} alt="hero-bg-image" fill />
          </div>
        </div>
        {/* brown background till 1440 */}
        <div className="z-1 absolute bottom-0 right-0 top-0 hidden h-full w-[530px] bg-primary-600 xl:block 2xl:w-[685px]"></div>

        <div className="z-1 absolute bottom-0 left-0 right-0 h-[130px] w-full bg-primary-600 md:hidden"></div>
      </div>
    </>
  );
};

export default HeroSection;
