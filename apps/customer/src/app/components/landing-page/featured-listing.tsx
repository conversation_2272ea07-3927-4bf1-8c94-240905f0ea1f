"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@repo/ui/components/ui/tabs";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@repo/ui/components/ui/scroll-area";
import Autoplay from "embla-carousel-autoplay";
import type { PropertyType } from "@repo/database";
import HeadingBadge from "../shared/heading-badge";
import PropertyCard from "@repo/ui/components/shared/property-card";
import ContactAgentButton from "../shared/contact-agent-button";
import LikePropertyButton from "../shared/like-property-button";
import SharePropertyButton from "../shared/share-property-button";
import PropertyCardSkeleton from "@repo/ui/components/skeleton/property-card-skeleton";

import type { TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded } from "~/app/types";
import { useState } from "react";
import { api } from "~/trpc/react";
import { skipToken } from "@tanstack/react-query";

type FeaturedListingProps = {
  propertyTypes: PropertyType[];
  properties: TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded[];
  //   customer: TCustomerProfile;
};

const FeaturedListing = ({
  propertyTypes,
  properties,
}: FeaturedListingProps) => {
  const [selectedTab, setSelectedTab] = useState<string | null>(null);
  const { data: customer } = api.user.getProfile.useQuery();
  console.log("profile data====", customer);

  const { data, isPending } = api.property.getFeaturedProperties.useQuery(
    selectedTab ? { propertyType: selectedTab } : skipToken,
  );

  const tabProperties = data ?? [];

  return (
    <div className="bg-white">
      <div className="container mx-auto max-w-full py-9 md:py-[40px] lg:py-[55px] xl:py-[65px] 2xl:py-[70px]">
        <div className="flex flex-col items-center justify-center">
          {/* Heading badge */}
          <HeadingBadge
            content="Hot Properties"
            className="mb-1 md:mb-2 lg:mb-2.5 xl:mb-[14px]"
          />
          {/* section heading */}
          <div className="mb-3 font-airbnb_w_xbd text-center text-3xl font-semibold text-text-700 md:mb-[14px] lg:mb-4 lg:text-4xl xl:mb-[18px] xl:text-[40px] xl:leading-[48px] 2xl:mb-6 2xl:text-[48px] 2xl:leading-[56px]">
            Explore Featured Listings
          </div>
        </div>

        {/*categories and property cards*/}
        <Tabs defaultValue="View All">
          <ScrollArea>
            <TabsList className="mb-8 flex flex-row gap-4 md:mb-9 md:gap-8 lg:mb-[45px] xl:mb-[50px] 2xl:mb-[60px]">
              {/* skeleton for tabs */}
              <TabsTrigger
                className="rounded bg-primary-0 px-3 py-2 font-airbnb_w_md text-xs font-medium text-primary-600 data-[state=active]:rounded data-[state=active]:bg-primary-600 data-[state=active]:text-primary-50 md:text-sm lg:text-base 2xl:text-lg"
                value={"View All"}
              >
                View All
              </TabsTrigger>

              {/* all other dynamic tabs */}
              {propertyTypes.map((item) => (
                <TabsTrigger
                  key={item.id}
                  className="rounded bg-primary-0 px-3 py-2 font-airbnb_w_md text-xs font-medium text-primary-600 data-[state=active]:rounded data-[state=active]:bg-primary-600 data-[state=active]:text-primary-50 md:text-sm lg:text-base 2xl:text-lg"
                  value={item.name}
                  onClick={() => setSelectedTab(item.name)}
                >
                  {item.name}
                </TabsTrigger>
              ))}
            </TabsList>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          {/* View All Tab Content */}
          <TabsContent value="View All">
            <Carousel
              opts={{
                align: "start",
                loop: true,
              }}
              plugins={[Autoplay({ delay: 2000 })]}
              className="w-full"
            >
              <CarouselContent>
                {properties.map((item) => (
                  <CarouselItem
                    key={item.id}
                    className="flex-shrink-0 basis-[356px] py-1 md:basis-[364px]"
                  >
                    <PropertyCard
                      key={item.id}
                      locationIcon="/icons/location.svg"
                      id={item.id}
                      property={item}
                      propertyOwnerId={item.userId}
                      contactOrCheckResponsesButton={
                        <ContactAgentButton
                          agentId={item.userId}
                          connectedAgentId={
                            customer?.connections[0]?.agentId ?? ""
                          }
                        />
                      }
                      likePropertyButton={
                        <LikePropertyButton
                          propertyId={item.id}
                          isPropertyLiked={!!item.customerFavourites.length}
                        />
                      }
                      sharePropertyButton={
                        <SharePropertyButton propertyId={item.id} />
                      }
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </TabsContent>

          {/* dynamic tabs content */}
          {propertyTypes.map((category, i) => (
            <TabsContent value={category.name} key={i}>
              <Carousel
                opts={{
                  align: "start",
                }}
                className="w-full"
              >
                <CarouselContent>
                  {isPending ? (
                    Array.from({ length: 10 }).map(() => (
                      <CarouselItem className="basis-[356px] md:basis-[364px]">
                        <PropertyCardSkeleton />
                      </CarouselItem>
                    ))
                  ) : tabProperties.length > 0 ? (
                    tabProperties.map((item, index) => (
                      <CarouselItem
                        key={index}
                        className="basis-[356px] md:basis-[364px]"
                      >
                        <PropertyCard
                          key={item.id}
                          locationIcon="/icons/location.svg"
                          id={item.id}
                          property={item}
                          propertyOwnerId={item.userId}
                          contactOrCheckResponsesButton={
                            <ContactAgentButton
                              agentId={item.userId}
                              connectedAgentId={
                                customer?.connections[0]?.agentId ?? ""
                              }
                            />
                          }
                          likePropertyButton={
                            <LikePropertyButton
                              propertyId={item.id}
                              isPropertyLiked={!!item.customerFavourites.length}
                            />
                          }
                          sharePropertyButton={
                            <SharePropertyButton propertyId={item.id} />
                          }
                        />
                      </CarouselItem>
                    ))
                  ) : (
                    <CarouselItem className="basis-[356px] md:basis-[364px]">
                      no properties found
                      {/* <PropertyCard
                                key={item.id}
                                locationIcon="/icons/location.svg"
                                id={item.id}
                                property={item}
                                propertyOwnerId={item.userId}
                                contactOrCheckResponsesButton={
                                  <ContactAgentButton agentId={item.userId} />
                                }
                                likePropertyButton={
                                  <LikePropertyButton
                                    propertyId={item.id}
                                    isPropertyLiked={
                                      !!item.customerFavourites.length
                                    }
                                  />
                                }
                                sharePropertyButton={
                                  <SharePropertyButton
                                    propertyId={item.id}
                                    showWhatsappIcon
                                  />
                                }
                              /> */}
                    </CarouselItem>
                  )}
                  {}
                </CarouselContent>
              </Carousel>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
};
export default FeaturedListing;
