"use client";
import { RealEstateBottomValues } from "~/app/utils/constants";
const BottomCardTitle = ({ title }: { title: string }) => {
    return (
        <>
            <h2 className="font-airbnb_w_xbd text-2xl font-extrabold text-secondary-2-700 md:text-3xl xl:text-[40px] xl:leading-[48px] 2xl:text-[48px] 2xl:leading-[56px]">
                {title}
            </h2>
        </>
    );
};

const BottomCardDescription = ({ description }: { description: string }) => {
    return (
        <>
            <div className="text-center font-airbnb_w_md text-base font-medium text-text-800 lg:text-lg 2xl:text-[20px] 2xl:leading-[30px]">
                {description}
            </div>
        </>
    );
};
const RealEstateBottomCard = () => {
    return (
        <>
            <div style={{
                backgroundImage :'url(/images/real-estate-bottom-background.png)'
            }} className="rounded-[32px] px-[16px] py-[24px] lg:px-[24px] lg:py-[40px] xl:py-[60px] 2xl:px-[32px] 2xl:bg-bottom 2xl:bg-no-repeat bg-repeat-x bg-text-30">
                <div className="flex flex-col gap-[20px] md:flex-row md:gap-[24px] lg:gap-[80px] xl:gap-[160px] 2xl:flex-col 2xl:justify-between">
                    {RealEstateBottomValues.map((item) => {
                        return (
                            <>
                                <div className="flex flex-col items-center justify-center gap-[6px] md:gap-[8px] lg:gap-[10px] xl:gap-[12px]">
                                    <BottomCardTitle title={item.title} />
                                    <BottomCardDescription description={item.description} />
                                </div>
                            </>
                        );
                    })}
                </div>
            </div>
        </>
    );
};
export default RealEstateBottomCard;
