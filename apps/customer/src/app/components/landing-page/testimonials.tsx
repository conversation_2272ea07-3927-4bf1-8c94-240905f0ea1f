"use client";
import Image from "next/image";
import React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import { Prisma } from "@repo/database";
type Testimonials = Prisma.CustomerTestimonialsGetPayload<{
  select: {
    id: true;
    name: true;
    description: true;
    fileKey: true;
    filePublicUrl: true;
    rating: true;
    city: {
      select: {
        name: true;
      };
    };
  };
}>;
const Testimonials = ({ testimonials }: { testimonials: Testimonials[] }) => {
  //   const testimonials = api.user.getAllTestimonials.useQuery()
  const oneStar = (
    <svg
      className=""
      width="186"
      height="63"
      viewBox="0 0 186 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="star">
        <path
          id="Shape"
          d="M14.2988 33.2474C14.6268 32.7368 15.3732 32.7368 15.7012 33.2474L19.9383 39.8441C20.0511 40.0198 20.2258 40.1467 20.4278 40.1998L28.011 42.191C28.598 42.3451 28.8286 43.055 28.4443 43.5247L23.4798 49.5929C23.3476 49.7545 23.2808 49.9599 23.2928 50.1684L23.7424 57.9957C23.7772 58.6016 23.1734 59.0403 22.6079 58.82L15.3025 55.9736C15.108 55.8978 14.892 55.8978 14.6975 55.9736L7.39212 58.82C6.82663 59.0403 6.22282 58.6016 6.25762 57.9957L6.70719 50.1684C6.71916 49.9599 6.65244 49.7545 6.52021 49.5929L1.55569 43.5247C1.17139 43.055 1.40203 42.3451 1.98903 42.191L9.57223 40.1998C9.77419 40.1467 9.94889 40.0198 10.0617 39.8441L14.2988 33.2474Z"
          fill="#F8C131"
        />
        <path
          id="Shape_2"
          d="M50.0184 16.684C50.4776 15.9692 51.5224 15.9692 51.9816 16.684L57.9136 25.9195C58.0716 26.1654 58.3161 26.3431 58.5989 26.4174L69.2154 29.2051C70.0372 29.4209 70.36 30.4146 69.822 31.0722L62.8717 39.5678C62.6866 39.794 62.5932 40.0815 62.6099 40.3734L63.2393 51.3317C63.2881 52.18 62.4427 52.7942 61.651 52.4857L51.4236 48.5008C51.1512 48.3947 50.8488 48.3947 50.5764 48.5008L40.349 52.4857C39.5573 52.7942 38.7119 52.18 38.7607 51.3317L39.3901 40.3734C39.4068 40.0815 39.3134 39.794 39.1283 39.5678L32.178 31.0722C31.64 30.4146 31.9628 29.4209 32.7846 29.2051L43.4011 26.4174C43.6839 26.3431 43.9284 26.1654 44.0864 25.9195L50.0184 16.684Z"
          fill="gray"
        />
        <path
          id="Shape_3"
          d="M134.018 16.684C134.478 15.9692 135.522 15.9692 135.982 16.684L141.914 25.9195C142.072 26.1654 142.316 26.3431 142.599 26.4174L153.215 29.2051C154.037 29.4209 154.36 30.4146 153.822 31.0722L146.872 39.5678C146.687 39.794 146.593 40.0815 146.61 40.3734L147.239 51.3317C147.288 52.18 146.443 52.7942 145.651 52.4857L135.424 48.5008C135.151 48.3947 134.849 48.3947 134.576 48.5008L124.349 52.4857C123.557 52.7942 122.712 52.18 122.761 51.3317L123.39 40.3734C123.407 40.0815 123.313 39.794 123.128 39.5678L116.178 31.0722C115.64 30.4146 115.963 29.4209 116.785 29.2051L127.401 26.4174C127.684 26.3431 127.928 26.1654 128.086 25.9195L134.018 16.684Z"
          fill="gray"
        />
        <path
          id="Shape_4"
          d="M170.299 33.2474C170.627 32.7368 171.373 32.7368 171.701 33.2474L175.938 39.8441C176.051 40.0198 176.226 40.1467 176.428 40.1998L184.011 42.191C184.598 42.3451 184.829 43.055 184.444 43.5247L179.48 49.5929C179.348 49.7545 179.281 49.9599 179.293 50.1684L179.742 57.9957C179.777 58.6016 179.173 59.0403 178.608 58.82L171.303 55.9736C171.108 55.8978 170.892 55.8978 170.697 55.9736L163.392 58.82C162.827 59.0403 162.223 58.6016 162.258 57.9957L162.707 50.1684C162.719 49.9599 162.652 49.7545 162.52 49.5929L157.556 43.5247C157.171 43.055 157.402 42.3451 157.989 42.191L165.572 40.1998C165.774 40.1467 165.949 40.0198 166.062 39.8441L170.299 33.2474Z"
          fill="gray"
        />
        <path
          id="Shape_5"
          d="M92.7847 2.04792C93.3532 1.16282 94.6468 1.16282 95.2153 2.04792L102.56 13.4823C102.755 13.7868 103.058 14.0068 103.408 14.0987L116.552 17.5502C117.57 17.8173 117.97 19.0477 117.303 19.8619L108.698 30.3801C108.469 30.6603 108.353 31.0163 108.374 31.3776L109.153 44.9451C109.214 45.9953 108.167 46.7557 107.187 46.3738L94.5244 41.4401C94.1871 41.3087 93.8129 41.3087 93.4756 41.4401L80.813 46.3738C79.8328 46.7557 78.7862 45.9953 78.8465 44.945L79.6258 31.3776C79.6466 31.0163 79.5309 30.6603 79.3017 30.3801L70.6965 19.8619C70.0304 19.0477 70.4302 17.8173 71.4476 17.5502L84.5919 14.0987C84.9419 14.0068 85.2447 13.7868 85.4403 13.4823L92.7847 2.04792Z"
          fill="gray"
        />
      </g>
    </svg>
  );
  const twoStars = (
    <svg
      className=""
      width="186"
      height="63"
      viewBox="0 0 186 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="star">
        <path
          id="Shape"
          d="M14.2988 33.2474C14.6268 32.7368 15.3732 32.7368 15.7012 33.2474L19.9383 39.8441C20.0511 40.0198 20.2258 40.1467 20.4278 40.1998L28.011 42.191C28.598 42.3451 28.8286 43.055 28.4443 43.5247L23.4798 49.5929C23.3476 49.7545 23.2808 49.9599 23.2928 50.1684L23.7424 57.9957C23.7772 58.6016 23.1734 59.0403 22.6079 58.82L15.3025 55.9736C15.108 55.8978 14.892 55.8978 14.6975 55.9736L7.39212 58.82C6.82663 59.0403 6.22282 58.6016 6.25762 57.9957L6.70719 50.1684C6.71916 49.9599 6.65244 49.7545 6.52021 49.5929L1.55569 43.5247C1.17139 43.055 1.40203 42.3451 1.98903 42.191L9.57223 40.1998C9.77419 40.1467 9.94889 40.0198 10.0617 39.8441L14.2988 33.2474Z"
          fill="#F8C131"
        />
        <path
          id="Shape_2"
          d="M50.0184 16.684C50.4776 15.9692 51.5224 15.9692 51.9816 16.684L57.9136 25.9195C58.0716 26.1654 58.3161 26.3431 58.5989 26.4174L69.2154 29.2051C70.0372 29.4209 70.36 30.4146 69.822 31.0722L62.8717 39.5678C62.6866 39.794 62.5932 40.0815 62.6099 40.3734L63.2393 51.3317C63.2881 52.18 62.4427 52.7942 61.651 52.4857L51.4236 48.5008C51.1512 48.3947 50.8488 48.3947 50.5764 48.5008L40.349 52.4857C39.5573 52.7942 38.7119 52.18 38.7607 51.3317L39.3901 40.3734C39.4068 40.0815 39.3134 39.794 39.1283 39.5678L32.178 31.0722C31.64 30.4146 31.9628 29.4209 32.7846 29.2051L43.4011 26.4174C43.6839 26.3431 43.9284 26.1654 44.0864 25.9195L50.0184 16.684Z"
          fill="#F8C131"
        />
        <path
          id="Shape_3"
          d="M134.018 16.684C134.478 15.9692 135.522 15.9692 135.982 16.684L141.914 25.9195C142.072 26.1654 142.316 26.3431 142.599 26.4174L153.215 29.2051C154.037 29.4209 154.36 30.4146 153.822 31.0722L146.872 39.5678C146.687 39.794 146.593 40.0815 146.61 40.3734L147.239 51.3317C147.288 52.18 146.443 52.7942 145.651 52.4857L135.424 48.5008C135.151 48.3947 134.849 48.3947 134.576 48.5008L124.349 52.4857C123.557 52.7942 122.712 52.18 122.761 51.3317L123.39 40.3734C123.407 40.0815 123.313 39.794 123.128 39.5678L116.178 31.0722C115.64 30.4146 115.963 29.4209 116.785 29.2051L127.401 26.4174C127.684 26.3431 127.928 26.1654 128.086 25.9195L134.018 16.684Z"
          fill="gray"
        />
        <path
          id="Shape_4"
          d="M170.299 33.2474C170.627 32.7368 171.373 32.7368 171.701 33.2474L175.938 39.8441C176.051 40.0198 176.226 40.1467 176.428 40.1998L184.011 42.191C184.598 42.3451 184.829 43.055 184.444 43.5247L179.48 49.5929C179.348 49.7545 179.281 49.9599 179.293 50.1684L179.742 57.9957C179.777 58.6016 179.173 59.0403 178.608 58.82L171.303 55.9736C171.108 55.8978 170.892 55.8978 170.697 55.9736L163.392 58.82C162.827 59.0403 162.223 58.6016 162.258 57.9957L162.707 50.1684C162.719 49.9599 162.652 49.7545 162.52 49.5929L157.556 43.5247C157.171 43.055 157.402 42.3451 157.989 42.191L165.572 40.1998C165.774 40.1467 165.949 40.0198 166.062 39.8441L170.299 33.2474Z"
          fill="gray"
        />
        <path
          id="Shape_5"
          d="M92.7847 2.04792C93.3532 1.16282 94.6468 1.16282 95.2153 2.04792L102.56 13.4823C102.755 13.7868 103.058 14.0068 103.408 14.0987L116.552 17.5502C117.57 17.8173 117.97 19.0477 117.303 19.8619L108.698 30.3801C108.469 30.6603 108.353 31.0163 108.374 31.3776L109.153 44.9451C109.214 45.9953 108.167 46.7557 107.187 46.3738L94.5244 41.4401C94.1871 41.3087 93.8129 41.3087 93.4756 41.4401L80.813 46.3738C79.8328 46.7557 78.7862 45.9953 78.8465 44.945L79.6258 31.3776C79.6466 31.0163 79.5309 30.6603 79.3017 30.3801L70.6965 19.8619C70.0304 19.0477 70.4302 17.8173 71.4476 17.5502L84.5919 14.0987C84.9419 14.0068 85.2447 13.7868 85.4403 13.4823L92.7847 2.04792Z"
          fill="gray"
        />
      </g>
    </svg>
  );
  const threeStars = (
    <svg
      className=""
      width="186"
      height="63"
      viewBox="0 0 186 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="star">
        <path
          id="Shape"
          d="M14.2988 33.2474C14.6268 32.7368 15.3732 32.7368 15.7012 33.2474L19.9383 39.8441C20.0511 40.0198 20.2258 40.1467 20.4278 40.1998L28.011 42.191C28.598 42.3451 28.8286 43.055 28.4443 43.5247L23.4798 49.5929C23.3476 49.7545 23.2808 49.9599 23.2928 50.1684L23.7424 57.9957C23.7772 58.6016 23.1734 59.0403 22.6079 58.82L15.3025 55.9736C15.108 55.8978 14.892 55.8978 14.6975 55.9736L7.39212 58.82C6.82663 59.0403 6.22282 58.6016 6.25762 57.9957L6.70719 50.1684C6.71916 49.9599 6.65244 49.7545 6.52021 49.5929L1.55569 43.5247C1.17139 43.055 1.40203 42.3451 1.98903 42.191L9.57223 40.1998C9.77419 40.1467 9.94889 40.0198 10.0617 39.8441L14.2988 33.2474Z"
          fill="#F8C131"
        />
        <path
          id="Shape_2"
          d="M50.0184 16.684C50.4776 15.9692 51.5224 15.9692 51.9816 16.684L57.9136 25.9195C58.0716 26.1654 58.3161 26.3431 58.5989 26.4174L69.2154 29.2051C70.0372 29.4209 70.36 30.4146 69.822 31.0722L62.8717 39.5678C62.6866 39.794 62.5932 40.0815 62.6099 40.3734L63.2393 51.3317C63.2881 52.18 62.4427 52.7942 61.651 52.4857L51.4236 48.5008C51.1512 48.3947 50.8488 48.3947 50.5764 48.5008L40.349 52.4857C39.5573 52.7942 38.7119 52.18 38.7607 51.3317L39.3901 40.3734C39.4068 40.0815 39.3134 39.794 39.1283 39.5678L32.178 31.0722C31.64 30.4146 31.9628 29.4209 32.7846 29.2051L43.4011 26.4174C43.6839 26.3431 43.9284 26.1654 44.0864 25.9195L50.0184 16.684Z"
          fill="#F8C131"
        />
        <path
          id="Shape_3"
          d="M134.018 16.684C134.478 15.9692 135.522 15.9692 135.982 16.684L141.914 25.9195C142.072 26.1654 142.316 26.3431 142.599 26.4174L153.215 29.2051C154.037 29.4209 154.36 30.4146 153.822 31.0722L146.872 39.5678C146.687 39.794 146.593 40.0815 146.61 40.3734L147.239 51.3317C147.288 52.18 146.443 52.7942 145.651 52.4857L135.424 48.5008C135.151 48.3947 134.849 48.3947 134.576 48.5008L124.349 52.4857C123.557 52.7942 122.712 52.18 122.761 51.3317L123.39 40.3734C123.407 40.0815 123.313 39.794 123.128 39.5678L116.178 31.0722C115.64 30.4146 115.963 29.4209 116.785 29.2051L127.401 26.4174C127.684 26.3431 127.928 26.1654 128.086 25.9195L134.018 16.684Z"
          fill="gray"
        />
        <path
          id="Shape_4"
          d="M170.299 33.2474C170.627 32.7368 171.373 32.7368 171.701 33.2474L175.938 39.8441C176.051 40.0198 176.226 40.1467 176.428 40.1998L184.011 42.191C184.598 42.3451 184.829 43.055 184.444 43.5247L179.48 49.5929C179.348 49.7545 179.281 49.9599 179.293 50.1684L179.742 57.9957C179.777 58.6016 179.173 59.0403 178.608 58.82L171.303 55.9736C171.108 55.8978 170.892 55.8978 170.697 55.9736L163.392 58.82C162.827 59.0403 162.223 58.6016 162.258 57.9957L162.707 50.1684C162.719 49.9599 162.652 49.7545 162.52 49.5929L157.556 43.5247C157.171 43.055 157.402 42.3451 157.989 42.191L165.572 40.1998C165.774 40.1467 165.949 40.0198 166.062 39.8441L170.299 33.2474Z"
          fill="gray"
        />
        <path
          id="Shape_5"
          d="M92.7847 2.04792C93.3532 1.16282 94.6468 1.16282 95.2153 2.04792L102.56 13.4823C102.755 13.7868 103.058 14.0068 103.408 14.0987L116.552 17.5502C117.57 17.8173 117.97 19.0477 117.303 19.8619L108.698 30.3801C108.469 30.6603 108.353 31.0163 108.374 31.3776L109.153 44.9451C109.214 45.9953 108.167 46.7557 107.187 46.3738L94.5244 41.4401C94.1871 41.3087 93.8129 41.3087 93.4756 41.4401L80.813 46.3738C79.8328 46.7557 78.7862 45.9953 78.8465 44.945L79.6258 31.3776C79.6466 31.0163 79.5309 30.6603 79.3017 30.3801L70.6965 19.8619C70.0304 19.0477 70.4302 17.8173 71.4476 17.5502L84.5919 14.0987C84.9419 14.0068 85.2447 13.7868 85.4403 13.4823L92.7847 2.04792Z"
          fill="#F8C131"
        />
      </g>
    </svg>
  );
  const fourStars = (
    <svg
      className=""
      width="186"
      height="63"
      viewBox="0 0 186 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="star">
        <path
          id="Shape"
          d="M14.2988 33.2474C14.6268 32.7368 15.3732 32.7368 15.7012 33.2474L19.9383 39.8441C20.0511 40.0198 20.2258 40.1467 20.4278 40.1998L28.011 42.191C28.598 42.3451 28.8286 43.055 28.4443 43.5247L23.4798 49.5929C23.3476 49.7545 23.2808 49.9599 23.2928 50.1684L23.7424 57.9957C23.7772 58.6016 23.1734 59.0403 22.6079 58.82L15.3025 55.9736C15.108 55.8978 14.892 55.8978 14.6975 55.9736L7.39212 58.82C6.82663 59.0403 6.22282 58.6016 6.25762 57.9957L6.70719 50.1684C6.71916 49.9599 6.65244 49.7545 6.52021 49.5929L1.55569 43.5247C1.17139 43.055 1.40203 42.3451 1.98903 42.191L9.57223 40.1998C9.77419 40.1467 9.94889 40.0198 10.0617 39.8441L14.2988 33.2474Z"
          fill="#F8C131"
        />
        <path
          id="Shape_2"
          d="M50.0184 16.684C50.4776 15.9692 51.5224 15.9692 51.9816 16.684L57.9136 25.9195C58.0716 26.1654 58.3161 26.3431 58.5989 26.4174L69.2154 29.2051C70.0372 29.4209 70.36 30.4146 69.822 31.0722L62.8717 39.5678C62.6866 39.794 62.5932 40.0815 62.6099 40.3734L63.2393 51.3317C63.2881 52.18 62.4427 52.7942 61.651 52.4857L51.4236 48.5008C51.1512 48.3947 50.8488 48.3947 50.5764 48.5008L40.349 52.4857C39.5573 52.7942 38.7119 52.18 38.7607 51.3317L39.3901 40.3734C39.4068 40.0815 39.3134 39.794 39.1283 39.5678L32.178 31.0722C31.64 30.4146 31.9628 29.4209 32.7846 29.2051L43.4011 26.4174C43.6839 26.3431 43.9284 26.1654 44.0864 25.9195L50.0184 16.684Z"
          fill="#F8C131"
        />
        <path
          id="Shape_3"
          d="M134.018 16.684C134.478 15.9692 135.522 15.9692 135.982 16.684L141.914 25.9195C142.072 26.1654 142.316 26.3431 142.599 26.4174L153.215 29.2051C154.037 29.4209 154.36 30.4146 153.822 31.0722L146.872 39.5678C146.687 39.794 146.593 40.0815 146.61 40.3734L147.239 51.3317C147.288 52.18 146.443 52.7942 145.651 52.4857L135.424 48.5008C135.151 48.3947 134.849 48.3947 134.576 48.5008L124.349 52.4857C123.557 52.7942 122.712 52.18 122.761 51.3317L123.39 40.3734C123.407 40.0815 123.313 39.794 123.128 39.5678L116.178 31.0722C115.64 30.4146 115.963 29.4209 116.785 29.2051L127.401 26.4174C127.684 26.3431 127.928 26.1654 128.086 25.9195L134.018 16.684Z"
          fill="#F8C131"
        />
        <path
          id="Shape_4"
          d="M170.299 33.2474C170.627 32.7368 171.373 32.7368 171.701 33.2474L175.938 39.8441C176.051 40.0198 176.226 40.1467 176.428 40.1998L184.011 42.191C184.598 42.3451 184.829 43.055 184.444 43.5247L179.48 49.5929C179.348 49.7545 179.281 49.9599 179.293 50.1684L179.742 57.9957C179.777 58.6016 179.173 59.0403 178.608 58.82L171.303 55.9736C171.108 55.8978 170.892 55.8978 170.697 55.9736L163.392 58.82C162.827 59.0403 162.223 58.6016 162.258 57.9957L162.707 50.1684C162.719 49.9599 162.652 49.7545 162.52 49.5929L157.556 43.5247C157.171 43.055 157.402 42.3451 157.989 42.191L165.572 40.1998C165.774 40.1467 165.949 40.0198 166.062 39.8441L170.299 33.2474Z"
          fill="gray"
        />
        <path
          id="Shape_5"
          d="M92.7847 2.04792C93.3532 1.16282 94.6468 1.16282 95.2153 2.04792L102.56 13.4823C102.755 13.7868 103.058 14.0068 103.408 14.0987L116.552 17.5502C117.57 17.8173 117.97 19.0477 117.303 19.8619L108.698 30.3801C108.469 30.6603 108.353 31.0163 108.374 31.3776L109.153 44.9451C109.214 45.9953 108.167 46.7557 107.187 46.3738L94.5244 41.4401C94.1871 41.3087 93.8129 41.3087 93.4756 41.4401L80.813 46.3738C79.8328 46.7557 78.7862 45.9953 78.8465 44.945L79.6258 31.3776C79.6466 31.0163 79.5309 30.6603 79.3017 30.3801L70.6965 19.8619C70.0304 19.0477 70.4302 17.8173 71.4476 17.5502L84.5919 14.0987C84.9419 14.0068 85.2447 13.7868 85.4403 13.4823L92.7847 2.04792Z"
          fill="#F8C131"
        />
      </g>
    </svg>
  );
  const fiveStars = (
    <svg
      width="186"
      height="63"
      viewBox="0 0 186 63"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="star">
        <path
          id="Shape"
          d="M14.2988 33.2474C14.6268 32.7368 15.3732 32.7368 15.7012 33.2474L19.9383 39.8441C20.0511 40.0198 20.2258 40.1467 20.4278 40.1998L28.011 42.191C28.598 42.3451 28.8286 43.055 28.4443 43.5247L23.4798 49.5929C23.3476 49.7545 23.2808 49.9599 23.2928 50.1684L23.7424 57.9957C23.7772 58.6016 23.1734 59.0403 22.6079 58.82L15.3025 55.9736C15.108 55.8978 14.892 55.8978 14.6975 55.9736L7.39212 58.82C6.82663 59.0403 6.22282 58.6016 6.25762 57.9957L6.70719 50.1684C6.71916 49.9599 6.65244 49.7545 6.52021 49.5929L1.55569 43.5247C1.17139 43.055 1.40203 42.3451 1.98903 42.191L9.57223 40.1998C9.77419 40.1467 9.94889 40.0198 10.0617 39.8441L14.2988 33.2474Z"
          fill="#F8C131"
        />
        <path
          id="Shape_2"
          d="M50.0184 16.684C50.4776 15.9692 51.5224 15.9692 51.9816 16.684L57.9136 25.9195C58.0716 26.1654 58.3161 26.3431 58.5989 26.4174L69.2154 29.2051C70.0372 29.4209 70.36 30.4146 69.822 31.0722L62.8717 39.5678C62.6866 39.794 62.5932 40.0815 62.6099 40.3734L63.2393 51.3317C63.2881 52.18 62.4427 52.7942 61.651 52.4857L51.4236 48.5008C51.1512 48.3947 50.8488 48.3947 50.5764 48.5008L40.349 52.4857C39.5573 52.7942 38.7119 52.18 38.7607 51.3317L39.3901 40.3734C39.4068 40.0815 39.3134 39.794 39.1283 39.5678L32.178 31.0722C31.64 30.4146 31.9628 29.4209 32.7846 29.2051L43.4011 26.4174C43.6839 26.3431 43.9284 26.1654 44.0864 25.9195L50.0184 16.684Z"
          fill="#F8C131"
        />
        <path
          id="Shape_3"
          d="M134.018 16.684C134.478 15.9692 135.522 15.9692 135.982 16.684L141.914 25.9195C142.072 26.1654 142.316 26.3431 142.599 26.4174L153.215 29.2051C154.037 29.4209 154.36 30.4146 153.822 31.0722L146.872 39.5678C146.687 39.794 146.593 40.0815 146.61 40.3734L147.239 51.3317C147.288 52.18 146.443 52.7942 145.651 52.4857L135.424 48.5008C135.151 48.3947 134.849 48.3947 134.576 48.5008L124.349 52.4857C123.557 52.7942 122.712 52.18 122.761 51.3317L123.39 40.3734C123.407 40.0815 123.313 39.794 123.128 39.5678L116.178 31.0722C115.64 30.4146 115.963 29.4209 116.785 29.2051L127.401 26.4174C127.684 26.3431 127.928 26.1654 128.086 25.9195L134.018 16.684Z"
          fill="#F8C131"
        />
        <path
          id="Shape_4"
          d="M170.299 33.2474C170.627 32.7368 171.373 32.7368 171.701 33.2474L175.938 39.8441C176.051 40.0198 176.226 40.1467 176.428 40.1998L184.011 42.191C184.598 42.3451 184.829 43.055 184.444 43.5247L179.48 49.5929C179.348 49.7545 179.281 49.9599 179.293 50.1684L179.742 57.9957C179.777 58.6016 179.173 59.0403 178.608 58.82L171.303 55.9736C171.108 55.8978 170.892 55.8978 170.697 55.9736L163.392 58.82C162.827 59.0403 162.223 58.6016 162.258 57.9957L162.707 50.1684C162.719 49.9599 162.652 49.7545 162.52 49.5929L157.556 43.5247C157.171 43.055 157.402 42.3451 157.989 42.191L165.572 40.1998C165.774 40.1467 165.949 40.0198 166.062 39.8441L170.299 33.2474Z"
          fill="#F8C131"
        />
        <path
          id="Shape_5"
          d="M92.7847 2.04792C93.3532 1.16282 94.6468 1.16282 95.2153 2.04792L102.56 13.4823C102.755 13.7868 103.058 14.0068 103.408 14.0987L116.552 17.5502C117.57 17.8173 117.97 19.0477 117.303 19.8619L108.698 30.3801C108.469 30.6603 108.353 31.0163 108.374 31.3776L109.153 44.9451C109.214 45.9953 108.167 46.7557 107.187 46.3738L94.5244 41.4401C94.1871 41.3087 93.8129 41.3087 93.4756 41.4401L80.813 46.3738C79.8328 46.7557 78.7862 45.9953 78.8465 44.945L79.6258 31.3776C79.6466 31.0163 79.5309 30.6603 79.3017 30.3801L70.6965 19.8619C70.0304 19.0477 70.4302 17.8173 71.4476 17.5502L84.5919 14.0987C84.9419 14.0068 85.2447 13.7868 85.4403 13.4823L92.7847 2.04792Z"
          fill="#F8C131"
        />
      </g>
    </svg>
  );

  return (
    <div className="bg-[#F9F6F1] py-9 md:py-[50px] lg:py-[55px] xl:py-[65px] 2xl:py-[70px]">
      <div className="container mx-auto max-w-full">
        <div className="flex flex-col items-center gap-[110px] md:gap-[72px] lg:gap-[82px] xl:gap-[92px] 2xl:gap-[97px]">
          {/* heading */}
          <div className="flex w-full flex-col items-center gap-1.5 md:gap-4 lg:gap-[18px]">
            <p className="font-airbnb_w_md text-lg font-medium text-primary-2-800 lg:text-xl 2xl:text-2xl">
              Hear from real estate professionals
            </p>
            <h2 className="p-1.5 text-center font-airbnb_w_xbd text-2xl font-extrabold text-text-700 md:w-[676px] md:p-2.5 md:text-3xl lg:w-[811px] lg:p-3 lg:text-4xl xl:w-[1073px] xl:text-[48px] xl:leading-[56px]">
              Listen what our customers have to say
            </h2>
          </div>
          {/* cards container */}
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="pt-11">
              {testimonials.map((item) => {
                return (
                  <CarouselItem key={item.id} className="basis-[326px]">
                    {/* card */}
                    <div className="relative w-full bg-white px-8 pb-8 pt-[82px]">
                      {/* image */}
                      <div className="absolute -top-10 left-1/2 flex size-[108px] -translate-x-1/2 items-center justify-center rounded-full border border-black">
                        <div className="relative aspect-square w-[90px]">
                          <Image
                            src={"/images/placeholder-user-image.jpg"}
                            alt="profile"
                            fill
                            className="relative rounded-full object-cover"
                          ></Image>
                        </div>
                      </div>
                      <div className="flex flex-col items-center gap-5">
                        {/* name */}
                        <div className="flex flex-col items-center gap-0.5">
                          <h2 className="font-airbnb_w_bd text-lg font-bold text-text-550">
                            {item.name}
                          </h2>
                          <p className="font-airbnb_w_bk tracking-[2px] text-text-550">
                            {item.city.name}
                          </p>
                        </div>
                        {/* comment */}
                        <p className="h-[72px] text-center font-airbnb_w_bk text-base text-text-600">
                          {item.description}
                        </p>
                        {/* stars */}
                        {item.rating === 1
                          ? oneStar
                          : item.rating === 2
                            ? twoStars
                            : item.rating === 3
                              ? threeStars
                              : item.rating === 4
                                ? fourStars
                                : fiveStars}
                      </div>
                    </div>
                  </CarouselItem>
                );
              })}
            </CarouselContent>
          </Carousel>
          <div className=""></div>
        </div>
      </div>
    </div>
  );
};

export default Testimonials;
