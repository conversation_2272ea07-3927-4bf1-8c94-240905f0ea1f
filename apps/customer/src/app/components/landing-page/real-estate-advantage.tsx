"use client";
import { RealEstateAdvantages, RealEstateImages } from "~/app/utils/constants";
import HeadingBadge from "../shared/heading-badge";
import RealEstateAdvantageCard from "./real-estate-advantage-card";
import { useState, useEffect, useRef } from "react";
import RealEstateBottomCard from "./real-estate-bottom-card";
import Image from "next/image";

const RealEstateAdvantage = () => {
  const [cardId, setCardId] = useState<string>("1");
  const showedImages = RealEstateImages.filter((item) => item.id === cardId);
  const unShowedImages = RealEstateImages.filter((item) => item.id !== cardId);
  const sectionRef = useRef<HTMLDivElement>(null);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isTransitioning) {
            const cardIndex = cardRefs.current.findIndex(
              (ref) => ref === entry.target,
            );
            if (cardIndex !== -1) {
              setIsTransitioning(true);
              setCardId(RealEstateAdvantages[cardIndex]?.id ?? "1");

              setTimeout(() => {
                setIsTransitioning(false);
              }, 500);
            }
          }
        });
      },
      {
        threshold: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
        rootMargin: "-40% 0px -40% 0px",
      },
    );

    cardRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    return () => {
      cardRefs.current.forEach((ref) => {
        if (ref) {
          observer.unobserve(ref);
        }
      });
    };
  }, [isTransitioning]);

  return (
    <>
      <div className="bg-gradient-to-b from-black to-[#5F2800]">
        <div className="container mx-auto max-w-full py-[32px] md:py-[40px] lg:py-[55px] xl:py-[65px] 2xl:py-[70px]">
          <div className="flex items-center justify-center">
            {/* Heading badge */}
            <HeadingBadge
              content="Your Real Estate Advantage"
              fileUrl="/icons/grid-icon.svg"
              className="mb-1 md:mb-2 lg:mb-2.5 xl:mb-[14px]"
            />
          </div>
          <h2 className="mb-3 mt-1 text-center font-airbnb_w_xbd text-2xl font-extrabold text-text-30 md:mb-[14px] md:mt-[8px] md:text-3xl lg:mb-[16px] lg:mt-[10px] xl:mb-[18px] xl:mt-[14px] xl:text-4xl 2xl:text-5xl">
            Empower Your Real Estate Journey with Deer
          </h2>
          <h4 className="mb-[32px] text-center font-airbnb_w_md text-lg font-medium text-secondary-2-200 md:mb-[36px] lg:mb-[45px] lg:text-[20px] lg:leading-[30px] xl:mb-[50px] 2xl:mb-[60px] 2xl:text-[24px] 2xl:leading-[32px]">
            Discover the unique features that set us apart
          </h4>

          <div
            id="real-estate-advantage"
            ref={sectionRef}
            className="flex flex-col gap-[30px] md:gap-[40px] lg:gap-[36px] xl:gap-[50px] 2xl:flex-row"
          >
            <div className="block md:hidden lg:block">
              <div className="flex items-stretch lg:gap-[36px] xl:gap-[66px]">
                <div className="flex flex-col">
                  {RealEstateAdvantages.map((item, index) => {
                    return (
                      <div
                        key={item.id}
                        ref={(el) => {
                          cardRefs.current[index] = el;
                        }}
                      >
                        <RealEstateAdvantageCard
                          active={item.id === cardId}
                          title={item.title}
                          description={item.description}
                        />
                      </div>
                    );
                  })}
                </div>

                <div className="relative">
                  <div className="flex h-full flex-col flex-wrap overflow-hidden">
                    {showedImages.map((item) => {
                      return (
                        <div
                          key={item.id}
                          className="relative lg:mb-[80px] lg:aspect-[393/451] lg:w-[393px] xl:aspect-[524/603] xl:w-[524px]"
                        >
                          <Image
                            src={item.image}
                            alt="real-estate-advantage"
                            fill
                            className="object-cover"
                          />
                        </div>
                      );
                    })}

                    <div className="relative lg:aspect-[393/225] lg:w-[393px] xl:aspect-[524/150] xl:w-[524px]">
                      <Image
                        src={
                          unShowedImages[parseInt(cardId)]?.image ??
                          "/images/access-to-verified-properties.png"
                        }
                        alt="real-estate-advantage"
                        fill
                        className="rounded-t-[12px] object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="hidden md:block lg:hidden">
              <div className="flex w-full items-center justify-between">
                <div
                  className="max-w-[355px]"
                  onClick={() => setCardId(RealEstateAdvantages[0]?.id ?? "")}
                >
                  <RealEstateAdvantageCard
                    title={RealEstateAdvantages[0]?.title ?? ""}
                    description={RealEstateAdvantages[0]?.description ?? ""}
                    active={RealEstateAdvantages[0]?.id === cardId}
                  />
                </div>

                <Image
                  src="/images/pan-india-exposure.png"
                  alt="pan-india-exposure"
                  width={261}
                  height={300}
                />
              </div>

              <div className="flex w-full items-center justify-between">
                <Image
                  src="/images/direct-connectedness.png"
                  alt="direct-connectedness"
                  width={261}
                  height={300}
                />
                <div
                  className="max-w-[355px]"
                  onClick={() => setCardId(RealEstateAdvantages[1]?.id ?? "")}
                >
                  <RealEstateAdvantageCard
                    title={RealEstateAdvantages[1]?.title ?? ""}
                    description={RealEstateAdvantages[1]?.description ?? ""}
                    active={RealEstateAdvantages[1]?.id === cardId}
                  />
                </div>
              </div>

              <div className="flex w-full items-center justify-between">
                <div
                  className="max-w-[355px]"
                  onClick={() => setCardId(RealEstateAdvantages[2]?.id ?? "")}
                >
                  <RealEstateAdvantageCard
                    title={RealEstateAdvantages[2]?.title ?? ""}
                    description={RealEstateAdvantages[2]?.description ?? ""}
                    active={RealEstateAdvantages[2]?.id === cardId}
                  />
                </div>

                <Image
                  src="/images/access-to-verified-properties.png"
                  alt="pan-india-exposure"
                  width={261}
                  height={300}
                />
              </div>

              <div className="flex w-full items-center justify-between">
                <Image
                  src="/images/authenticity-credibility.png"
                  alt="authenticity-credibility"
                  width={261}
                  height={300}
                />
                <div
                  className="max-w-[355px]"
                  onClick={() => setCardId(RealEstateAdvantages[3]?.id ?? "")}
                >
                  <RealEstateAdvantageCard
                    title={RealEstateAdvantages[3]?.title ?? ""}
                    description={RealEstateAdvantages[3]?.description ?? ""}
                    active={RealEstateAdvantages[3]?.id === cardId}
                  />
                </div>
              </div>
            </div>

            <RealEstateBottomCard />
          </div>
        </div>
      </div>
    </>
  );
};
export default RealEstateAdvantage;
