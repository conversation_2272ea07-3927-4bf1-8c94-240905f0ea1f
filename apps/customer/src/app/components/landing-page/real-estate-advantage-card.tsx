import Image from "next/image";

const RealEstateAdvantageCard = ({
  title,
  description,
  active,
}: {
  title: string;
  description: string;
  active?: boolean;
}) => {
  return (
    <>
      <div
        className={`flex cursor-pointer flex-col gap-[12px] p-[16px] transition-all duration-300 ease-in-out md:p-[24px] xl:p-[40px] ${
          active
            ? "rounded-[12px] border-l-[3px] border-[#FFFFFF] bg-white/10"
            : "opacity-50 hover:opacity-75"
        }`}
      >
        <div className="flex items-center gap-[12px]">
          <Image
            src={
              active
                ? "/images/profile-circle.svg"
                : "/images/profile-circle-basic.svg"
            }
            alt="profile-picture"
            width={32}
            height={32}
            className="transition-all duration-500 ease-in-out"
          />
          <div className="font-airbnb_w_bd text-xl font-bold leading-[30px] text-text-100 transition-all duration-500 ease-in-out md:text-2xl 2xl:text-3xl">
            {title}
          </div>
        </div>
        <div className="font-airbnb_w_bk text-sm font-normal text-text-50 transition-all duration-500 ease-in-out md:text-base xl:text-lg">
          {description}
        </div>
      </div>
    </>
  );
};
export default RealEstateAdvantageCard;
