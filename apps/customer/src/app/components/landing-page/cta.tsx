import { Button } from "@repo/ui/components/ui/button";
import Image from "next/image";
import React from "react";
import { CTAStoresCards } from "~/app/utils/constants";

const CTA = () => {
  return (
    <div className="container mx-auto max-w-full">
      <div className="flex flex-col gap-[60px] rounded-[36px] bg-[linear-gradient(98deg,_#E9E9E9_1.91%,_#D0D0D0_151.25%)] px-6 lg:flex-row lg:items-end lg:justify-end lg:pl-[50px] xl:pl-[80px]">
        {/* left */}
        <div className="flex-1 pt-9 lg:py-10 xl:py-[60px]">
          <div className="mb-5 flex flex-col items-center justify-center md:mb-6 lg:mb-8 lg:items-start xl:mb-[50px]">
            <h2 className="mb-2 font-airbnb_w_xbd text-2xl font-extrabold text-secondary-2-700 md:mb-[14px] md:mb-[18px] md:text-3xl lg:text-4xl xl:text-5xl">
              Try It! For FREE
            </h2>

            <p className="mb-3 text-center font-airbnb_w_bk text-lg text-secondary-2-900 md:mb-[18px] lg:mb-6 lg:text-start lg:font-airbnb_w_md lg:text-xl xl:text-2xl">
              The hassle-free platform for agents to connect, collaborate and
              conquer.
            </p>

            <Button className="bg-secondary-2-900 px-6 py-3">
              <p className="mx-[60px] sm:mx-[86px]">Sign Up! Now</p>
            </Button>
          </div>

          <div className="flex flex-col items-center justify-center gap-2 lg:items-start xl:gap-4">
            <p className="font-medium text-secondary-2-900 xl:text-xl">
              Download the app
            </p>

            <div className="flex items-center gap-5">
              {CTAStoresCards.map((item) => (
                <StoreCard key={item.id} {...item} />
              ))}
            </div>
          </div>
        </div>

        {/* right */}
        <div className="flex h-fit flex-1 items-center justify-center overflow-hidden">
          <div className="relative aspect-[357/250] w-[357px] md:-bottom-[50px] md:aspect-[535/350] md:w-[535px] lg:-bottom-0 lg:aspect-[432/354] lg:w-[432px] xl:aspect-[588/430] xl:w-[588px] 2xl:aspect-[672/500] 2xl:w-[672px]">
            <Image
              src="/images/cta-phones.svg"
              alt="phones"
              fill
              className="relative object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CTA;

const StoreCard = ({
  icon,
  title,
  storeName,
}: {
  icon: string;
  title: string;
  storeName: string;
}) => {
  return (
    <div className="flex items-center gap-1.5 rounded-lg bg-secondary-2-900 px-3 py-1.5 sm:px-5">
      <Image
        src={icon}
        alt={storeName}
        width={100}
        height={100}
        className="size-6 xl:size-[30px]"
      />

      <div className="flex flex-col items-start text-white">
        <span className="text-nowrap font-airbnb_w_bk text-[8px] sm:text-[10px] xl:text-xs">
          {title}
        </span>
        <span className="text-nowrap text-xs sm:text-lg xl:text-[22px]">
          {storeName}
        </span>
      </div>
    </div>
  );
};
