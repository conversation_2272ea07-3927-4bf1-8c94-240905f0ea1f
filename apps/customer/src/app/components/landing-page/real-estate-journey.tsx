"use client";

import Image from "next/image";
import HeadingBadge from "../shared/heading-badge";
import { RealEstateJourneyCards } from "~/app/utils/constants";

const RealEstateJourney = () => {
  return (
    <>
      <div className="h-full bg-[url('/images/real-estate-bg.png')]">
        <div className="container mx-auto max-w-full py-9 md:py-[40px] lg:py-[55px] xl:py-[65px] 2xl:py-[70px]">
          <div className="flex flex-col items-center">
            {/* Heading badge */}
            <HeadingBadge
              content="Your Trusted Real Estate Guide"
              fileUrl="/icons/grid-icon.svg"
              className="mb-1 md:mb-2 lg:mb-2.5 xl:mb-[14px]"
            />

            {/* section heading */}
            <div className="mb-3 text-center font-airbnb_w_xbd text-3xl font-semibold text-text-30 md:mb-[14px] lg:mb-4 lg:text-4xl xl:mb-[18px] xl:text-[40px] xl:leading-[48px] 2xl:text-[48px] 2xl:leading-[56px]">
              Boost Your Real Estate Journey with Us
            </div>

            {/* sub-heading */}
            <div className="mb-8 text-center font-airbnb_w_md text-lg font-medium text-primary-200 md:mb-9 md:gap-8 lg:mb-[45px] lg:text-[20px] lg:leading-[30px] xl:mb-[50px] 2xl:mb-[60px]">
              Discover our uniqueness
            </div>

            {/* service cards */}
          </div>

          {/* <div className="flex flex-row justify-between rounded-[32px] bg-primary-100 px-4 py-6 lg:px-6 lg:py-10 xl:py-[60px] 2xl:w-1/5 2xl:flex-col 2xl:gap-[200px] 2xl:px-8">
            <div className="flex basis-1/5 flex-col items-center justify-center gap-3">
              <div className="font-airbnb_w_xbd text-[48px] leading-[56px] text-secondary-550">
                15 years
              </div>
              <div className="text-center text-text-800 2xl:text-[20px] 2xl:leading-[30px]">
                Experience in real estate
              </div>
            </div>

            <div className="flex flex-col items-center gap-3">
              <div className="font-airbnb_w_xbd text-[48px] leading-[56px] text-secondary-550">
                100%
              </div>
              <div className="text-center text-text-800 2xl:text-[20px] 2xl:leading-[30px]">
                Covers all the locations in INDIA, from states to cities
              </div>
            </div>

            <div className="flex flex-col items-center gap-3">
              <div className="font-airbnb_w_xbd text-[48px] leading-[56px] text-secondary-550">
                4.9
              </div>
              <div className="text-center text-text-800 2xl:text-[20px] 2xl:leading-[30px]">
                Satisfied consumer using app & website
              </div>
            </div>
          </div> */}

          {RealEstateJourneyCards.map((item) => (
            <div className="flex flex-row items-center gap-6">
              <div
                className={`${item.imageRight === false ? "order-2" : "order-1"} ${item.border == true ? "border-l-[3px] border-l-white" : ""} flex w-[65%] flex-col gap-3 rounded-xl p-4 md:p-6`}
              >
                <div className="flex gap-3">
                  <div className="relative aspect-square w-8">
                    <Image
                      src={"/images/profile-circle.png"}
                      fill={true}
                      alt="profile-circle"
                    />
                  </div>
                  <div className="font-airbnb_w_bd text-xl font-bold text-white md:text-2xl">
                    PAN India Exposure
                  </div>
                </div>
                <div className="font-airbnb_w_bk text-sm font-normal text-text-100 md:text-base">
                  Reach a vast network of brokers across India, allowing you to
                  significantly expand your property listings and reach
                  potential clients in every corner of the country.{" "}
                </div>
              </div>
              <div
                className={`${item.imageRight === false ? "order-1" : "order-2"} relative hidden aspect-[3/4] w-[261px] md:block`}
              >
                <Image
                  src={item.url}
                  fill
                  alt="real-estate-images"
                  className="object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default RealEstateJourney;
