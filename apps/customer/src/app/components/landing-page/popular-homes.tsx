"use client";

import PropertyCard from "@repo/ui/components/shared/property-card";
import HeadingBadge from "../shared/heading-badge";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";

import type {
  TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded,
} from "~/app/types";
import ContactAgentButton from "../shared/contact-agent-button";
import LikePropertyButton from "../shared/like-property-button";
import SharePropertyButton from "../shared/share-property-button";
import PropertyCardBig from "../shared/property-card-big";
import PropertyCardHorizontal from "../shared/property-card-horizontal";
import { api } from "~/trpc/react";

type PopularHomesProps = {
  properties: TPropertyWithUtilitiesAmenitiesUserCommentsCustomerFavouriteMediaIncluded[];
  //   customer: TCustomerProfile;
};

const PopularHomes = ({ properties }: PopularHomesProps) => {
  const { data: customer } = api.user.getProfile.useQuery();
  return (
    <>
      <div className="bg-primary-0">
        <div className="container mx-auto max-w-full py-9 md:py-[40px] lg:py-[55px] xl:py-[65px] 2xl:py-[70px]">
          {/* header of this section */}
          <div className="flex flex-col items-center">
            {/* Heading badge */}
            <HeadingBadge
              content="Must-See Listings"
              fileUrl="/icons/grid-icon.svg"
              className="mb-1 md:mb-2 lg:mb-2.5 xl:mb-[14px]"
            />

            {/* section heading */}
            <div className="mb-3 text-center font-airbnb_w_xbd text-3xl font-semibold text-text-700 md:mb-[14px] lg:mb-4 lg:text-4xl xl:mb-[18px] xl:text-[40px] xl:leading-[48px] 2xl:text-[48px] 2xl:leading-[56px]">
              Discover Today's Most Popular Homes
            </div>

            {/* sub-heading */}
            <div className="mb-8 text-center font-airbnb_w_md text-lg font-medium text-primary-600 md:mb-9 md:gap-8 lg:mb-[45px] lg:text-[20px] lg:leading-[30px] xl:mb-[50px] 2xl:mb-[60px]">
              Limited time offer: Explore trending properties now
            </div>
          </div>

          {/* till 1440 popular home cards */}
          {properties.length < 0 ? (
            <div className="flex min-h-[20vh] items-center justify-center text-xl font-bold text-primary-700">
              No property found
            </div>
          ) : (
            <div className="hidden justify-between xl:flex xl:gap-1">
              {/* first popular home card */}
              <div className="basis-[45%]">
                {properties[0] && <PropertyCardBig property={properties[0]} />}
              </div>
              {/* popular home crads except the first one */}
              <div className="flex basis-[54%] flex-col gap-6 overflow-y-auto">
                {properties.slice(1, 4).map((item) => (
                  <PropertyCardHorizontal property={item} key={item.id} />
                ))}
              </div>
            </div>
          )}

          {/* popular home cards carousel for small screens */}
          <div className="block xl:hidden">
            <Carousel
              opts={{
                align: "start",
              }}
              className="w-full"
            >
              <CarouselContent>
                {properties.map((item, index) => (
                  <CarouselItem
                    key={index}
                    className="basis-[356px] md:basis-[364px]"
                  >
                    <PropertyCard
                      key={item.id}
                      locationIcon="/icons/location.svg"
                      id={item.id}
                      property={item}
                      propertyOwnerId={item.userId}
                      contactOrCheckResponsesButton={
                        <ContactAgentButton
                          agentId={item.userId}
                          connectedAgentId={
                            customer?.connections[0]?.agentId ?? ""
                          }
                        />
                      }
                      likePropertyButton={
                        <LikePropertyButton
                          propertyId={item.id}
                          isPropertyLiked={!!item.customerFavourites.length}
                        />
                      }
                      sharePropertyButton={
                        <SharePropertyButton propertyId={item.id} />
                      }
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>
        </div>
      </div>
    </>
  );
};

export default PopularHomes;
