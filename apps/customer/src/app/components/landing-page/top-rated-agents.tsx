import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import { Button } from "@repo/ui/components/ui/button";
import HeadingBadge from "../shared/heading-badge";
import type { TAgent } from "~/app/types";

import AgentCard from "@repo/ui/components/shared/agent-card";

type TopRatedAgentsProps = {
  topRatedAgents: TAgent[];
};

const TopRatedAgents = ({ topRatedAgents }: TopRatedAgentsProps) => {
  return (
    <>
      <div className="bg-white">
        <div className="container mx-auto max-w-full py-9 md:py-[40px] lg:py-[55px] xl:py-[65px] 2xl:gap-[60px] 2xl:py-[70px]">
          {/* top part of this section */}
          <div className="mb-8 flex flex-col md:mb-9 lg:mb-[45px] lg:flex-row lg:items-end lg:justify-between xl:mb-[50px] 2xl:mb-[60px]">
            {/* badge and heading */}
            <div className="mb-[14px] lg:mb-0">
              <HeadingBadge
                content="Your Trusted Real Estate Companion"
                fileUrl="/icons/grid-icon.svg"
                className="mb-1 md:mb-2 lg:mb-2.5 xl:mb-[14px]"
              />
              {/* section heading */}
              <div className="text-center font-airbnb_w_xbd text-3xl font-semibold text-text-700 xl:text-[40px] xl:leading-[48px] 2xl:text-[48px] 2xl:leading-[56px]">
                Connect with the Best Agents
              </div>
            </div>
            {/* button */}
            <Button variant="default" className="bg-primary-500">
              Search Here, to Find the Perfect Deer
            </Button>
          </div>

          {/* carousel for agent-cards */}
          <Carousel
            opts={{
              align: "start",
            }}
            className="w-full"
          >
            <CarouselContent>
              {topRatedAgents.map((item) => (
                <CarouselItem
                  key={item.id}
                  className="basis-[356px] md:basis-[364px]"
                >
                  <AgentCard
                    verifiedAgentIcon="/icons/verified-agent.svg"
                    starIcon="/icons/star.svg"
                    clockIcon="/icons/clock.svg"
                    experienceIcon="/icons/experience.svg"
                    propertiesSoldIcon="/icons/home.svg"
                    link={`/agent-detail/${item.id}`}
                    {...item}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </>
  );
};
export default TopRatedAgents;
