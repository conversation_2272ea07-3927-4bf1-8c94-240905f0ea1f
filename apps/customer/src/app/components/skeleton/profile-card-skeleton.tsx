import React from "react";

const ProfileCardSkeleton = () => {
  return (
    <div className="flex flex-col gap-3 rounded-xl bg-[linear-gradient(90deg,_#F04D24_0%,_#C33D12_50%,_#962C00_100%)] p-3 xl:py-4">
      <div className="flex items-start gap-2">
        {/* Avatar skeleton */}
        <div className="relative aspect-square size-10 xl:size-[62px] 2xl:size-[100px]">
          <div className="h-full w-full animate-pulse rounded-[6px] bg-muted" />
        </div>

        {/* Name and email skeleton */}
        <div className="flex flex-1 flex-col gap-0.5">
          <div className="h-6 w-28 animate-pulse rounded bg-muted xl:h-7" />
          <div className="h-5 w-44 animate-pulse rounded bg-muted" />
        </div>
      </div>

      {/* Edit button skeleton */}
      <div className="flex items-center justify-end">
        <div className="flex items-center gap-6">
          <div className="size-5 animate-pulse rounded bg-muted xl:size-6" />
        </div>
      </div>
    </div>
  );
};

export default ProfileCardSkeleton;
