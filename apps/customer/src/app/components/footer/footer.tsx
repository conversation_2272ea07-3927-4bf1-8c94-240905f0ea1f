"use client";

import Link from "next/link";
import Image from "next/image";
import React from "react";
import { Mail, Phone } from "lucide-react";
import { useSession } from "next-auth/react";
import { SocialMediaLinks } from "~/app/utils/constants";

const Footer = () => {
  const { data } = useSession();

  return (
    <div className="bg-[url('/images/footer-bg.png')] bg-cover bg-no-repeat py-9 md:py-10 lg:py-[45px]">
      <div className="container mx-auto max-w-full">
        <div className="flex flex-col gap-6 xl:gap-8">
          {/* top div with logo and links */}
          <div className="flex flex-col gap-6 xl:flex-row xl:justify-between">
            {/*logo div */}
            <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between xl:flex-col xl:items-start xl:justify-normal xl:gap-8">
              <Link
                href="/"
                className="relative aspect-[5/3] w-[108px] md:w-[124px] xl:w-[148px]"
              >
                <Image
                  src="/footer-logo.svg"
                  fill
                  alt="logo-main"
                  className="relative object-cover"
                />
              </Link>
              <div className="flex flex-col gap-3 md:gap-[18px]">
                <h2 className="text-justify font-medium text-primary-2-900 md:text-lg lg:text-xl xl:text-2xl">
                  Limited offer! Don't Miss Out
                </h2>
                <div className="flex items-center gap-4">
                  {SocialMediaLinks.map((item, idx) => {
                    return (
                      <Link
                        key={item.id}
                        href={item.link}
                        target="_blank"
                        className="rounded-sm bg-secondary-2-900 p-[7px] md:p-2 xl:p-3"
                      >
                        <Image
                          src={item.src}
                          alt={item.alt}
                          width={50}
                          height={50}
                          className="size-[18px] md:size-5 xl:size-8"
                        ></Image>
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
            {/* links div */}
            <div className="flex flex-wrap gap-x-10 gap-y-[18px] md:justify-between lg:justify-normal lg:gap-20">
              {/* quick links */}
              <div className="flex flex-col gap-4">
                <h2 className="font-airbnb_w_bd text-lg font-bold text-secondary-2-900 2xl:text-xl">
                  Quick Links
                </h2>
                <ul className="flex flex-col gap-2.5 font-airbnb_w_bk text-secondary-2-900 2xl:text-lg">
                  <li>
                    <Link href="/">Home</Link>
                  </li>
                  {/* <li>About Us</li> */}
                  {/* <li>Contact Us</li> */}
                  {data?.user ? (
                    <></>
                  ) : (
                    <li>
                      <Link href="https://deerconnect.com/sign-in">
                        Become an Agent
                      </Link>
                    </li>
                  )}
                  {/* <li>Careers</li> */}
                </ul>
              </div>
              {/* resources */}
              <div className="flex flex-col gap-4">
                <h2 className="font-airbnb_w_bd text-lg font-bold text-secondary-2-900 2xl:text-xl">
                  Resources
                </h2>
                <ul className="flex flex-col gap-2.5 font-airbnb_w_bk text-secondary-2-900 2xl:text-lg">
                  <li>
                    <Link href="/property-listing?propertyFor=RENT">Rent</Link>
                  </li>
                  {/* <li>Lease</li> */}
                  <li>
                    <Link href="/property-listing?propertyFor=SALE">Buy</Link>
                  </li>
                  {/* {data?.user ? (
                    <li>
                      <Link href="?step=1&postPropertyFormOpen=true">
                        Post Property
                      </Link>
                    </li>
                  ) : (
                    <></>
                  )} */}
                  <li>
                    <Link className="" href="/privacy-policy">
                      Privacy Policy
                    </Link>
                  </li>
                  <li>
                    <Link className="" href="/disclaimer">
                      Disclaimer
                    </Link>
                  </li>
                  <li>
                    <Link className="" href="/terms-and-conditions">
                      Terms And Conditions
                    </Link>
                  </li>
                  <li>
                    <Link className="" href="/safety-guide">
                      Safety Guide
                    </Link>
                  </li>
                  <li>
                    <Link className="" href="/user-agreement">
                      User Agreement
                    </Link>
                  </li>
                </ul>
              </div>
              {/* contact us */}
              <div className="flex flex-col gap-4">
                <h2 className="font-airbnb_w_bd text-lg font-bold text-secondary-2-900 2xl:text-xl">
                  Contact Us
                </h2>
                <ul className="flex flex-col gap-2.5 font-airbnb_w_bk text-secondary-2-900 2xl:text-lg">
                  <li className="flex items-center gap-1">
                    <Mail className="size-[21.6px]" strokeWidth={1.2}></Mail>
                    <Link href="mailto:<EMAIL>">
                      <EMAIL>
                    </Link>
                  </li>
                  <li>
                    <Link
                      className="flex items-center gap-1"
                      href="tel:7043807438"
                    >
                      <Phone
                        className="size-[21.6px]"
                        strokeWidth={1.2}
                      ></Phone>{" "}
                      +91 7043807438
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          {/* bottom div */}
          <div className="flex flex-col gap-3 font-airbnb_w_bd font-bold text-secondary-2-900 md:flex-row md:justify-between">
            <p className="">
              Copyright © {new Date().getFullYear()} All Rights Reserved.
            </p>
            <Link href="https://nextflytech.com/" target="_blank">
              Developed By Nextfly Technologies
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
