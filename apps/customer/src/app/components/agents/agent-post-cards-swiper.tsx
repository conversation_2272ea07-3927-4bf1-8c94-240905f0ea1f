"use client";
import { api } from "~/trpc/react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import PostCard from "@repo/ui/components/shared/post-card";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter, useSearchParams } from "next/navigation";
import type { Prisma } from "@repo/database";
import { useSession } from "next-auth/react";
import ReportPostDialog from "../shared/report-post-dialog";

export type IPost = Prisma.PostGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        filePublicUrl: true;
        cloudinaryProfileImageUrl: true;
        name: true;
        company: {
          select: {
            companyName: true;
          };
        };
      };
    };
    media: {
      select: {
        filePublicUrl: true;
        cloudinaryUrl: true;
        cloudinaryId: true;
        mediaType: true;
      };
    };
    comments: {
      select: {
        comment: true;
        isPinned: true;
        createdAt: true;
        user: {
          select: {
            id: true;
            name: true;
            filePublicUrl: true;
            companyDetails: {
              select: {
                companyName: true;
              };
            };
          };
        };
        customer: {
          select: {
            id: true;
            name: true;
            profileImagePublicUrl: true;
          };
        };
        customerId: true;
        userId: true;
      };
    };
    likes: {
      select: {
        id: true;
        postId: true;
      };
    };
  };
}>;

const AgentPostCardsSwiper = ({ posts }: { posts: IPost[] }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { mutate: likePost } = api.social.likePost.useMutation();
  //   const { mutate: unlikePost } = api.social.unlikePost.useMutation();
  const { mutate: newComment } = api.social.newComment.useMutation();
  const trcpUtils = api.useUtils();
  const session = useSession();

  const handlePostClick = (postId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("postId", postId);
    router.replace(`?${params}`, { scroll: false });
  };

  return (
    <>
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {posts.length > 0 ? (
            posts.map((item, index) => {
              const handleLike = () => {
                likePost(
                  { postId: item.id },
                  {
                    onSuccess: () => {
                      void trcpUtils.social.invalidate();
                      void trcpUtils.agent.getAgentDetails.invalidate();
                    },
                    onError: (opts) => {
                      toast.error(opts.message);
                    },
                  },
                );
              };
              const handleComment = (postId: string, comment: string) => {
                newComment(
                  { comment, postId },
                  {
                    onSuccess: () => {
                      void trcpUtils.social.invalidate();
                      void trcpUtils.agent.getAgentDetails.invalidate();
                    },
                    onError: (opts) => {
                      toast.error(opts.message);
                    },
                  },
                );
              };

              return (
                <CarouselItem
                  key={index}
                  className="basis-[370px] md:basis-[390px] 2xl:basis-[500px]"
                >
                  <PostCard
                    isLiked={!!item.likes.length}
                    hideReportButton={session.data?.user?.id === item.user.id}
                    hideDeleteButton={session.data?.user?.id === item.user.id}
                    onLike={handleLike}
                    onComment={handleComment}
                    onPostClick={handlePostClick}
                    formatTime={(date) => date.toLocaleDateString()}
                    formatCount={(count) => count.toString()}
                    reportPostDialogForm={
                      <ReportPostDialog postId={item.id}></ReportPostDialog>
                    }
                    {...item}
                  />
                </CarouselItem>
              );
            })
          ) : (
            <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-lg text-primary-2-800 lg:text-xl">
              Your posts will appear here.
            </div>
          )}
        </CarouselContent>
      </Carousel>
    </>
  );
};

export default AgentPostCardsSwiper;
