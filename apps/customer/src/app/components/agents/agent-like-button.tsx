"use client";
import React, { useState, useEffect } from "react";
import { Heart} from "lucide-react";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { cn } from "@repo/ui/lib/utils";
import { Button } from "@repo/ui/components/ui/button";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

const LikeAgentButton = ({
  agentId,
  likeBtnClassname,
}: {
  agentId: string;
  likeBtnClassname?: string;
}) => {
  const [isFilled, setIsFilled] = useState<boolean>(false);
  const trpcUtils = api.useUtils();
  const router=useRouter()
  const {data:session}=useSession();

  const { data: isLiked, isLoading } = api.likeAgent.isAgentLiked.useQuery(
    { agentId },
    {
      refetchOnWindowFocus: false,
    },
  );

  const likeAgentMutation = api.likeAgent.addLikedAgents.useMutation();

  useEffect(() => {
    if (typeof isLiked === "boolean") {
      setIsFilled(isLiked);
    }
  }, [isLiked]);

  const handleLikeAgents =async(e: { stopPropagation: () => void; }) => {
    if(!session)
    {
       router.push("/sign-in")
       toast.error("You must login to like an agent!")
    }
    e.stopPropagation();
    setIsFilled(!isFilled);

      await likeAgentMutation.mutateAsync({ agentId },{
        onSuccess:()=>{
            toast.success(
                isFilled ? "Removed from liked agents" : "Added to liked agents",
              );
              void trpcUtils.invalidate().catch(()=>console.log("refetched"))
        },
        onError:(error)=>{
            console.log("error is",error.message)
            setIsFilled(isFilled);
            toast.error("Failed to update like status");
        }
      });
    }
  return (
    <Button
      onClick={handleLikeAgents}
      className="rounded-lg border-[1.5px] border-primary-2-750 bg-text-20 font-airbnb_w_md font-medium text-primary-2-750 hover:bg-transparent md:rounded-xl"
    >
      <Heart
        className={cn(
          `mr-3 size-[18px] transition-all duration-300 ease-in-out lg:size-6 ${isFilled ? "scale-110" : "scale-100"} ${isLoading ? "opacity-50" : "opacity-100"} `,
          likeBtnClassname,
        )}
        fill={isFilled ? "currentColor" : "none"}
        color="#784100"
      />
      {isFilled ? "Added to Favourites" : "Add to Favourites"}
    </Button>
  );
};

export default LikeAgentButton
