"use client";
import { Prisma } from "@repo/database";
import PropertyCard from "@repo/ui/components/shared/property-card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import { useSession } from "next-auth/react";
import LikePropertyButton from "../shared/like-property-button";
import SharePropertyButton from "../shared/share-property-button";
import ContactAgentButton from "../shared/contact-agent-button";

export type IProperty = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      select: {
        id: true;
        company: true;
        companyDetails: true;
        name: true;
        rating: true;
        reviews: true;
      };
    };
    customerFavourites: true;
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
          };
        };
      };
    };
  };
}>;
const AgentPropertyCardsSwiper = ({
  properties,
  connectedAgentId,
}: {
  properties: IProperty[];
  connectedAgentId: string;
}) => {
  const session = useSession();
  return (
    <>
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {properties.length > 0 ? (
            properties.map((item, index) => (
              <CarouselItem key={index} className="basis-[340px]">
                <PropertyCard
                  locationIcon="/icons/location.svg"
                  id={item.id}
                  propertyOwnerId={item.user.id}
                  key={item.id}
                  property={item}
                  userId={session.data?.user?.id}
                  contactOrCheckResponsesButton={
                    <ContactAgentButton
                      agentId={item.userId}
                      connectedAgentId={connectedAgentId}
                    />
                  }
                  likePropertyButton={
                    <LikePropertyButton
                      propertyId={item.id}
                      isPropertyLiked={!!item.customerFavourites.length}
                    />
                  }
                  sharePropertyButton={
                    <SharePropertyButton propertyId={item.id} />
                  }
                />
              </CarouselItem>
            ))
          ) : (
            <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-lg text-primary-2-800 lg:text-xl">
              Your active property listings will appear here.
            </div>
          )}
        </CarouselContent>
      </Carousel>
    </>
  );
};
export default AgentPropertyCardsSwiper;
