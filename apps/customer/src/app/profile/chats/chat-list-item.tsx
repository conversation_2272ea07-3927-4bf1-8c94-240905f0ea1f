import { Badge } from "@repo/ui/components/ui/badge";
import { format } from "date-fns";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import type { TConversationWithAgentCustomerMessages } from "~/app/types";
import { CHAT_PARAM_NAME } from "~/app/utils/constants";
import { api } from "~/trpc/react";

const ChatListItem = ({
  id,
  agent,
  customerUnseenMessagesCount,
  messages,
}: TConversationWithAgentCustomerMessages) => {
  const searchParams = useSearchParams();
  const currentConversationId = searchParams.get(CHAT_PARAM_NAME);

  const { mutate: toggleStatus } = api.chat.toggleStatus.useMutation();
  const { mutate: setUnseenMessagesToZero } =
    api.chat.setUnseenMessagesToZero.useMutation();

  const updateSearchParams = (newId: string) => {
    history.pushState(null, "", `?${CHAT_PARAM_NAME}=${newId}`);
    setUnseenMessagesToZero({ conversationId: newId });
  };

  const handleConverseClick = () => {
    toggleStatus({ status: true });

    if (id !== currentConversationId) {
      updateSearchParams(id);
    }
  };

  return (
    <div
      onClick={handleConverseClick}
      className={`flex w-full cursor-pointer justify-between gap-4 rounded-lg px-3 pb-4 pt-3 ${
        id === currentConversationId ? "bg-[#fff6f4]" : "bg-text-30"
      }`}
    >
      {/* left div */}
      <div className="flex w-full items-start gap-3">
        {/* image */}
        <div className="relative aspect-square w-9 xl:w-[46px]">
          <Image
            src={
              agent.cloudinaryProfileImageUrl ??
              agent.filePublicUrl ??
              "/images/placeholder-user-image.jpg"
            }
            className="relative rounded-full object-cover"
            alt={`${agent.name}'s profile`}
            fill
          />
        </div>
        {/* content */}
        <div className="flex w-full flex-col gap-2">
          {/* top div with user name, property name and badge */}
          <div className="flex items-start justify-between gap-2">
            {/* name */}
            <div className="space-y-0.5">
              <h2 className="font-airbnb_w_bd font-bold text-primary-2-800">
                {agent.name}
              </h2>
              {/* {property?.propertyTitle && (
                <p className="line-clamp-2 font-airbnb_w_bk text-xs text-[#1e1e1e]">
                  Property Name: {property.propertyTitle}
                </p>
              )} */}
            </div>
            {/* badge */}
            <Badge className="rounded-sm bg-secondary-2-100 px-2 py-1 font-airbnb_w_md text-xs font-medium">
              Agent
            </Badge>
          </div>
          {/* bottom div with message */}
          <p className="line-clamp-1 font-airbnb_w_bk text-xs text-primary-2-800 lg:text-sm">
            {messages.at(-1)?.content}
          </p>
        </div>
      </div>
      {/* right div */}
      <div className="flex flex-col items-end gap-2 text-nowrap">
        <div className="font-airbnb_w_md text-[10px] font-medium leading-3 text-primary-2-800 lg:text-xs">
          {messages.at(-1)?.createdAt &&
            format(new Date(messages.at(-1)?.createdAt ?? ""), "hh:mm aa")}
        </div>
        {customerUnseenMessagesCount > 0 && (
          <div className="size-5 rounded-full bg-secondary-500 p-[3px] text-center text-xs font-medium text-white">
            {customerUnseenMessagesCount}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatListItem;
