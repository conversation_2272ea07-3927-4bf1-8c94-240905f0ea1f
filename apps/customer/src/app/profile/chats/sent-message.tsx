import type { CustomerAgentConnectionMessages } from "@repo/database";
import { format } from "date-fns";
import { CheckCheckIcon } from "lucide-react";
import React from "react";

const SentMessage = ({
  message,
}: {
  message: CustomerAgentConnectionMessages;
}) => {
  return (
    <div className="max-w-[270px] self-end rounded-bl-[10px] rounded-tl-[10px] rounded-tr-[10px] bg-[#f2f2f2] px-3 py-2.5 font-airbnb_w_bk text-sm text-text-600 sm:max-w-[349px] xl:text-base">
      <p>{message.content}</p>
      <p className="flex items-center justify-end gap-0.5 text-primary-2-700">
        {format(message.createdAt, "h:mm a")}{" "}
        <CheckCheckIcon className="size-3 text-primary-2-700"></CheckCheckIcon>
      </p>
    </div>
  );
};

export default SentMessage;
