import React, { memo } from "react";
import ChatScreenHeader from "./chat-screen-header";
import ChatScreenBody from "./chat-screen-body";
import type { TConversationWithAgentCustomerMessages } from "~/app/types";

const ChatScreen = ({
  conversation,
}: {
  conversation: TConversationWithAgentCustomerMessages;
}) => {
  return (
    <div className="w-full">
      <ChatScreenHeader conversation={conversation}></ChatScreenHeader>
      <ChatScreenBody />
    </div>
  );
};

export default memo(ChatScreen);
