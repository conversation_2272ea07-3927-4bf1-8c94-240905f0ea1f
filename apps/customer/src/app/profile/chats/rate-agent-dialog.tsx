"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Upload, X } from "lucide-react";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";
import { Rating } from "@smastrom/react-rating";
import "@smastrom/react-rating/style.css";
import { toast } from "@repo/ui/components/ui/sonner";
import { CustomerRateAgentSchema } from "@repo/validators";
import { Textarea } from "@repo/ui/components/ui/textarea";
import { api } from "~/trpc/react";
import { useRouter } from "next/navigation";
import { CldUploadWidget, CldVideoPlayer } from "next-cloudinary";
import type { CloudinaryUploadWidgetResults } from "next-cloudinary";

import { env } from "~/env";
import { useSession } from "next-auth/react";

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB

type RateAgentDialogProps = {
  propertyId?: string;
  ratedToUserId: string;
  connectionId: string;
};

const updatedSchema = CustomerRateAgentSchema.omit({
  fileKey: true,
  filePublicUrl: true,
}).extend({
  cloudinaryPublicId: z.string().optional(),
  cloudinaryUrl: z.string().optional(),
});

const RateAgentDialog = ({
  propertyId,
  ratedToUserId,
  connectionId,
}: RateAgentDialogProps) => {
  const { data: session } = useSession();
  const router = useRouter();

  const form = useForm<z.infer<typeof updatedSchema>>({
    resolver: zodResolver(updatedSchema),
    defaultValues: {
      connectionId: connectionId,
      ratedToUserId: ratedToUserId,
      propertyId: propertyId ?? undefined,
      userRatingMessage: "",
    },
  });

  const { mutate: rateAgent } =
    api.customerReviews.customerRateToAgentNew.useMutation();

  const onSubmit = (values: z.infer<typeof updatedSchema>) => {
    rateAgent(values, {
      onSuccess: (opts) => {
        router.refresh();
        toast.success(opts.message);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  const handleUserRatingVideoUpload = (
    result: CloudinaryUploadWidgetResults,
  ) => {
    const { info } = result as {
      info: { public_id: string; secure_url: string };
      event: string;
    };

    const cloudinaryPublicId = info.public_id;
    const cloudinaryUrl = info.secure_url;

    if (!cloudinaryPublicId || !cloudinaryUrl) {
      toast.error("Upload failed");
      return;
    }

    form.setValue("cloudinaryPublicId", cloudinaryPublicId);
    form.setValue("cloudinaryUrl", cloudinaryUrl);
  };

  const videoUrl = form.watch("cloudinaryUrl");

  return (
    <Dialog modal={false}>
      <DialogTrigger asChild>
        <Button size="sm">Rate Agent</Button>
      </DialogTrigger>
      <DialogContent
        className="max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="userStarsCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Agent Stars</FormLabel>
                  <FormControl>
                    <Rating
                      style={{ maxWidth: 180 }}
                      value={field.value}
                      onChange={field.onChange}
                      transition="zoom"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="userRatingMessage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Agent Review Message</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Rating message for the agent."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Video Upload Section */}
            <FormItem>
              <FormLabel>Upload Video Review (Optional)</FormLabel>
              <div className="mt-2" onClick={(e) => e.stopPropagation()}>
                {videoUrl ? (
                  <div className="relative">
                    <CldVideoPlayer
                      logo={{
                        imageUrl: "/logos/new-logo-footer.svg",
                        onClickUrl: "/",
                      }}
                      src={videoUrl}
                      className="h-48 w-full rounded-lg object-cover"
                      controls
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <CldUploadWidget
                      signatureEndpoint="/api/cloudinary"
                      uploadPreset={env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET}
                      onSuccess={handleUserRatingVideoUpload}
                      options={{
                        croppingShowDimensions: true,
                        croppingValidateDimensions: true,
                        showSkipCropButton: false,
                        maxFiles: 1,
                        showPoweredBy: false,
                        resourceType: "auto",
                        maxFileSize: MAX_FILE_SIZE,
                        clientAllowedFormats: ["mp4", "mov", "avi", "mkv"],
                        folder: `${env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME}/customers/${session?.user?.id}/ratings`,
                        styles: {
                          zIndex: 9999,
                          position: "fixed",
                          modal: { zIndex: 9999 },
                        },
                        showAdvancedOptions: false,
                        showUploadMoreButton: false,
                        singleUploadAutoClose: false,
                      }}
                    >
                      {({ open }) => (
                        <div>
                          <button
                            type="button"
                            onClick={() => open()}
                            className="flex w-full cursor-pointer flex-col items-center gap-2 rounded-lg border-2 border-dashed border-gray-300 p-6 hover:border-gray-400"
                          >
                            <Upload className="h-8 w-8 text-gray-500" />
                            <span className="text-sm text-gray-500">
                              Click to upload video (Max 100MB)
                            </span>
                          </button>
                        </div>
                      )}
                    </CldUploadWidget>
                  </div>
                )}
              </div>
            </FormItem>

            {propertyId && (
              <>
                <FormField
                  control={form.control}
                  name="propertyStarsCount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Stars</FormLabel>
                      <FormControl>
                        <Rating
                          style={{ maxWidth: 180 }}
                          value={field.value ?? 0}
                          onChange={field.onChange}
                          transition="zoom"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="propertyRatingMessage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Review Message</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Rating message for the property."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}
            <Button type="submit">Submit</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default RateAgentDialog;
