import { Badge } from "@repo/ui/components/ui/badge";
import React, { useCallback, useEffect, useRef, useState } from "react";
import ReceivedMessage from "./received-message";
import SentMessage from "./sent-message";
import { Input } from "@repo/ui/components/ui/input";
import { SendIcon } from "lucide-react";
import type { Message } from "@repo/database";
import { useSession } from "next-auth/react";
import { api } from "~/trpc/react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "@repo/ui/components/ui/sonner";
import { formatDate } from "date-fns";
import { useInView } from "react-intersection-observer";
import { skipToken } from "@tanstack/react-query";
import { CHAT_PARAM_NAME } from "~/app/utils/constants";

const SCROLL_THRESHOLD = 100;
const MESSAGE_LIMIT = 100;
const POLLING_INTERVAL = 10000; // 10 seconds

const getGroupTitle = (date: Date): string => {
  const now = new Date();
  const diffInDays = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),
  );

  if (diffInDays === 0) return "Today";
  if (diffInDays === 1) return "Yesterday";
  return formatDate(date, "dd, eeee");
};

const ChatScreenBody = () => {
  const router = useRouter();
  const { ref: loadMoreRef, inView } = useInView();
  const trpcUtils = api.useUtils();
  const searchParams = useSearchParams();
  const [newMessage, setNewMessage] = useState<string>("");
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const messageEndDivRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const connectionId = searchParams.get(CHAT_PARAM_NAME);

  // Reset isInitialLoad when connectionId changes
  useEffect(() => {
    setIsInitialLoad(true);
  }, [connectionId]);

  const {
    data: messagesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = api.chat.getMessages.useInfiniteQuery(
    connectionId
      ? {
          connectionId: connectionId,
          limit: MESSAGE_LIMIT,
        }
      : skipToken,
    {
      getNextPageParam: (lastPage) => lastPage.nextCursor,
      enabled: !!connectionId,
      refetchInterval: POLLING_INTERVAL,
    },
  );

  const { data: sessionData, status } = useSession();

  // Load more messages when scrolling to top
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      void fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const { mutate: sendMessage } = api.chat.sendMessage.useMutation({
    onSuccess: () => {
      setNewMessage("");
      router.refresh();
      void trpcUtils.chat.getMessages.invalidate();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const isNearBottom = useCallback(() => {
    if (!chatContainerRef.current) return false;
    const container = chatContainerRef.current;
    return (
      container.scrollHeight - container.scrollTop - container.clientHeight <
      SCROLL_THRESHOLD
    );
  }, []);

  const scrollToBottom = useCallback((smooth = true) => {
    if (!messageEndDivRef.current) return;
    messageEndDivRef.current.scrollIntoView({
      behavior: smooth ? "smooth" : "auto",
      block: "end",
    });
  }, []);

  // Scroll to bottom on initial load
  useEffect(() => {
    if (messagesData && isInitialLoad) {
      scrollToBottom(false);
      setIsInitialLoad(false);
    }
  }, [messagesData, isInitialLoad, scrollToBottom]);

  // Scroll handling for new messages
  useEffect(() => {
    if (!messageEndDivRef.current || !chatContainerRef.current) return;

    const messages = messagesData?.pages.flatMap((page) => page.messages) ?? [];
    const shouldScroll = messages.length
      ? isNearBottom() ||
        messages[messages.length - 1]?.senderId === sessionData?.user?.id
      : false;

    if (shouldScroll) {
      scrollToBottom();
    }
  }, [
    messagesData,
    sessionData?.user?.id,
    isInitialLoad,
    isNearBottom,
    scrollToBottom,
  ]);

  // Group messages by date
  const groupedMessages = React.useMemo(() => {
    const messages = messagesData?.pages.flatMap((page) => page.messages) ?? [];
    return messages.reduce<Record<string, Message[]>>((groups, message) => {
      const groupTitle = getGroupTitle(new Date(message.createdAt));
      if (!groups[groupTitle]) {
        groups[groupTitle] = [];
      }
      groups[groupTitle].push(message);
      return groups;
    }, {});
  }, [messagesData]);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !sessionData?.user?.id || !connectionId) return;

    sendMessage({
      message: newMessage.trim(),
      connectionId: connectionId,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (status === "loading") return null;

  return (
    <>
      <div
        ref={chatContainerRef}
        className="relative flex max-h-[calc(100vh-32vh)] min-h-[calc(100vh-32vh)] flex-col items-center gap-5 overflow-y-auto py-4 lg:px-5"
      >
        {/* Load more trigger */}
        <div ref={loadMoreRef} className="h-4 w-full" />

        {isFetchingNextPage && (
          <Badge className="bg-secondary-2-100 text-secondary-2-700">
            Loading more messages...
          </Badge>
        )}

        <div className="flex w-full flex-col gap-2">
          {Object.entries(groupedMessages).map(
            ([groupTitle, groupMessages]) => (
              <div key={groupTitle} className="flex w-full flex-col gap-2">
                <div className="sticky top-0 z-10 flex w-full items-center justify-center">
                  <Badge className="z-10 w-fit bg-secondary-2-100 font-airbnb_w_md text-sm font-medium text-secondary-2-700">
                    {groupTitle}
                  </Badge>
                </div>

                {groupMessages.map((message, idx) =>
                  message.senderId === sessionData?.user?.id ? (
                    <SentMessage key={idx} message={message} />
                  ) : (
                    <ReceivedMessage key={idx} message={message} />
                  ),
                )}
              </div>
            ),
          )}

          <div ref={messageEndDivRef} />
        </div>
      </div>

      <div className="relative w-full">
        <Input
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          className="border-secondary-2-100 pr-10 text-primary-2-800 placeholder:text-primary-2-800"
          placeholder="Type your message..."
        />
        <SendIcon
          className="absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer"
          strokeWidth={1}
          onClick={handleSendMessage}
        />
      </div>
    </>
  );
};

export default ChatScreenBody;
