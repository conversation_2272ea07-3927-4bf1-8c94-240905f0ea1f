import { useRouter } from "next/navigation";
import { ChevronLeft } from "lucide-react";
import { Separator } from "@repo/ui/components/ui/separator";
import React, { memo } from "react";
import type { TConversationWithAgentCustomerMessages } from "~/app/types";
import ChatListItem from "./chat-list-item";

const ChatListScreen = ({
  conversation,
}: {
  conversation: TConversationWithAgentCustomerMessages;
}) => {
  const router = useRouter();

  return (
    <div className="w-full">
      <p
        className="flex cursor-pointer items-center gap-4 p-2 text-lg font-medium text-text-550 md:py-3 md:text-xl lg:h-[76px] xl:px-4 xl:text-2xl"
        onClick={() => router.back()}
      >
        <ChevronLeft className="size-[18px] text-primary-2-800 lg:size-5 2xl:size-6" />
        Chat
      </p>
      <Separator></Separator>

      <div className="flex max-h-[calc(100vh-25vh)] flex-col gap-2 overflow-hidden overflow-y-auto lg:px-2 xl:px-4">
        <ChatListItem {...conversation} />
        {/* <>chat list item</> */}
        <Separator className="h-0.5 bg-primary-200" />
      </div>
    </div>
  );
};

export default memo(ChatListScreen);
