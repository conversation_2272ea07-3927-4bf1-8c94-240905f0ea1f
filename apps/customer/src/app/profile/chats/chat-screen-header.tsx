import React, { useState } from "react";
import { ChevronLeft, EllipsisVerticalIcon } from "lucide-react";
import Image from "next/image";
import { Separator } from "@repo/ui/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";
import "@smastrom/react-rating/style.css";
import type { TConversationWithAgentCustomerMessages } from "~/app/types";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { CHAT_PARAM_NAME } from "~/app/utils/constants";
import { api } from "~/trpc/react";
import RateAgentDialog from "./rate-agent-dialog";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { toast } from "@repo/ui/components/ui/sonner";
import { Textarea } from "@repo/ui/components/ui/textarea";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";

const ChatScreenHeader = ({
  conversation,
}: {
  conversation: TConversationWithAgentCustomerMessages;
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [reportReason, setReportReason] = useState<string | undefined>(
    undefined,
  );
  const [reportDialogOpen, setReportDialogOpen] = useState<boolean>(false);
  const { mutate: toggleStatus } = api.chat.toggleStatus.useMutation();
  const { mutate: deleteChat, isPending: deleteChatIsPending } =
    api.chat.deleteChat.useMutation();
  const {
    mutate: reportAgentFromChat,
    isPending: reportAgentFromChatIsPending,
  } = api.chat.reportAgentFromChat.useMutation();
  const trpcUtils = api.useUtils();
  const pathName = usePathname();
  console.log("convo data---------", conversation);
  const agentId = conversation.agentId;

  const handleBackButton = () => {
    toggleStatus({ status: false });

    const params = new URLSearchParams();
    params.delete(CHAT_PARAM_NAME);
    router.replace(`?${params.toString()}`);
  };

  const handleRemoveAgent = () => {
    deleteChat(
      { connectionId: conversation.id },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          router.replace(pathName);
          void trpcUtils.invalidate();
        },
        onError: (opts) => {
          toast.error(opts.message);
          void trpcUtils.invalidate();
        },
      },
    );
  };
  const handleReportAgent = () => {
    if (!reportReason || reportReason.length < 5) {
      toast.warning("Please write atleast 5 characters.");
      return;
    }
    reportAgentFromChat(
      {
        reasonForReporting: reportReason,
        reportedUserId: agentId,
      },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          setReportReason("");
          setReportDialogOpen(false);
          void trpcUtils.invalidate();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <>
      <div className="flex items-center justify-between lg:px-2">
        <div className="flex items-center gap-1 py-3 sm:gap-3">
          <ChevronLeft
            className="cursor-pointer text-primary-2-800"
            onClick={handleBackButton}
          ></ChevronLeft>
          {/* image */}
          <div className="relative aspect-square w-[52px]">
            <Image
              src={
                conversation.agent.cloudinaryProfileImageUrl ??
                conversation.agent.filePublicUrl ??
                "/images/placeholder-user-image.jpg"
              }
              className="relative rounded-full object-cover"
              alt="user-image"
              fill
            />
            {/* online indicator */}
            {conversation.agent.isOnline ? (
              <div className="absolute bottom-0 right-0 size-[15px] rounded-full border-2 border-white bg-green-500"></div>
            ) : (
              <div className="absolute bottom-0 right-0 size-[15px] rounded-full border-2 border-white bg-red-500"></div>
            )}
          </div>

          {/* content */}
          <div className="flex items-start sm:gap-4">
            {/* name */}
            <div className="space-y-1">
              <h2
                onClick={() => {
                  const params = new URLSearchParams(searchParams);
                  params.set("viewAgentId", conversation.agentId);
                  router.push(`?${params}`);
                }}
                className="line-clamp-1 cursor-pointer font-airbnb_w_bd text-lg font-bold text-primary-2-800 xl:text-lg"
              >
                {conversation.agent.name}
              </h2>
            </div>
            {/* badge */}
            {/* <Badge className="rounded-sm px-2 py-1 font-airbnb_w_md text-xs font-medium">
              Agent
            </Badge> */}
          </div>
        </div>

        <div className="flex items-center gap-4">
          {conversation.rating.length > 0 ? (
            <span className="flex items-center gap-2">
              <Image
                src="/icons/star.svg"
                alt="star"
                height={100}
                width={100}
                className="size-5"
              />
              {conversation.rating[0]?.userStarsCount}
            </span>
          ) : (
            <RateAgentDialog
              propertyId={conversation.propertyId ?? undefined}
              ratedToUserId={conversation.agentId}
              connectionId={conversation.id}
            />
          )}

          {/* dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="rounded-[6px] bg-secondary-2-100 p-1 hover:cursor-pointer">
                <EllipsisVerticalIcon className="text-primary-2-800"></EllipsisVerticalIcon>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-40 sm:w-56"
              side="left"
              align="start"
            >
              <DropdownMenuGroup>
                <DropdownMenuSub>
                  <DropdownMenuItem className="cursor-pointer">
                    <div
                      onClick={() => {
                        const params = new URLSearchParams(searchParams);
                        params.set("viewAgentId", conversation.agentId);
                        router.push(`?${params}`);
                      }}
                      className="flex w-full items-center gap-5"
                    >
                      <Image
                        src="/icons/profile.svg"
                        alt="profile"
                        height={100}
                        width={100}
                        className="size-5"
                      />
                      <span>View Profile</span>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuSub>
                <DropdownMenuSub>
                  <DropdownMenuItem
                    className="cursor-pointer gap-5"
                    onClick={() => router.push(`/profile/help-center`)}
                  >
                    <Image
                      src="/icons/help-center.svg"
                      alt="profile"
                      height={100}
                      width={100}
                      className="size-5"
                    />
                    <span>Help Center</span>
                  </DropdownMenuItem>
                </DropdownMenuSub>
                <DropdownMenuSub>
                  <DropdownMenuItem
                    className="cursor-pointer gap-5"
                    onClick={() => setReportDialogOpen(true)}
                  >
                    <Image
                      src="/icons/report.svg"
                      alt="profile"
                      height={100}
                      width={100}
                      className="size-5"
                    />

                    <span>Report User</span>
                  </DropdownMenuItem>
                </DropdownMenuSub>
                <DropdownMenuItem className="cursor-pointer gap-5">
                  <Dialog>
                    <DialogTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <div className="flex w-full gap-5">
                        <Image
                          src="/icons/delete.svg"
                          alt="profile"
                          height={100}
                          width={100}
                          className="size-5"
                        />
                        <span>Remove Agent</span>
                      </div>
                    </DialogTrigger>
                    <DialogContent
                      className="sm:max-w-[425px]"
                      onClick={(e) => e.preventDefault()}
                    >
                      <DialogHeader>
                        <DialogTitle>Remove Agent</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to remove this agent?
                          <br />
                          Please note that submitting this request will also
                          delete your chat history with this agent.
                        </DialogDescription>
                      </DialogHeader>

                      <DialogFooter>
                        {deleteChatIsPending ? (
                          <LoadingButton loading>Submitting...</LoadingButton>
                        ) : (
                          <Button type="submit" onClick={handleRemoveAgent}>
                            Confirm
                          </Button>
                        )}
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
          <Dialog open={reportDialogOpen} onOpenChange={setReportDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Report Agent</DialogTitle>
                <DialogDescription>
                  Please provide a brief explanation for why you are reporting
                  this agent.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-3">
                <Textarea
                  value={reportReason}
                  onChange={(e) => setReportReason(e.target.value)}
                  placeholder="Reason to report the agent."
                  onKeyDown={(e) => {
                    if (e.key === " ") {
                      e.stopPropagation();
                    }
                  }}
                />

                {reportAgentFromChatIsPending ? (
                  <LoadingButton loading>reporting...</LoadingButton>
                ) : (
                  <Button
                    className="flex items-center gap-2"
                    onClick={handleReportAgent}
                  >
                    Submit
                  </Button>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <Separator></Separator>
    </>
  );
};

export default ChatScreenHeader;
