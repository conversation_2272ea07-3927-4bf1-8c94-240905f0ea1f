"use client";

import { cn } from "@repo/ui/lib/utils";
import { useSearchParams } from "next/navigation";
import React from "react";
import type { TConversationWithAgentCustomerMessages } from "~/app/types";
import ChatListScreen from "./chat-list-screen";
import ChatScreen from "./chat-screen";
import { CHAT_PARAM_NAME } from "~/app/utils/constants";

type ChatClientProps = {
  conversation: TConversationWithAgentCustomerMessages | null;
};

const ChatClient = ({ conversation }: ChatClientProps) => {
  const searchParams = useSearchParams();
  const isConversationId = !!searchParams.get(CHAT_PARAM_NAME);

  if (!conversation) {
    return (
      <div className="flex min-h-28 items-center justify-center text-xl font-semibold text-primary-700">
        No Conversation Found
      </div>
    );
  }

  return (
    <div className="sticky top-20 flex max-h-screen lg:-mx-6 lg:-my-5 xl:top-24">
      <div
        className={cn(
          "max-h-[calc(100vh-32vh)] min-h-[calc(100vh-32vh)] w-full border-primary-150 lg:max-w-[324px] lg:border-r xl:max-w-[415px] 2xl:max-w-[485px]",
          isConversationId ? "hidden lg:block" : "block",
        )}
      >
        <ChatListScreen conversation={conversation} />
      </div>
      <div
        className={cn(
          "w-full lg:border-l",
          isConversationId ? "block" : "hidden",
        )}
      >
        <ChatScreen conversation={conversation}></ChatScreen>
      </div>
    </div>
  );
};

export default ChatClient;
