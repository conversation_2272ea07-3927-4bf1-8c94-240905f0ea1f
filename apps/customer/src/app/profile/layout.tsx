import React from "react";
import ProfileSideBar from "@repo/ui/components/shared/profile-side-bar";
import type { Metadata } from "next";

import {
  ProfileSidebarReferralsAndFeedbackLinks,
  ProfileSidebarReportAndHelpLinks,
  ProfileSidebarSettingsLinks,
} from "../utils/constants";
import LogoutButton from "../components/shared/logout-button";
import ProfileCard from "../components/shared/profile-card";
import DeleteButton from "../components/shared/delete-button";

export const metadata: Metadata = {
  title: "Profile - My Deer",
  description: "Manage your My Deer account and profile information",
};

const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="my-5 flex rounded-xl border-2 border-secondary-2-100 bg-white md:mx-5 md:my-6 lg:my-8 xl:my-10 2xl:my-12">
      <nav className="sticky top-[90px] z-20 hidden h-full w-full border-r-2 border-r-secondary-2-100 bg-white lg:block lg:max-w-[267px] xl:max-w-[270px] 2xl:max-w-[370px]">
        <ProfileSideBar
          profileCard={<ProfileCard />}
          logoutButton={<LogoutButton />}
          linksArray1={ProfileSidebarSettingsLinks}
          linksArray2={ProfileSidebarReportAndHelpLinks}
          linksArray3={ProfileSidebarReferralsAndFeedbackLinks}
          deleteAccount={<DeleteButton></DeleteButton>}
        />
      </nav>

      <div className="w-full px-5 py-[18px] md:px-6 md:py-5 xl:px-8 xl:py-6 2xl:px-9 2xl:py-8">
        {children}
      </div>
    </div>
  );
};

export default Layout;
