import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@repo/ui/components/ui/tabs";
import { api } from "~/trpc/server";
import PropertyCard from "@repo/ui/components/shared/property-card";
// import ContactCheckResponsesButton from "~/app/_components/shared/contact-checkresponses-button";
// import EditPropertyButton from "~/app/_components/shared/edit-property-button";
// import LikePropertyButton from "~/app/_components/shared/like-property-button";
// import SharePropertyButton from "~/app/_components/shared/share-property-button";
// import MyListingsTab from "./my-listings-tab";
import LikePropertyButton from "~/app/components/shared/like-property-button";
import SharePropertyButton from "~/app/components/shared/share-property-button";
import ContactAgentButton from "~/app/components/shared/contact-agent-button";
import AgentCard from "@repo/ui/components/shared/agent-card";

const FavouritesTabs = async () => {
  //   const { data: session } = useSession();
  const favouriteProperties = await api.user.getFavouritesProperties();
  const favouriteAgents = await api.likeAgent.getFavouritesAgents();
  const customerDetail = await api.user.getProfile();

  return (
    <Tabs defaultValue="properties">
      <TabsList className="flex items-center justify-start gap-10 overflow-x-auto border-b-2 border-primary-2-300 px-6">
        <TabsTrigger
          value="properties"
          className="px-[21px] py-2 text-sm text-text-600 data-[state=active]:border-b-2 data-[state=active]:border-primary-2-700 data-[state=active]:bg-transparent data-[state=active]:font-airbnb_w_md data-[state=active]:font-medium data-[state=active]:text-text-600 data-[state=active]:shadow-none 2xl:px-[15px] 2xl:py-1.5"
        >
          Properties
        </TabsTrigger>
        <TabsTrigger
          value="agents"
          className="px-[21px] py-2 text-sm text-text-600 data-[state=active]:border-b-2 data-[state=active]:border-primary-2-700 data-[state=active]:bg-transparent data-[state=active]:font-airbnb_w_md data-[state=active]:font-medium data-[state=active]:text-text-600 data-[state=active]:shadow-none 2xl:px-[15px] 2xl:py-1.5"
        >
          Agents
        </TabsTrigger>
      </TabsList>
      <div className="py-4 xl:py-[18px] 2xl:py-8">
        {/* favourie properties */}
        <TabsContent
          value="properties"
          className="grid w-full max-w-full grid-cols-1 gap-4 md:grid-cols-2 lg:gap-[22px] xl:grid-cols-3 xl:gap-5 2xl:grid-cols-4"
        >
          {favouriteProperties.length > 0 ? (
            favouriteProperties.map((item) => (
              <PropertyCard
                key={item.id}
                className="min-w-min max-w-full"
                locationIcon="/icons/location.svg"
                id={item.propertyId}
                property={item.property}
                propertyOwnerId={item.property.userId}
                contactOrCheckResponsesButton={
                  <ContactAgentButton
                    agentId={item.property.userId}
                    connectedAgentId={
                      customerDetail.connections[0]?.agentId ?? ""
                    }
                  />
                }
                likePropertyButton={
                  <LikePropertyButton
                    propertyId={item.property.id}
                    isPropertyLiked={!!item.property.customerFavourites.length}
                  />
                }
                sharePropertyButton={
                  <SharePropertyButton propertyId={item.propertyId} />
                }
              />
            ))
          ) : (
            <div className="col-span-full flex min-h-[20vh] items-center justify-center text-center text-lg text-primary-800 sm:text-xl">
              Liked properties will appear here
            </div>
          )}
        </TabsContent>

        {/* favourite agents */}
        <TabsContent
          value="agents"
          className="grid w-full max-w-full grid-cols-1 gap-4 md:grid-cols-2 lg:gap-[22px] xl:grid-cols-3 xl:gap-5 2xl:grid-cols-4"
        >
          {favouriteAgents.length > 0 ? (
            favouriteAgents.map((item) => (
              <AgentCard {...item.likedAgent}></AgentCard>
            ))
          ) : (
            <div className="col-span-full flex min-h-[20vh] items-center justify-center text-center text-lg text-primary-800 sm:text-xl">
              Liked agents will appear here
            </div>
          )}
        </TabsContent>
      </div>
    </Tabs>
  );
};

export default FavouritesTabs;
