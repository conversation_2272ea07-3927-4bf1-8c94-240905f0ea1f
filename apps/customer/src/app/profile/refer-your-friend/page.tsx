"use client";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import Image from "next/image";

import React from "react";

import { toast } from "@repo/ui/components/ui/sonner";
import { api } from "~/trpc/react";

import ProfilePageTitleWithBackButton from "~/app/components/shared/profile-title";

import { WhatsappShareButton } from "react-share";
import SharePropertyButton from "~/app/components/shared/share-property-button";
import Loading from "~/app/components/shared/loading";
import GenerateCustomInviteCodeDialog from "./generate-custom-invite-code-dialog";
import ShareWebsiteButton from "./share-website-button";

const ReferYourFriend = () => {
  const { data: profile, isPending } = api.user.getProfile.useQuery();
  const inviteCode = profile?.inviteCode ?? "N/A";
  const link = window.location.host + `/sign-up?referralCode=${inviteCode}`;
  const handleCopyCode = async () => {
    await window.navigator.clipboard.writeText(inviteCode);

    toast.success("Invite code copied to your clipboard");
  };
  return (
    <div className="space-y-4 md:space-y-[18px] lg:space-y-5 xl:space-y-6">
      <ProfilePageTitleWithBackButton title="Refer your friend" />

      <div className="flex flex-col items-center space-y-6 text-center xl:space-y-7">
        <div className="flex flex-col items-center justify-center">
          <p className="font-airbnb_w_bk text-sm text-text-550 xl:text-base 2xl:text-lg">
            Invite Agent
          </p>
          <h1 className="mb-2 font-airbnb_w_blk text-lg font-extrabold text-primary-2-700 md:mb-[10px] md:text-xl lg:mb-3 lg:text-2xl xl:mb-4 2xl:text-3xl">
            Invite Agents & Earn
          </h1>
          <p className="font-airbnb_w_bk text-sm text-text-500 xl:text-base 2xl:text-lg">
            Refer agents to join our platform & earn rewards
          </p>
        </div>

        <div className="relative aspect-square w-[300px] md:w-[400px] lg:w-[500px] xl:w-[600px]">
          <Image
            src="/images/refer-your-friend.jpg"
            alt="refer-your-friend"
            fill={true}
            className=""
          />
        </div>

        <p
          className="flex cursor-pointer items-center gap-2 rounded-2xl bg-secondary-2-100 px-[18px] py-[10px] font-airbnb_w_bk text-xs text-text-600 xl:text-base"
          onClick={handleCopyCode}
        >
          Invite code:{" "}
          {isPending ? (
            <Loading className="border-primary-2-500"></Loading>
          ) : (
            inviteCode
          )}
          <Image
            src="/icons/copy.svg"
            alt="copy"
            height={50}
            width={50}
            className="size-3"
          />
        </p>
        <GenerateCustomInviteCodeDialog></GenerateCustomInviteCodeDialog>

        <div className="flex w-full items-center justify-around pt-8 md:pt-9">
          <Button className="group">
          <WhatsappShareButton url={link}>
              <div className="flex items-center gap-2 text-sm font-medium text-white lg:text-base xl:text-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="25"
                  height="24"
                  viewBox="0 0 25 24"
                  fill="none"
                  className="size-5 xl:size-6"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M20.456 4.00809C18.351 1.90063 15.5515 0.739517 12.5692 0.738281C6.42369 0.738281 1.42216 5.73967 1.41969 11.8868C1.41887 13.8518 1.93221 15.7701 2.90793 17.4608L1.32617 23.2383L7.23668 21.6878C8.86526 22.5762 10.6987 23.0444 12.5646 23.0449H12.5693C18.7141 23.0449 23.7162 18.0431 23.7185 11.8957C23.7197 8.9165 22.5611 6.1154 20.456 4.00809ZM12.5692 21.162H12.5653C10.9025 21.1613 9.27176 20.7144 7.84875 19.8703L7.51051 19.6694L4.00313 20.5895L4.9393 17.1698L4.71889 16.8192C3.79123 15.3438 3.30138 13.6384 3.3022 11.8875C3.30412 6.77815 7.46135 2.62134 12.5729 2.62134C15.0481 2.62216 17.3749 3.58731 19.1244 5.33894C20.874 7.09058 21.837 9.41885 21.8361 11.895C21.8339 17.0048 17.677 21.162 12.5692 21.162ZM17.6523 14.2215C17.3738 14.082 16.004 13.4083 15.7486 13.3152C15.4935 13.2222 15.3075 13.1759 15.122 13.4547C14.9362 13.7335 14.4024 14.3611 14.2398 14.5469C14.0772 14.7328 13.9149 14.7561 13.6362 14.6166C13.3576 14.4772 12.46 14.1829 11.3958 13.2339C10.5677 12.4952 10.0087 11.5829 9.84607 11.3041C9.68375 11.0251 9.8447 10.8888 9.96829 10.7356C10.2699 10.3611 10.5719 9.96844 10.6647 9.78264C10.7577 9.59669 10.7111 9.43396 10.6413 9.29457C10.5719 9.15518 10.0147 7.78395 9.78262 7.22598C9.5563 6.68298 9.32683 6.75632 9.15572 6.7478C8.99339 6.7397 8.80759 6.73805 8.62178 6.73805C8.43611 6.73805 8.13426 6.80768 7.87883 7.08673C7.62354 7.36565 6.90393 8.03952 6.90393 9.41075C6.90393 10.782 7.90218 12.1067 8.04143 12.2926C8.18068 12.4785 10.0059 15.2924 12.8004 16.499C13.4651 16.7863 13.9839 16.9575 14.3886 17.0859C15.0561 17.298 15.6632 17.268 16.1433 17.1964C16.6786 17.1163 17.7914 16.5223 18.0237 15.8717C18.2558 15.2209 18.2558 14.6632 18.1861 14.5469C18.1166 14.4307 17.9308 14.3611 17.6523 14.2215Z"
                    fill="white"
                  />
                </svg>
                Whatsapp
              </div>
            </WhatsappShareButton>
          </Button>
          <ShareWebsiteButton link={link} />
        </div>
      </div>
    </div>
  );
};

export default ReferYourFriend;
