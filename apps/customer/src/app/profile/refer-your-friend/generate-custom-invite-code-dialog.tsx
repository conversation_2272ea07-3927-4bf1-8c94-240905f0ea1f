"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@repo/ui/components/ui/button";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
const formSchema = z.object({
  inviteCode: z.string().min(4),
});

const GenerateCustomInviteCodeDialog = () => {
  const [open, setOpen] = useState(false);
  const trpcUtils = api.useUtils();
  const updateInviteCode = api.user.updateInviteCode.useMutation();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      inviteCode: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    updateInviteCode.mutate(values, {
      onSuccess: (opts) => {
        if (opts.warning) {
          toast.warning(opts.message);
          return;
        }
        setOpen(false);
        void trpcUtils.user.invalidate();
        toast.success(opts.message);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
    console.log(values);
  }
  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" onClick={() => setOpen(true)}>
            Generate custom invite code
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Generate custom invite code</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="inviteCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invite code</FormLabel>
                    <FormControl>
                      <Input placeholder="type here.." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex w-full justify-end">
                <Button className="self-end" type="submit">
                  Submit
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GenerateCustomInviteCodeDialog;
