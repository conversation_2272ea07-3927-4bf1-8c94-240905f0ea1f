"use client";

import React from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";
import {
  FacebookShareButton,
  FacebookIcon,
  EmailShareButton,
  EmailIcon,
  TelegramShareButton,
  TelegramIcon,
} from "react-share";
import { CopyIcon } from "lucide-react";
import { toast } from "@repo/ui/components/ui/sonner";
import { Button } from "@repo/ui/components/ui/button";
import { usePathname, useSearchParams } from "next/navigation";

export function ShareIcons({ link }: { link: string }) {
  //   const link = window.location.host;

  return (
    <div className="flex justify-center gap-2 sm:gap-4">
      <FacebookShareButton url={link}>
        <FacebookIcon className="size-10 sm:size-16" round={true} />
      </FacebookShareButton>
      <EmailShareButton url={link}>
        <EmailIcon className="size-10 sm:size-16" round={true} />
      </EmailShareButton>
      <TelegramShareButton url={link}>
        <TelegramIcon className="size-10 sm:size-16" round={true} />
      </TelegramShareButton>
    </div>
  );
}

const ShareWebsiteButton = ({ link }: { link: string }) => {
  const handleCopy = async () => {
    // const link = window.location.host;

    await navigator.clipboard.writeText(link);
    toast.success("Link has been copied to your clipboard");
  };
  return (
    <>
      <Dialog>
        <DialogTrigger>
          <Button variant={"secondary"} className="flex items-center gap-2">
            <Image
              src="/icons/share.svg"
              alt="share"
              height={24}
              width={24}
              className="size-5 xl:size-6"
            />
            <div className="text-sm lg:text-base xl:text-lg">More option</div>
          </Button>
        </DialogTrigger>
        <DialogContent className="w-[300px] rounded-2xl bg-white p-10 sm:w-[400px] sm:rounded-2xl md:w-[512px]">
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between">
              <p className="font-raleway text-2xl font-bold -tracking-[0.23px] text-primary-2-800">
                Share It
              </p>
            </div>
            <div>
              <ShareIcons link={link} />
            </div>
            <Button
              className="flex w-full items-center justify-between rounded-xl bg-white px-4 py-[11px] hover:bg-primary-2-400 active:bg-white"
              onClick={handleCopy}
            >
              <p className="text-lg text-primary-2-800">Copy Link</p>
              <CopyIcon className="text-primary-2-800"></CopyIcon>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ShareWebsiteButton;
