import React from "react";
import ProfilePageTitleWithBackButton from "~/app/components/shared/profile-title";
import { api } from "~/trpc/server";
import ReviewCard from "./review-card";

const page = async () => {
  const customerReviews = await api.customerReviews.getCustomerReviews();

  return (
    <div className="flex flex-col gap-4 md:gap-[18px] lg:gap-5 xl:gap-6 2xl:gap-8">
      <ProfilePageTitleWithBackButton title="My Reviews" />
      {customerReviews.length > 0 ? (
        <div className="flex flex-col gap-[30px] lg:gap-5 xl:grid xl:grid-cols-2 xl:gap-[30px] 2xl:gap-[40px]">
          {customerReviews.map((review) => (
            <ReviewCard review={review} />
          ))}
        </div>
      ) : (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-lg text-primary-2-800 lg:text-xl">
          Your reviews will appear here.
        </div>
      )}
    </div>
  );
};

export default page;
