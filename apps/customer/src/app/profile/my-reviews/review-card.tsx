"use client";
import { Circle<PERSON>, Ellipsis } from "lucide-react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import Image from "next/image";
import type { Prisma } from "@repo/database";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { api } from "~/trpc/react";
import { useRouter, useSearchParams } from "next/navigation";

import "@smastrom/react-rating/style.css";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { CldUploadWidget, CldVideoPlayer } from "next-cloudinary";
import type { CloudinaryUploadWidgetResults } from "next-cloudinary";

import { env } from "~/env";
type IReview = Prisma.CustomerRatingsToAgentsGetPayload<{
  select: {
    id: true;
    userStarsCount: true;
    userRatingMessage: true;
    fileKey: true;
    filePublicUrl: true;
    cloudinaryPublicId: true;
    cloudinaryUrl: true;
    ratedTo: {
      select: {
        id: true;
        name: true;
        userLocation: true;
        filePublicUrl: true;
        cloudinaryProfileImageUrl: true;
        city: {
          select: {
            name: true;
          };
        };
        company: {
          select: {
            id: true;
            companyName: true;
          };
        };
      };
    };
  };
}>;
const ReviewCard = ({ review }: { review: IReview }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const session = useSession();
  const [open, setOpen] = useState<boolean>(false);
  const [dialog, setDialog] = useState<boolean>(false);

  const updateReview = api.customerReviews.updateCustomerReviews.useMutation();

  const { mutate: deleteReview, isPending: removeAgentIsPending } =
    api.customerReviews.deleteCustomerReviewToAgent.useMutation();

  const handleUploadVideo = async (result: CloudinaryUploadWidgetResults) => {
    const { info } = result as {
      info: { public_id: string; secure_url: string };
      event: string;
    };

    const cloudinaryPublicId = info.public_id;
    const cloudinaryUrl = info.secure_url;

    if (!cloudinaryPublicId || !cloudinaryUrl) {
      toast.error("Upload failed");
      return;
    }

    await updateReview.mutateAsync(
      {
        id: review.id,
        cloudinaryPublicId: cloudinaryPublicId,
        cloudinaryUrl: cloudinaryUrl,
      },
      {
        onSuccess: () => {
          router.refresh();
          toast.success("Video uploaded successfully.");
        },
        onError: (err) => {
          toast.error(err.message);
        },
      },
    );
  };

  const handleDeleteReview = () => {
    deleteReview(
      { reviewId: review.id },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          router.refresh();
          setDialog(false);
        },
        onError: (err) => {
          toast.error(err.message);
        },
      },
    );
  };

  const getFileName = (url?: string | null) => {
    if (!url) return "";
    return url.split("/").pop() ?? "";
  };

  const hasVideo = Boolean(review.cloudinaryUrl ?? review.filePublicUrl);

  return (
    <>
      <div className="flex flex-col gap-4 rounded-2xl border-2 border-secondary-2-100 p-4 md:gap-5 md:p-5 lg:p-6">
        {/* agent-rating */}
        <div className="flex w-fit flex-row gap-1 rounded-sm bg-[#FFFBEF] px-3 py-1.5 font-airbnb_w_md text-sm font-medium text-primary-2-750 xl:text-base">
          Rated :{" "}
          <span className="flex items-center gap-1">
            <Image
              src="/icons/star.svg"
              alt="star"
              height={100}
              width={100}
              className="size-5"
            />
            {review.userStarsCount}
          </span>
        </div>
        {/* Agent details */}
        <div className="flex flex-row items-start justify-between">
          <div className="flex w-[99%] flex-row gap-2 md:gap-4">
            <div className="relative aspect-square size-[52px] lg:size-[70px]">
              <Image
                src={
                  review.ratedTo.cloudinaryProfileImageUrl ??
                  review.ratedTo.filePublicUrl ??
                  "/images/agent-fallback.png"
                }
                alt="customer-image"
                className="rounded-[17.2px] object-cover"
                fill
              />
            </div>
            <div className="flex flex-col gap-1.5 lg:gap-3">
              <div className="font-airbnb_w_xbd text-xl font-extrabold leading-[30px] text-primary-2-750 lg:text-2xl">
                {review.ratedTo.name}
              </div>
              <div className="font-airbnb_w_bk text-sm font-normal text-text-600 lg:text-base">
                {review.ratedTo.company &&
                  `${review.ratedTo.company.companyName}, `}
                {review.ratedTo.city?.name && review.ratedTo.city.name}
              </div>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="rounded-[6px] p-1 hover:cursor-pointer">
                <Ellipsis size={20} strokeWidth={2} color="#848484" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-40 p-2 sm:w-[165px]"
              side="left"
              align="start"
            >
              <DropdownMenuGroup className="flex flex-col gap-2">
                <DropdownMenuItem className="cursor-pointer gap-1">
                  <div
                    onClick={() => {
                      const params = new URLSearchParams(searchParams);
                      params.set("viewAgentId", review.ratedTo.id);
                      router.push(`?${params}`);
                    }}
                    className="flex items-center gap-2"
                  >
                    <Image
                      src="/icons/profile.svg"
                      alt="profile"
                      height={100}
                      width={100}
                      className="size-5"
                    />
                    <span>View Profile</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer">
                  <div onClick={() => setDialog(true)}>
                    <div className="flex w-full items-center gap-2">
                      <Image
                        src="/icons/delete.svg"
                        alt="profile"
                        height={100}
                        width={100}
                        className="size-5"
                      />
                      <span>Delete Review</span>
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        {/* Review message */}
        <p className="font-airbnb_w_bk text-sm font-normal text-text-500 md:text-base xl:text-lg">
          {review.userRatingMessage}
        </p>
        {/* Video upload or playback */}
        <div className="flex flex-col gap-4 rounded-lg bg-[#FFFAF9] p-4">
          <div>
            <div className="mb-1.5 font-airbnb_w_md text-sm font-medium text-text-700 lg:text-base">
              Video Uploaded
            </div>
            {hasVideo ? (
              <button className="w-full truncate rounded-xl border border-text-100 bg-[#FFFAF9] px-4 py-3 text-left font-airbnb_w_bk text-sm font-normal text-text-600 lg:px-5 lg:py-[14px] lg:text-base">
                {getFileName(review.cloudinaryUrl ?? review.filePublicUrl)}
              </button>
            ) : (
              <div className="w-full truncate rounded-xl border border-text-100 bg-[#FFFAF9] px-4 py-3 text-left font-airbnb_w_bk text-sm font-normal text-text-600 lg:px-5 lg:py-[14px] lg:text-base">
                No video uploaded
              </div>
            )}
          </div>

          {hasVideo ? (
            <>
              <Button
                onClick={() => setOpen(true)}
                variant={"outline"}
                className="w-[231px] border-[1.4px] font-airbnb_w_md"
              >
                Watch Uploaded Video
              </Button>
            </>
          ) : (
            <CldUploadWidget
              signatureEndpoint="/api/cloudinary"
              uploadPreset={env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET}
              onSuccess={handleUploadVideo}
              options={{
                croppingShowDimensions: true,
                croppingValidateDimensions: true,
                showSkipCropButton: false,
                maxFiles: 1,
                resourceType: "auto",
                showPoweredBy: false,
                maxFileSize: 100 * 1024 * 1024, // 100MB
                clientAllowedFormats: ["mp4", "mov", "avi", "mkv"],
                folder: `${env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME}/customers/${session.data?.user?.id}/ratings`,
                styles: {
                  zIndex: 9999,
                  position: "fixed",
                  modal: { zIndex: 9999 },
                },
                showAdvancedOptions: false,
                showUploadMoreButton: false,
                singleUploadAutoClose: false,
              }}
            >
              {({ open }) => (
                <Button
                  onClick={() => open()}
                  variant={"outline"}
                  className="w-[231px] border-[1.4px] font-airbnb_w_md"
                >
                  Upload
                </Button>
              )}
            </CldUploadWidget>
          )}
        </div>
      </div>

      {/* video upload dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="item-center flex h-[90vh] w-[80%] justify-center rounded-xl bg-black md:w-[70%]">
          {/* Show standard video player if S3 URL exists */}
          {review.filePublicUrl && !review.cloudinaryPublicId && (
            <video
              src={review.filePublicUrl}
              controls
              muted
              className="h-full w-full object-contain"
            ></video>
          )}

          {/* Show Cloudinary player if cloudinaryPublicId exists */}
          {review.cloudinaryPublicId && (
            <CldVideoPlayer
              src={review.cloudinaryPublicId}
              controls
              muted
              className="h-full w-full object-contain"
            ></CldVideoPlayer>
          )}

          <DialogFooter className="absolute right-6 top-6">
            <CircleX
              className="size-6 cursor-pointer text-white"
              onClick={() => setOpen(false)}
            />
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* delete review dialog */}
      <Dialog open={dialog} onOpenChange={setDialog}>
        <DialogContent
          className="sm:max-w-[425px]"
          onClick={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>Delete Review</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this review?
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            {removeAgentIsPending ? (
              <LoadingButton loading>Submitting...</LoadingButton>
            ) : (
              <Button type="submit" onClick={handleDeleteReview}>
                Confirm
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ReviewCard;
