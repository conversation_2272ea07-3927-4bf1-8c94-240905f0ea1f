import Image from "next/image";
import Link from "next/link";
import type { LinkProps } from "next/link";
import React from "react";

// import { api } from "~/trpc/server";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@repo/ui/components/ui/accordion";
import HeadingBadge from "~/app/components/shared/heading-badge";
import ProfilePageTitleWithBackButton from "~/app/components/shared/profile-title";
import { api } from "~/trpc/server";

const ContactInfo = [
  {
    icon: "/icons/email.svg",
    text: "Email",
    relevantInfo: "<EMAIL>",
    href: "mailto:<EMAIL>",
  },
  {
    icon: "/icons/phone.svg",
    text: "Contact Number",
    relevantInfo: "7043807438",
    href: "whatsapp://send?abid=7043807438&text=Hello%2C%20World!",
  },
];

interface ContactCardProp extends LinkProps {
  icon: string;
  text: string;
  relevantInfo: string;
}

const ContactCard: React.FC<ContactCardProp> = ({
  icon,
  text,
  relevantInfo,
  ...props
}) => {
  return (
    <Link {...props} className="flex w-full items-start gap-4 px-3 py-2">
      <div className="flex items-center justify-center rounded-full bg-primary-100 p-[6.73px]">
        <Image
          src={icon}
          alt={text}
          height={50}
          width={50}
          className="size-6 lg:size-[33px]"
        />
      </div>
      <div className="space-y-[5px]">
        <p className="font-medium text-primary-600 lg:text-lg">{text}</p>
        <p className="text-text-550">{relevantInfo}</p>
      </div>
    </Link>
  );
};

const HelpCenter = async () => {
  const faqs = await api.faq.getAllFaq({
    project: "B2C_MY_DEER",
    page: "HELP_CENTER_PAGE",
  });

  return (
    <div className="space-y-4 md:space-y-[18px] lg:space-y-5 xl:space-y-6">
      <ProfilePageTitleWithBackButton title="Help Center" />

      <div className="space-y-[18px] md:space-y-6 xl:space-y-8">
        {/* contact info cards */}
        <div className="flex flex-col items-center gap-[18px] rounded-3xl border-2 border-primary-100 bg-[#FFFEFD] p-4 md:gap-5 lg:p-6 xl:space-y-[30px]">
          {/* title and summary */}
          <div className="flex flex-col items-center justify-center gap-2 text-center">
            <HeadingBadge content="Help & Support" />
            <p className="font-airbnb_w_bk text-sm text-text-600 lg:text-base">
              Feel free to reach out to us with any inquiries, feedback, or
              requests. Our dedicated team is committed to providing you with
              exceptional customer service. We look forward to hearing from you!
            </p>
          </div>

          {/* contact details */}
          <div className="flex w-full flex-col gap-2 md:flex-row">
            {ContactInfo.map((item, idx) => (
              <ContactCard key={idx} {...item} />
            ))}
          </div>
        </div>

        {/* accordion */}
        {faqs.length > 0 && (
          <div className="space-y-[18px] lg:space-y-6 xl:space-y-[30px]">
            <div className="flex flex-col items-center gap-2">
              <HeadingBadge content="FAQ" />
              <h1 className="text-center font-airbnb_w_bd text-xl font-bold lg:text-2xl">
                You Have Question. We’ve Your Answer
              </h1>
            </div>

            <div>
              <Accordion type="single" className="space-y-5" collapsible>
                {faqs.map((item, idx) => (
                  <AccordionItem
                    key={idx}
                    value={`item-${idx}`}
                    className="rounded-xl border border-text-500 border-opacity-30 p-5 data-[state=open]:border-[#5F3924] data-[state=open]:border-opacity-30 data-[state=open]:bg-[#FFFCF4]"
                  >
                    <AccordionTrigger className="text-start font-medium text-text-550 data-[state=open]:text-primary-2-800 lg:text-lg">
                      {item.question}
                    </AccordionTrigger>
                    <AccordionContent className="font-airbnb_w_bk text-sm text-text-550">
                      {item.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HelpCenter;
