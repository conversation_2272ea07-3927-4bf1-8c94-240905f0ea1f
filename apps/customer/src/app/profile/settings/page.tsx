import Image from "next/image";
import React from "react";

import { Switch } from "@repo/ui/components/ui/switch";
import ProfilePageTitleWithBackButton from "~/app/components/shared/profile-title";

const Settings = () => {
  return (
    <div className="space-y-4 lg:space-y-5 xl:space-y-6 2xl:space-y-8">
      {/* title */}
      <ProfilePageTitleWithBackButton title="Settings" />

      <div className="space-y-[14px] xl:space-y-5 2xl:space-y-[18px]">
        {/* general settings */}
        <div className="2xl:space-y-4">
          {/* title */}
          <p className="font-airbnb_w_bd text-lg font-bold text-text-600 xl:text-xl">
            General Settings
          </p>

          <div className="flex items-start gap-4 py-3">
            {/* bell icon */}
            <Image
              src="/icons/notification-bell.svg"
              height={50}
              width={50}
              className="size-6"
              alt="bell"
            />

            {/* notification */}
            <div className="flex-1 space-y-1 xl:space-y-2 2xl:space-y-4">
              <div>
                <p className="font-medium text-primary-2-800 md:text-sm lg:text-base xl:text-lg">
                  Notification
                </p>
                <p className="font-airbnb_w_bk text-[10px] md:text-sm lg:text-base">
                  Toggle on to mute the notification, you will not receive any
                  notification related to app
                </p>
              </div>
            </div>

            {/* switch */}
            <Switch className="bg-primary-2-700" />
          </div>
        </div>

        {/* chat settings */}
        <div className="2xl:space-y-4">
          {/* title */}
          <p className="font-airbnb_w_bd text-lg font-bold text-text-600 xl:text-xl">
            Chat Settings
          </p>

          <div className="flex items-start gap-4 py-3">
            {/* bell icon */}
            <Image
              src="/icons/notification-bell.svg"
              height={50}
              width={50}
              className="size-6"
              alt="bell"
            />

            {/* notification */}
            <div className="flex-1 space-y-1 xl:space-y-2 2xl:space-y-4">
              <div>
                <p className="font-medium text-primary-2-800 md:text-sm lg:text-base xl:text-lg">
                  Notification
                </p>
                <p className="font-airbnb_w_bk text-[10px] md:text-sm lg:text-base">
                  Mute companion chat notification on home screen
                </p>
              </div>
            </div>

            {/* switch */}
            <Switch className="bg-primary-2-700" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
