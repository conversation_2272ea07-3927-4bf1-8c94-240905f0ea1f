import PropertyCard from "@repo/ui/components/shared/property-card";
import React from "react";
import ContactAgentButton from "~/app/components/shared/contact-agent-button";
import LikePropertyButton from "~/app/components/shared/like-property-button";
import SharePropertyButton from "~/app/components/shared/share-property-button";
import { api } from "~/trpc/server";

const page = async () => {
  const history = await api.user.getPropertyHistory();
  const customerDetail = await api.user.getProfile();

  return (
    <div className="my-4 grid grid-cols-1 place-items-center gap-5 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {history.length > 0 ? (
        history.map((item) => (
          <PropertyCard
            key={item.id}
            className="min-w-min max-w-full"
            locationIcon="/icons/location.svg"
            id={item.propertyId}
            property={item.property}
            propertyOwnerId={item.property.userId}
            contactOrCheckResponsesButton={
              <ContactAgentButton
                agentId={item.property.userId}
                connectedAgentId={customerDetail.connections[0]?.agentId ?? ""}
              />
            }
            likePropertyButton={
              <LikePropertyButton
                propertyId={item.propertyId}
                isPropertyLiked={!!item.property.customerFavourites.length}
              />
            }
            sharePropertyButton={
              <SharePropertyButton propertyId={item.propertyId} />
            }
          />
        ))
      ) : (
        <div className="col-span-full flex min-h-[20vh] items-center justify-center text-center text-lg text-primary-800 sm:text-xl">
          Your viewed properties will appear here
        </div>
      )}
    </div>
  );
};

export default page;
