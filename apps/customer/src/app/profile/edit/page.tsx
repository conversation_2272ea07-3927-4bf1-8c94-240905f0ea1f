"use server";

import Image from "next/image";
import React from "react";

import PersonalInfoForm from "./personal-info-form";

import EditUserImage from "./edit-user-image";
import ProfilePageTitleWithBackButton from "~/app/components/shared/profile-title";
import { api } from "~/trpc/server";

const Stats = async () => {
  const profile = await api.user.getProfile();

  if (!profile) {
    return null;
  }

  return (
    <div className="flex flex-col gap-6 md:flex-row xl:gap-10">
      {/* top - profile photo */}
      <div className="flex flex-col gap-4 lg:gap-5 2xl:gap-8">
        <ProfilePageTitleWithBackButton title="Edit Profile" />

        <div className="relative flex items-center justify-center rounded-lg p-[25px] shadow-[1px_1px_4px_0px_#F15F3A,-1px_-1px_4px_0px_#FFC727] backdrop-blur-lg lg:p-9 xl:p-[50px] 2xl:p-[60px]">
          <Image
            src={
              profile.cloudinaryImagePublicUrl ??
              profile.profileImagePublicUrl ??
              "/images/placeholder-user-image.jpg"
            }
            alt="profile"
            height={1000}
            width={1000}
            className="size-[130px] rounded-full object-cover xl:size-[175px] 2xl:size-[230px]"
          />
          {/* add image button */}
          <EditUserImage userId={profile.id} />
        </div>
      </div>

      {/* details */}
      <PersonalInfoForm user={profile} />
    </div>
  );
};

export default Stats;
