"use client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@repo/ui/components/ui/button";
import type { z } from "zod";
import { toast } from "@repo/ui/components/ui/sonner";
import { Textarea } from "@repo/ui/components/ui/textarea";

import { CustomerEditProfileSchema } from "@repo/validators";
import type { Customer } from "@repo/database";

import { api } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";

const PersonalInfoForm = ({ user }: { user: Customer }) => {
  const { mutate: updateProfile, isPending } =
    api.user.updateProfile.useMutation();

  const onSubmit = (values: z.infer<typeof CustomerEditProfileSchema>) => {
    updateProfile(values, {
      onSuccess: (opts) => {
        if (opts.warning) {
          toast.warning(opts.message);
          return;
        }
        toast.success(opts.message);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  const handleCancel = () => {
    form.reset();
  };

  const form = useForm<z.infer<typeof CustomerEditProfileSchema>>({
    resolver: zodResolver(CustomerEditProfileSchema),
    defaultValues: {
      name: user.name,
      email: user.email,
      phoneNumber: user.phoneNumber,
      adharcardNumber: user.adharcardNumber ?? undefined,
      bio: user.bio ?? "",
    },
  });

  return (
    <>
      <div className="flex-1 md:my-8 lg:my-10 2xl:my-16">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="relative space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Name
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: someone" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Email Id
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: <EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Phone Number
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 9876543212" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="adharcardNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Adhar Card Number
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 123456789012" {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-text-600 md:text-sm 2xl:text-xl">
                    Write Bio
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      className="bg-white"
                      {...field}
                      placeholder="Type here..."
                    ></Textarea>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="sticky bottom-0 flex items-center justify-between bg-[rgba(255,_251,_249,_0.30)] py-3 backdrop-blur-lg">
              <Button
                onClick={handleCancel}
                variant={"outline"}
                className="py-3 hover:bg-secondary-2-100 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
                type="button"
              >
                Cancel
              </Button>
              {isPending ? (
                <LoadingButton
                  className="py-3 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
                  loading
                >
                  Updating...
                </LoadingButton>
              ) : (
                <Button
                  type="submit"
                  className="py-3 2xl:px-6 2xl:py-[14px] 2xl:text-lg"
                >
                  Save Changes
                </Button>
              )}
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};

export default PersonalInfoForm;
