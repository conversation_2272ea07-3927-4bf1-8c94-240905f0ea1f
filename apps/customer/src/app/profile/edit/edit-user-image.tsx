"use client";

import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter } from "next/navigation";
import React from "react";
import { api } from "~/trpc/react";
import { CldUploadWidget } from "next-cloudinary";
import type { CloudinaryUploadWidgetResults } from "next-cloudinary";
import { env } from "~/env";
import { Camera } from "lucide-react";

const EditUserImage = ({ userId }: { userId: string }) => {
  const router = useRouter();

  const { mutate: updateProfileImage } =
    api.user.updateUserProfileMedia.useMutation();

  const handleUserProfileImageUpload = (
    result: CloudinaryUploadWidgetResults,
  ) => {
    const { info } = result as {
      info: { public_id: string; secure_url: string };
      event: string;
    };

    const cloudinaryPublicId = info.public_id;
    const cloudinaryUrl = info.secure_url;

    if (!cloudinaryPublicId || !cloudinaryUrl) {
      toast.error("Upload failed");
      return;
    }

    updateProfileImage(
      {
        cloudinaryPublicId: cloudinaryPublicId,
        cloudinaryUrl: cloudinaryUrl,
      },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          router.refresh();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <>
      <CldUploadWidget
        signatureEndpoint="/api/cloudinary"
        uploadPreset={env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET}
        onSuccess={handleUserProfileImageUpload}
        options={{
          cropping: true,
          croppingCoordinatesMode: "custom",
          croppingAspectRatio: 1,
          croppingShowDimensions: true,
          croppingValidateDimensions: true,
          showSkipCropButton: false,
          maxFiles: 1,
          showPoweredBy: false,
          resourceType: "image",
          clientAllowedFormats: ["png", "jpg", "jpeg"],
          folder: `${env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME}/customers/${userId}/profile`,
        }}
      >
        {({ open }) => (
          <button
            onClick={() => open()}
            className="absolute bottom-[25px] flex items-center gap-1 rounded-xl bg-secondary-2-100 p-[6px] text-[10px] font-medium text-primary-600 lg:bottom-[36px] xl:bottom-[50px] 2xl:bottom-[60px] 2xl:text-base"
          >
            <Camera className="size-3 text-primary-2-800 2xl:size-5" />
            Add Photo
          </button>
        )}
      </CldUploadWidget>
    </>
  );
};

export default EditUserImage;
