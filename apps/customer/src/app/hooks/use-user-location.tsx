"use client";

import { useEffect, useState } from "react";

type Location = {
  latitude: number;
  longitude: number;
};

const useUserLocation = () => {
  const [userLocation, setUserLocation] = useState<Location | null>(null);

  useEffect(() => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({
          latitude: latitude,
          longitude: longitude,
        });
      },
      (error) => {
        console.error("Error getting user location: ", error);
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0,
      },
    );
  }, []);

  return {
    lat: userLocation?.latitude,
    lng: userLocation?.longitude,
  };
};

export default useUserLocation;
