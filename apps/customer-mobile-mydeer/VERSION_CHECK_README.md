# Version Check Feature - Customer Mobile App

This feature automatically checks if the current app version meets the minimum required version and shows an update prompt if needed.

## How it works

1. **Version Check**: On app startup, the system compares the current app version with a minimum required version
2. **Update Prompt**: If the current version is below the minimum, a modal dialog appears asking the user to update
3. **Store Redirect**: When the user taps "Update Now", they are redirected to the appropriate app store (App Store for iOS, Play Store for Android)
4. **App Blocking**: Optionally, the app can be blocked from usage until updated

## Configuration

### Version Configuration (`src/config/version-config.ts`)

```typescript
export const VERSION_CONFIG = {
  // Minimum required version for the app to function
  MINIMUM_REQUIRED_VERSION: '0.0.4',
  
  // App store URLs for updates
  STORE_URLS: {
    ios: 'https://apps.apple.com/app/your-app-id', // Replace with actual App Store URL
    android: 'https://play.google.com/store/apps/details?id=com.nextflytech.mydeer',
  },
  
  // Version check settings
  SETTINGS: {
    CHECK_ON_START: true,      // Whether to check on app start
    ALLOW_DISMISS: true,       // Whether to allow dismissing the prompt
    BLOCK_APP_USAGE: true,     // Whether to block app usage if version is below minimum
  },
};
```

### How to update minimum version

1. Update `MINIMUM_REQUIRED_VERSION` in `src/config/version-config.ts`
2. The version format should be semantic (e.g., "1.2.3")
3. The system will automatically compare versions and show the update prompt if needed

## Features

### ✅ Version Comparison
- Semantic version comparison (e.g., "1.2.3" vs "1.2.0")
- Handles different version formats gracefully
- Supports major.minor.patch versioning

### ✅ User Experience
- Beautiful modal dialog with warning icon
- Clear version information display
- One-tap update button
- Optional dismiss functionality
- Multi-language support (English & Arabic)

### ✅ Platform Support
- iOS: Redirects to App Store
- Android: Redirects to Play Store
- Fallback error handling if store cannot be opened

### ✅ Configuration Options
- Enable/disable version checking
- Allow/block app usage for outdated versions
- Allow/disable dismiss functionality
- Configurable store URLs

### ✅ Integration
- Works with existing authentication flow
- Integrates with Expo Updates for runtime version checking

## Usage Examples

### Basic Usage
The version check is automatically performed on app startup. No additional code needed.

### Manual Version Check
```typescript
import { checkUpdateStatus } from '@/utils/version-check';

const status = checkUpdateStatus('1.0.0');
if (status.needsUpdate) {
  // Handle update requirement
}
```

## Translation Keys

The feature supports internationalization with the following keys:

### English (`src/translations/en.json`)
```json
{
  "update": {
    "required": {
      "title": "Update Required",
      "message": "A new version of the app is required to continue."
    },
    "current": {
      "version": "Current Version"
    },
"update": {
  "required": {
    "title": "Update Required",
    "message": "A new version of the app is required to continue.",
    "version": "Required Version"
  },
  "current": {
    "version": "Current Version"
  },
  // … any other keys under "update" …
}
    },
    "button": "Update Now",
    "dismiss": "Dismiss",
    "error": {
      "title": "Update Error",
      "message": "Unable to open app store. Please update manually."
    }
  },
  "common": {
    "ok": "OK"
  }
}
```

### Arabic (`src/translations/ar.json`)
```json
{
  "update": {
    "required": {
      "title": "تحديث مطلوب",
      "version": "الإصدار المطلوب",
      "message": "إصدار جديد من التطبيق مطلوب للمتابعة."
    },
    "current": {
      "version": "الإصدار الحالي"
    },
    "button": "تحديث الآن",
    "dismiss": "إلغاء",
    "error": {
      "title": "خطأ في التحديث",
      "message": "لا يمكن فتح متجر التطبيقات. يرجى التحديث يدوياً."
    }
  },
  "common": {
    "ok": "موافق"
  }
}
```

## Files Modified/Created

### New Files
- `src/utils/version-check.ts` - Version comparison utilities
- `src/components/update-prompt.tsx` - Update prompt modal component
- `src/config/version-config.ts` - Version configuration
- `VERSION_CHECK_README.md` - This documentation

### Modified Files
- `src/app/index.tsx` - Added version check logic
- `src/translations/en.json` - Added translation keys
- `src/translations/ar.json` - Added translation keys

## Testing

To test the version check feature:

1. **Set a higher minimum version**: Update `MINIMUM_REQUIRED_VERSION` to a version higher than the current app version (0.0.5)
2. **Run the app**: The update prompt should appear on startup
3. **Test dismiss**: If `ALLOW_DISMISS` is true, you should be able to dismiss the prompt
4. **Test update button**: Tapping "Update Now" should open the app store
5. **Test blocking**: If `BLOCK_APP_USAGE` is true, the app should not proceed without updating

## Current App Version

- **Package.json version**: 0.0.5
- **iOS version**: 0.0.4
- **Android version**: 0.0.5
- **Minimum required version**: 0.0.4

## Troubleshooting

### Update prompt not showing
- Check that `CHECK_ON_START` is set to `true`
- Verify that `MINIMUM_REQUIRED_VERSION` is higher than the current app version
- Ensure the version format is correct (e.g., "1.2.3")

### Store URL not working
- Update the `STORE_URLS` in the configuration with correct URLs
- Test the URLs manually in a browser
- Check platform-specific URL formats

### Translation not working
- Verify translation keys are added to both `en.json` and `ar.json`
- Check that the translation function is properly imported
- Ensure the language is set correctly in the app 