/* eslint-disable max-lines-per-function */
import type { ConfigContext, ExpoConfig } from '@expo/config';

import { ClientEnv, Env } from './env';

// const appIconBadgeConfig: AppIconBadgeConfig = {
//   enabled: Env.APP_ENV !== 'production',
//   badges: [
//     {
//       text: Env.APP_ENV,
//       type: 'banner',
//       color: 'white',
//     },
//     {
//       text: Env.VERSION.toString(),
//       type: 'ribbon',
//       color: 'white',
//     },
//   ],
// };

export default ({ config }: ConfigContext): ExpoConfig => ({
  // ...config,
  name: 'MyDeer',
  description: `MyDeer`,
  owner: 'nextfly_technologies',
  scheme: 'com.nextflytech.mydeer',
  slug: 'mydeer-customer',
  orientation: 'portrait',
  icon: './assets/adaptive-icon.png',
  userInterfaceStyle: 'light',
  newArchEnabled: true,
  // splash: {
  //   image: './assets/splash.png',
  //   resizeMode: 'cover',
  //   backgroundColor: '#2E3C4B',
  // },
  updates: {
    url: 'https://u.expo.dev/0d682abe-2acf-43bb-bc44-555db00b25ef',
    fallbackToCacheTimeout: 0,
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.nextflytech.mydeer',
    version: '0.0.5',
    runtimeVersion: '0.0.5',
    buildNumber: '2',
    icon: './assets/adaptive-icon.png',
    splash: {
      image: './assets/splash-icon.png',
      resizeMode: 'contain',
      backgroundColor: '#2E3C4B',
    },
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      NSCameraUsageDescription:
        'The app accesses your camera to let you take photos for reviews and update profile picture.',
      NSPhotoLibraryUsageDescription:
        'The app accesses your photos to let you share them for reviews and update profile picture.',
      NSPhotoLibraryAddUsageDescription:
        'The app accesses your photo library to save photos for reviews and update profile picture.',
      NSLocationWhenInUseUsageDescription:
        'Allow $(PRODUCT_NAME) to use your location to show the properties on the map that are near you.',
      LSApplicationQueriesSchemes: ['tel', 'mailto'],
    },
    config: {
      googleMapsApiKey: 'AIzaSyA8xcWnQtKcNfWlbST_AE0GOHAYs1mNC9w',
    },
  },
  experiments: {
    typedRoutes: true,
  },
  android: {
    // edgeToEdgeEnabled: true,
    softwareKeyboardLayoutMode: 'pan',
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#2E3C4B',
    },
    package: 'com.nextflytech.mydeer', //Env.PACKAGE,
    runtimeVersion: '0.0.5',
    versionCode: 5,
    permissions: [
      'CAMERA',
      'READ_EXTERNAL_STORAGE',
      'WRITE_EXTERNAL_STORAGE',
      'READ_MEDIA_IMAGES',
    ],
    config: {
      googleMaps: {
        apiKey: 'AIzaSyDU7HhmW9U0VikdE77EiHpVKK3ZhoV1EBU',
      },
    },
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },
  plugins: [
    'expo-secure-store',
    [
      'expo-font',
      {
        fonts: [
          './assets/fonts/Inter.ttf',
          './assets/fonts/AirbnbCereal_W_XBd.otf',
          './assets/fonts/AirbnbCereal_W_Md.otf',
          './assets/fonts/AirbnbCereal_W_Lt.otf',
          './assets/fonts/AirbnbCereal_W_Blk.otf',
          './assets/fonts/AirbnbCereal_W_Bk.otf',
          './assets/fonts/AirbnbCereal_W_Bd.otf',
        ],
      },
    ],
    'expo-localization',
    'expo-router',
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location to show the properties on the map that are near you.',
      },
    ],
    [
      'app-icon-badge',
      {
        enabled: Env.APP_ENV !== 'production',
        badges: [
          {
            text: Env.APP_ENV,
            type: 'banner',
            color: 'white',
          },
          {
            text: Env.VERSION.toString(),
            type: 'ribbon',
            color: 'white',
          },
        ],
      },
    ],
    [
      'expo-image-picker',
      {
        photosPermission:
          'The app accesses your photos to upload reviews and update profile picture.',
        cameraPermission:
          'The app accesses your camera to let you take photos for reviews and update profile picture.',
        microphonePermission:
          'The app accesses your microphone to let you record audio for posting reviews.',
      },
    ],
    [
      'expo-document-picker',
      {
        iCloudContainerEnvironment: 'Production',
      },
    ],
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    [
      'expo-video',
      {
        supportsBackgroundPlayback: false,
        supportsPictureInPicture: false,
      },
    ],
    [
      'expo-splash-screen',
      {
        backgroundColor: '#FFFFFF',
        image: './assets/splash-icon.png',
        // dark: {
        //   image: './assets/splash-icon.png',
        //   backgroundColor: '#000000',
        // },
        imageWidth: 250,
      },
    ],
    [
      'expo-build-properties',
      {
        android: {
          targetSdkVersion: 36,
          compileSdkVersion: 36,
        },
      },
    ],
    [
      'react-native-edge-to-edge',
      {
        android: {
          parentTheme: 'Light',
          enforceNavigationBarContrast: false,
        },
      },
    ],
    [
      "react-native-maps",
      {
        iosGoogleMapsApiKey: 'AIzaSyA8xcWnQtKcNfWlbST_AE0GOHAYs1mNC9w',
        androidGoogleMapsApiKey: 'AIzaSyCeCQ8HkaF1YRJP-31Z5688adQOr9Gvb28',
      },
    ],
    [
      'onesignal-expo-plugin',
      {
        mode: 'production',
      },
    ],
    // ['app-icon-badge', appIconBadgeConfig],
  ],
  extra: {
    ...ClientEnv,
    eas: {
      projectId: 'f5caf0ea-c9cf-43b5-98b0-64e644a869b1', //Env.EAS_PROJECT_ID,
    },
  },
});
