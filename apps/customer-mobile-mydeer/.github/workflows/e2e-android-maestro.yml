# 🔗 Links:
# Source file: https://github.com/obytes/react-native-template-obytes/blob/master/.github/workflows/e2e-android.yml
# End-to-end testing: https://starter.obytes.com/testing/end-to-end-testing/

# ✍️ Description:
# This workflow is used to run end-to-end tests on Android using Maestro Cloud.
# As a first step, it will generate a test APK using the Gradle build and  then trigger  Maestro Cloud to run the tests on the generated APK.
# This workflow will be triggered on pull requests (PRs) with the label "android-test-maestro-cloud" or can be manually triggered from the Actions tab.

# 🚨 GITHUB SECRETS REQUIRED:
# MAESTRO_CLOUD_API_KEY: API key for Maestro Cloud. You can get it from https://cloud.mobile.dev/ci-integration/github-actions#add-your-api-key-secret

name: E2E Tests Android (Maestro Cloud)

on:
  workflow_dispatch:
  pull_request:
    branches: [main, master]

jobs:
  generate-and-test-apk:
    if: github.event_name != 'pull_request' || ( github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'android-test-maestro-cloud'))
    name: Generate and Test Test APK (Maestro Cloud)
    runs-on: ubuntu-latest

    steps:
      - name: 📦 Checkout project repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📦 Setup Node + PNPM + install deps
        uses: ./.github/actions/setup-node-pnpm-install

      - name: 📦 Set Up JDK + Generate Test APK
        uses: ./.github/actions/setup-jdk-generate-apk
        with:
          APP_ENV: staging

      - name: Upload Test APK
        uses: actions/upload-artifact@v3
        with:
          name: test-apk
          path: ./android/app/build/outputs/apk/release/app-release.apk

      - name: 📱 Run E2E Tests with Maestro Cloud
        uses: mobile-dev-inc/action-maestro-cloud@v1.4.1
        with:
          api-key: ${{ secrets.MAESTRO_CLOUD_API_KEY }}
          app-file: ./android/app/build/outputs/apk/release/app-release.apk
          env: |
            APP_ID=com.obytes.staging
