name: <PERSON> stale issues and pull requests

on:
  schedule:
    - cron: '0 0 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/stale@v1
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'This issue is stale because it has been open 90 days with no activity. Remove stale label or comment or this will be closed in 14 days'
          stale-pr-message: 'This PR is stale because it has been open 90 days with no activity. Remove stale label or comment or this will be closed in 14 days'
          stale-issue-label: 'no-issue-activity'
          stale-pr-label: 'no-pr-activity'
          days-before-stale: 60
          days-before-close: 14
