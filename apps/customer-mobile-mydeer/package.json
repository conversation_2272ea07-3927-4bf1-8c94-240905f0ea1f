{"name": "mydeer", "version": "0.0.5", "private": true, "main": "expo-router/entry", "scripts": {"start": "cross-env EXPO_NO_DOTENV=1 expo start", "prebuild": "cross-env EXPO_NO_DOTENV=1 yarn expo prebuild", "android": "cross-env EXPO_NO_DOTENV=1 expo run:android", "ios": "cross-env EXPO_NO_DOTENV=1 expo run:ios", "web": "cross-env EXPO_NO_DOTENV=1 expo start --web", "xcode": "xed -b ios", "doctor": "npx expo-doctor@latest", "start:staging": "cross-env APP_ENV=staging pnpm run start", "prebuild:staging": "cross-env APP_ENV=staging pnpm run prebuild", "prebuild:development": "cross-env APP_ENV=development pnpm run prebuild", "android:staging": "cross-env APP_ENV=staging pnpm run android", "ios:staging": "cross-env APP_ENV=staging pnpm run ios", "start:production": "cross-env APP_ENV=production pnpm run start", "prebuild:production": "cross-env APP_ENV=production pnpm run prebuild", "android:production": "cross-env APP_ENV=production pnpm run android", "ios:production": "cross-env APP_ENV=production pnpm run ios", "build:development:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:staging:ios": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform ios", "build:staging:android": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform android ", "build:production:ios": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "prepare": "husky", "app-release": "cross-env SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft", "version": "pnpm run prebuild && git add .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc  --noemit", "lint:translations": "eslint ./src/translations/ --fix --ext .json  ", "check-all": "pnpm run lint && pnpm run type-check && pnpm run lint:translations "}, "dependencies": {"@azure/core-asynciterator-polyfill": "^1.0.2", "@cloudinary/url-gen": "^1.21.0", "@expo/metro-runtime": "~5.0.4", "@gorhom/bottom-sheet": "5.0.6", "@hookform/resolvers": "^3.9.1", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/slider": "4.5.6", "@react-native-picker/picker": "2.11.1", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5", "@tanstack/react-query-devtools": "^5", "@trpc/client": "^11.2.0", "@trpc/react-query": "^11.2.0", "@trpc/server": "^11.2.0", "@trpc/tanstack-react-query": "^11.2.0", "app-icon-badge": "^0.1.2", "cloudinary-react-native": "1.0.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "expo": "53.0.20", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.8", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-google-places-autocomplete": "^1.2.0", "expo-image": "~2.4.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-maps": "~0.11.0", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-video": "~2.2.2", "expo-web-browser": "~14.2.0", "i18next": "^23.16.8", "lodash.memoize": "^4.1.2", "meilisearch": "^0.49.0", "moti": "^0.29.0", "nativewind": "~4.1.23", "onesignal-expo-plugin": "^2.0.3", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^4.1.2", "react-google-autocomplete": "2.7.3", "react-hook-form": "^7.54.0", "react-i18next": "^15.1.4", "react-native": "0.79.5", "react-native-edge-to-edge": "1.6.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.48", "react-native-google-places-textinput": "^0.7.4", "react-native-keyboard-controller": "^1.14.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.23.12", "react-native-mmkv": "~3.2.0", "react-native-multi-slider": "^0.3.6", "react-native-otp-entry": "^1.8.1", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-restart": "0.0.27", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-web": "^0.20.0", "react-native-onesignal": "^5.2.12", "react-query-kit": "^3.3.1", "rn-eventsource-reborn": "^1.0.5", "rn-swiper-list": "^2.2.0", "string-width": "^7.2.0", "strip-ansi": "^7.1.0", "superjson": "2.2.1", "tailwind-variants": "^0.2.1", "web-streams-polyfill": "^4.0.0", "wrap-ansi": "^9.0.0", "zod": "^3.24.1", "zustand": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@dev-plugins/react-query": "^0.3.1", "@expo/config": "^11.0.0", "@repo/customer-api": "*", "@repo/customer-auth": "*", "@repo/database": "*", "@repo/partner-api": "*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react-native": "^12.9.0", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.14", "@types/lodash.memoize": "^4.1.9", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^9.12.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-compiler": "19.0.0-beta-a7bf2bd-20241110", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-testing-library": "^6.5.0", "eslint-plugin-unicorn": "^46.0.1", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "~53.0.9", "jest-junit": "^16.0.0", "lint-staged": "^15.2.11", "np": "^10.1.0", "prettier": "^3.3.3", "tailwindcss": "^3.4.15", "ts-jest": "^29.2.5", "typescript": "^5.8.2"}, "repository": {"type": "git", "url": "git+https://github.com/user/repo-name.git"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-restart"]}}, "install": {"exclude": ["eslint-config-expo"]}}, "osMetadata": {"initVersion": "7.0.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}