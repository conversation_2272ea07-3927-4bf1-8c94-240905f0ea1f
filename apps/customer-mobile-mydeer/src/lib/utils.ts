import { Linking } from 'react-native';
import { create, type StoreApi, type UseBoundStore } from 'zustand';
import { twMerge } from 'tailwind-merge';
import { type ClassValue, clsx } from 'clsx';
import { Preference } from '@/types';
import { persist, createJSONStorage } from 'zustand/middleware';
import { storageAdapter } from './storage';

export function openLinkInBrowser(url: string) {
  Linking.canOpenURL(url).then((canOpen) => canOpen && Linking.openURL(url));
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

type WithSelectors<S> = S extends { getState: () => infer T }
  ? S & { use: { [K in keyof T]: () => T[K] } }
  : never;

export const createSelectors = <S extends UseBoundStore<StoreApi<object>>>(
  _store: S
) => {
  let store = _store as WithSelectors<typeof _store>;
  store.use = {};
  for (let k of Object.keys(store.getState())) {
    (store.use as any)[k] = () => store((s) => s[k as keyof typeof s]);
  }

  return store;
};

type PreferenceStore = {
  userPreference: Preference;
  setPropertyFor: (propertyFor: 'SALE' | 'RENT') => void;
  setPropertyFilter: (data: Partial<Preference>) => void;
  resetPreference: () => void;
};

// Create the base store with initial values
const usePreferenceStoreBase = create<PreferenceStore>()(
  persist(
    (set) => ({
      userPreference: {
        propertyFor: 'SALE', // Default value
      } as Preference,

      setPropertyFor: (propertyFor: 'SALE' | 'RENT') =>
        set((state) => ({
          userPreference: {
            ...state.userPreference,
            propertyFor,
          },
        })),

      setPropertyFilter: (data: Partial<Preference>) =>
        set((state) => ({
          userPreference: {
            ...state.userPreference,
            ...data,
          },
        })),

      resetPreference: () =>
        set({
          userPreference: {
            propertyFor: 'SALE',
          } as Preference,
        }),
    }),
    {
      name: 'preference-storage',
      storage: createJSONStorage(() => storageAdapter),
    }
  )
);

// Add selectors for easier access to the store
export const usePreferenceStore = createSelectors(usePreferenceStoreBase);
