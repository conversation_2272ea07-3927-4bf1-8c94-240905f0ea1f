import z from 'zod';

const EnquiryType = ['AGENT', 'CUSTOMER'] as const;

const PropertyForEnum = ['SALE', 'RENT'] as const;

const FurnishingEnum = ['RAW', 'SEMIFURNISHED', 'FULLYFURNISHED'] as const;

const PropertyStateEnum = ['NEW', 'RESALE', 'UPCOMING'] as const;

const FacingEnum = [
  'NORTH',
  'SOUTH',
  'EAST',
  'WEST',
  'NORTH_EAST',
  'NORTH_WEST',
  'SOUTH_EAST',
  'SOUTH_WEST',
] as const;

const PossessionStateEnum = [
  'READY_TO_MOVE',
  'UNDER_6_MONTHS',
  'UNDER_1_YEAR',
  'UNDER_3_YEARS',
] as const;

const phoneRegex = new RegExp(/^[1-9][0-9]{9}/);

export const signInSchema = z.object({
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: 'Invalid phone number' })
    .min(10, 'Phone number must be at least 10 digits')
    .max(10, 'Phone number must be at most 10 digits'),
}) as z.ZodType<{ phoneNumber: string }>;



const pancardRegex = new RegExp(/([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}$/, 'i');

const gstRegex = new RegExp(
  /\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}/,
);

const adharcardNumber = new RegExp(/^[1-9]\d{11}$/);

export const signUpInputValidation = z.object({
  name: z.string().min(2, {
    message: 'Please provide a valid name with at least 2 characters.',
  }),
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: 'Please enter 10 digit phone number.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),

  adharcardNumber: z
    .string()
    .regex(adharcardNumber, {
      message: 'Please enter a valid Aadhar card number with exactly 12 digits.',
    })
    .min(12, { message: 'Aadhar card number must have exactly 12 digits.' })
    .optional()
    .transform(val => val === "" ? undefined : val),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message:
      'You must accept the terms of service and privacy policy to continue.',
  }),
});

export const landingSignUpInputValidation = z.object({
  name: z.string().min(2, { message: 'Name must be of 2 characters' }),
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: 'Invalid phone number' }),
  email: z.string().email(),
  agentCode: z.string().optional().nullable(),
  enquiryType: z.enum(EnquiryType),
});

export const companyDetailsSchema = z.object({
  name: z.string().min(5, { message: 'Name must be of 5 characters' }),
  email: z.string().email(),
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: 'Invalid phone number' }),
  address: z.string().min(1, { message: 'Select location' }),
  latitude: z.string(),
  longitude: z.string(),
  website: z.string(),
  fax: z
    .string()
    .length(11, { message: 'Fax number must be of 11 digits' })
    .optional(),
  about: z.string(),
});

export const propertyCategorySchema = z.string();
export const propertyForSchema = z.enum(PropertyForEnum, {
  required_error: 'Please select a value',
  invalid_type_error: 'Please select a value',
});

export const step1FormSchema = z.object({
  registeryFileKey: z.string(),
  propertyTitle: z
    .string()
    .min(5, { message: 'Title should have at least 5 characters' }),
  propertyFor: propertyForSchema,
  propertyCategoryId: propertyCategorySchema,
  propertyTypeId: z.string(),
  bedrooms: z.coerce.number({ message: 'Required' }).optional(),
  bathrooms: z.coerce.number({ message: 'Required' }).optional(),
  propertyPrice: z.coerce.number({ message: 'Enter an amount' }).nonnegative(),
  securityDeposit: z.coerce
    .number({ message: 'Enter an amount' })
    .nonnegative(),
  areaUnitId: z.string(),
  area: z.coerce.number({ message: 'Required' }),
  aboutProperty: z.string().optional(),
});

export const step2FormSchema = z.object({
  id: z.string().optional().nullable(),
  propertyAddress: z.string().min(1, { message: 'Required' }),
  propertyLatitude: z.number(),
  propertyLongitude: z.number(),
  propertyGooglePlaceId: z.string(),
  propertyAddressComponents: z.any(),
  propertyMarkersLatLng: z
    .record(z.any(), z.any(), {
      message: 'Please add the property markers on the map.',
    })
    .array(),
  propertyLocation: z.string().min(1, { message: 'Required' }),
  utilities: z.array(
    z.object({
      utility: z.string().min(1, { message: 'Required' }),
      distanceInKm: z.coerce
        .number({ message: 'Enter a distance in Km' })
        .gt(0),
    }),
  ),
});

export const step3FormSchema = z.object({
  id: z.string().optional().nullable(),
  societyOrLocalityName: z.string().min(1, {
    message: 'Society or locality name must be of atleast 1 character.',
  }),
  buildYear: z.coerce
    .number({ message: 'Enter a valid build year' })
    .min(1900)
    .max(2100),
  possessionState: z.enum(PossessionStateEnum, {
    message: 'Required',
    required_error: 'Please select a value',
    invalid_type_error: 'Please select a value',
  }),
  amenities: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
    }),
  ),
  facing: z.enum(FacingEnum, {
    required_error: 'Please select a value',
    invalid_type_error: 'Please select a value',
    message: 'Required',
  }),
  furnishing: z.enum(FurnishingEnum, {
    required_error: 'Please select a value',
    invalid_type_error: 'Please select a value',
    message: 'Required',
  }),
  totalFloors: z.coerce.number({ message: 'Enter a valid total floors' }),
  floorNumber: z.coerce.number({ message: 'Enter a valid floor number' }),
  carParking: z.coerce.number({ message: 'Enter a valid car parking' }),
  propertyState: z.enum(PropertyStateEnum, {
    required_error: 'Please select a value',
    invalid_type_error: 'Please select a value',
    message: 'Required',
  }),
});

export const createMediaSectionSchema = z.object({
  title: z.string(),
  propertyId: z.string(),
});

export const createMediaSchema = z.object({
  fileKey: z.string(),
  filePublicUrl: z.string().optional().nullable(),
  mediaSectionId: z.string(),
});

export const GiveUsFeedbackSchema = z.object({
  agentName: z
    .string()
    .min(1, { message: 'Agent name must be of atleast 1 character.' }),
  agentCompanyName: z
    .string()
    .min(1, { message: 'Agent company name must be of atleast 1 character.' }),
  rating: z.number().min(1).max(5, { message: 'Invalid rating.' }),
  whatDoYouLikeAboutTheAgent: z.string().array().optional(),
  doYouHaveAnythingElseToAddAboutTheAgent: z
    .string()
    .min(1, { message: 'Atleast 1 character is required.' }),
});

export const CustomerPropertiesFilterSchema = z.object({
  propertyFor: z.enum(PropertyForEnum).optional(),
  minPrice: z.number().optional(),
  maxPrice: z.number().optional(),
  beds: z.number().optional(),
  baths: z.number().optional(),
  homeTypes: z.string().array().optional(),
  areaUnitId: z.string(),
  minArea: z.number().optional(),
  maxArea: z.number().optional(),
  listingTypes: z.string().array().optional(),
  furnishType: z.enum(FurnishingEnum).optional(),
  propertyState: z.enum(PropertyStateEnum).optional(),
  possessionState: z.enum(PossessionStateEnum).optional(),
  amenities: z.string().array().optional(),
  facing: z.enum(FacingEnum).optional(),
  pointOfInterests: z.string().array().optional(),
  stayType: z.string().optional(),
  rentAmenities: z.string().optional(),
  propertyCategory: z.string().optional(),
  searchQuery: z.string().optional(),
  take: z.number(),
  page: z.number().optional(),
});

export const CustomerAgentsFilterSchema = z.object({
  take: z.number(),
  page: z.number().optional(),
});
