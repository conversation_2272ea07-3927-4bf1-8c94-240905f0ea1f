import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { createSelectors } from '../utils';
import { storageAdapter } from '../storage';

interface FirstTimeState {
  isFirstTime: boolean;
  setIsFirstTime: (value: boolean) => void;
}

const useFirstTimeStoreBase = create<FirstTimeState>()(
  persist(
    (set) => ({
      isFirstTime: true,
      setIsFirstTime: (value: boolean) => set({ isFirstTime: value }),
    }),
    {
      name: 'is-first-time-storage',
      storage: createJSONStorage(() => storageAdapter),
    }
  )
);

const useFirstTimeStore = createSelectors(useFirstTimeStoreBase);

export const useIsFirstTime = () => {
  const isFirstTime = useFirstTimeStore.use.isFirstTime();
  const setIsFirstTime = useFirstTimeStore.use.setIsFirstTime();

  return [isFirstTime, setIsFirstTime] as const;
};
