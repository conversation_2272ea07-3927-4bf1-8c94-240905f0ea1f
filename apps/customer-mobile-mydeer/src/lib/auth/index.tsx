import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { StateStorage } from 'zustand/middleware';
import { storage, storageAdapter } from '../storage';
import { createSelectors } from '../utils';
import { getToken, removeToken, setToken } from './utils';

interface AuthState {
  token: string | null;
  status: 'idle' | 'signOut' | 'signIn';
  signIn: (data: string) => void;
  signOut: () => void;
  hydrate: () => void;
}

const _useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      status: 'idle',
      token: null,

      signIn: (token) => {
        setToken(token);
        set({ status: 'signIn', token });
      },

      signOut: () => {
        removeToken();
        set({ status: 'signOut', token: null });
      },

      hydrate: () => {
        try {
          const userToken = getToken();
          if (userToken !== null) {
            get().signIn(userToken);
          } else {
            get().signOut();
          }
        } catch (e) {
          console.error(e);
          // catch error here
          // Maybe sign_out user!
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => storageAdapter),
    }
  )
);

export const useAuth = createSelectors(_useAuth);

export const signOut = () => _useAuth.getState().signOut();
export const signIn = (token: string) => _useAuth.getState().signIn(token);
export const hydrateAuth = () => _useAuth.getState().hydrate();
