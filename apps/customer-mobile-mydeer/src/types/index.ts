import { Prisma } from '@repo/database';
import { CustomerPropertiesFilterSchema } from '@repo/validators';
import { z } from 'zod';

export type Preference = Partial<
  Omit<z.infer<typeof CustomerPropertiesFilterSchema>, 'areaUnitId' | 'take'>
>;
export interface AgentProps {
  name: string;
  id: string;
  createdAt: Date;
  verifiedAgent: boolean | null;
  experience: string | null;
  propertiesSold: number | null;
  rating: string | null;
  filePublicUrl: string | null;
  cloudinaryProfileImageUrl: string | null;
  coustomerConnections?: { id: string }[];
  operationArea?: any[];
  customerRatingsToAgents?: any[];
  properties?: any[];
  company?: any;
  userLocation?: string;
  bio?: string;
  languages?: any[];
  reviews?: number;
}

export type PropertyDetailsProps = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      include: {
        company: true;
        companyDetails: true;
      };
    };
    comments: {
      include: {
        user: true;
      };
    };
    customerFavourites: true;
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
            cloudinaryUrl: true;
          };
        };
      };
    };
    PropertyCategory: true;
  };
}> & {
  propertyMarkersLatLng?: Array<{ lat: number; lng: number }>;
};

export type CustomerReviewData = Prisma.CustomerRatingsToAgentsGetPayload<{
  select: {
    id: true;
    userStarsCount: true;
    userRatingMessage: true;
    fileKey: true;
    filePublicUrl: true;
    cloudinaryUrl: true;
    cloudinaryPublicId: true;
    ratedTo: {
      select: {
        id: true;
        name: true;
        userLocation: true;
        filePublicUrl: true;
        company: {
          select: {
            id: true;
            companyName: true;
          };
        };
      };
    };
  };
}>;

export type IProperty = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      select: {
        id: true;
        company: true;
        companyDetails: true;
        name: true;
        rating: true;
        reviews: true;
      };
    };
    customerFavourites: true;
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
            cloudinaryUrl: true;
          };
        };
      };
    };
  };
}>;

export type HistoryComponentProps =
  Prisma.CustomerPropertyViewingHistoryGetPayload<{
    include: {
      property: {
        include: {
          areaUnit: true;
          utilities: true;
          amenities: true;
          user: {
            include: {
              company: true;
              companyDetails: true;
            };
          };
          comments: {
            include: {
              user: true;
            };
          };
          customerFavourites: true;
          mediaSections: {
            include: {
              media: {
                select: {
                  id: true;
                  fileKey: true;
                  filePublicUrl: true;
                };
              };
            };
          };
        };
      };
    };
  }>;
