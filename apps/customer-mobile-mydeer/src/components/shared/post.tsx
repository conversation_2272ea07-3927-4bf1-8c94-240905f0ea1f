import { Pressable, StyleSheet, Text, TextInput, View } from 'react-native';
import React, { useState } from 'react';
import { Image } from 'expo-image';
import UserPic from './user-pic';
import { formatTime } from '@/utils/format-terms';
import { router } from 'expo-router';
import { showMessage } from 'react-native-flash-message';
import { api } from '@/utils/api';
import VideoComponent from './video-component';

export const CommentBottomStrip = React.memo(
  ({
    postId,
    likedId,
    totalComments,
    totalLikes,
    refetch,
  }: {
    postId: string;
    likedId: string;
    totalComments: number;
    totalLikes: number;
    refetch?: () => void;
  }) => {
    const [comment, setComment] = useState('');
    const { mutate: likePost } = api.social.likePost.useMutation();
    const { mutate: newComment } = api.social.newComment.useMutation();
    const [liked, setLiked] = useState(!!likedId);

    const handleLikeClick = () => {
      likePost(
        { postId: postId },
        {
          onSuccess: (resp) => {
            showMessage({
              message: resp.message,
              type: 'success',
            });
            setLiked(resp.isLiked);
          },
          onError: (opts) => {
            showMessage({
              message: opts.message,
              type: 'danger',
            });
          },
        }
      );
    };

    const handleComment = () => {
      if (comment.trim().length === 0) {
        showMessage({
          message: 'Please enter something.',
          type: 'danger',
        });
        return;
      }
      newComment(
        { comment: comment, postId: postId },
        {
          onSuccess: () => {
            setComment('');
            refetch && refetch();
            // void trcpUtils.social.invalidate();
          },
          onError: (opts) => {
            showMessage({
              message: opts.message,
              type: 'danger',
            });
          },
        }
      );
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleComment();
      }
    };

    return (
      <>
        <View className="w-full flex-row items-center justify-between">
          <Pressable
            className="flex-row items-center"
            onPress={handleLikeClick}
          >
            <Image
              source={
                liked
                  ? require('../../../assets/icons/like1.png')
                  : require('../../../assets/icons/like2.png')
              }
              className="mr-2 h-5 w-5"
            />
            <Text
              className={`font-airbnb_bk font-normal text-sm ${
                // item.type === 'display'
                //   ? 'text-text-500'
                liked ? 'text-primary-main700' : 'text-text-500'
              }`}
            >
              Like
            </Text>
          </Pressable>
          <View
            className="flex-row items-center"
            // onPress={item.type === 'action' ? item.onPress : undefined}
          >
            <Text className="font-airbnb_bk font-normal text-sm text-primary-main700">
              {totalLikes} likes
            </Text>
          </View>
          <View
            className="flex-row items-center"
            // onPress={item.type === 'action' ? item.onPress : undefined}
          >
            <Text className="font-airbnb_bk font-normal text-sm text-text-500">
              {totalComments} comments
            </Text>
          </View>
        </View>
        <View
          className={`mt-2.5 px-3 flex-row items-center justify-between border border-text-100 rounded-lg`}
        >
          <TextInput
            className="p-3 flex-1"
            placeholder="Add a comment"
            value={comment}
            onChangeText={setComment}
          />
          <Pressable onPress={handleComment}>
            <Image
              source={require('../../../assets/icons/send.png')}
              className="h-4 w-4"
              tintColor={'#5F2800'}
            />
          </Pressable>
        </View>
      </>
    );
  }
);

const Post = ({ post }: { post: any }) => {
  return (
    <View>
      <Pressable
        className={`mb-3.5 p-3 border border-text-100 rounded-xl`}
        // onPress={() => router.push(`/home-top-tabs/${post.id}`)}
      >
        {/* Post heading */}
        <View className="mb-2 flex-row items-center">
          <View className="mr-3 rounded-full self-start">
            <UserPic
              picUrl={
                post.user?.cloudinaryProfileImageUrl || post.user?.filePublicUrl
              }
              color="#F04D24"
              size={54}
              className="mr-2.5 aspect-square h-16 rounded-full"
            />
            {/*<Image*/}
            {/*  source={require('../../../../assets/images/avatar.png')}*/}
            {/*  className="h-10 w-10"*/}
            {/*/>*/}
          </View>
          <View>
            <Text className="mb-[0.25px] font-bold font-airbnb_bd text-sm text-text-600">
              {post.user?.name}
            </Text>
            <Text className="mb-0.5 font-normal font-airbnb_bk text-[10px] text-primary-800">
              {post.user?.company?.companyName}
            </Text>
            <Text className="font-normal font-airbnb_bk text-[10px] text-text-500">
              {formatTime(post.createdAt)}
            </Text>
          </View>
        </View>

        {/* post */}
        <View className="items-center justify-center">
          <Text className="mb-3 font-airbnb_bk font-normal text-sm text-text-500 w-full">
            {post.content}
          </Text>
          {post.media[0] &&
          post.media[0].mediaType === 'IMAGE' &&
          post.media[0].cloudinaryUrl ? (
            <Image
              source={{ uri: post.media[0].cloudinaryUrl }}
              className="mb-4 h-[206px] w-full"
            />
          ) : (
            post.media[0] &&
            post.media[0].mediaType === 'IMAGE' &&
            post.media[0].filePublicUrl && (
              <Image
                source={{ uri: post.media[0].filePublicUrl }}
                className="mb-4 h-[206px] w-full"
              />
            )
          )}
          {post.media[0] &&
          post.media[0].mediaType === 'VIDEO' &&
          post.media[0].cloudinaryUrl ? (
            <VideoComponent
              videoSource={post.media[0].cloudinaryUrl}
              // className="mb-4 h-[206px] w-full"
            />
          ) : (
            post.media[0] &&
            post.media[0].mediaType === 'VIDEO' &&
            post.media[0].filePublicUrl && (
              <VideoComponent
                videoSource={post.media[0].filePublicUrl}
                // className="mb-4 h-[206px] w-full"
              />
            )
          )}
          <CommentBottomStrip
            postId={post.id}
            likedId={post.likes[0]?.id}
            totalComments={post.totalComments}
            totalLikes={post.totalLikes}
          />
        </View>
      </Pressable>
    </View>
  );
};

export default Post;

const styles = StyleSheet.create({});
