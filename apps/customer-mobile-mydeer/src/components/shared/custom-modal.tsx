import { View, Modal as RNModal, StyleSheet } from 'react-native';
import React from 'react';

interface CustomModalProps {
  visible: boolean;
  onClose: () => void;
  Content: React.ReactNode;
}

export const CustomModal = ({
  visible,
  onClose,
  Content,
}: CustomModalProps) => {
  return (
    <View className="h-fit">
      <RNModal
        visible={visible}
        transparent={true}
        onRequestClose={onClose}
        animationType="fade"
      >
        <View className="flex-1 items-center justify-center bg-black/50">
          <View
            style={styles.modalContent}
            className="bg-white px-5 py-5 rounded-xl"
          >
            {Content}
          </View>
        </View>
      </RNModal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContent: {
    width: '85%',
  },
});
