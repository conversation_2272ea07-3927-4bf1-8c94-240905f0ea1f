import React from 'react';
import { View, Text, Image } from 'react-native';

type BaseInfo = {
  heading: string;
  description: string;
  onEdit?: () => void;
};

type ExtendedInfo = {
  companyDetails: {
    icon?: any;
    title: string;
    value: string;
  }[];
};

type Info<T extends boolean> = T extends true
  ? BaseInfo & ExtendedInfo
  : BaseInfo;

const AgentInfoCard = <T extends boolean>(props: Info<T>) => {
  return (
    <View className="p-4 bg-white rounded-xl border-[1.5px] border-secondary-100">
      <View>
        <Text className="mb-3 text-lg font-airbnb_bd font-bold text-primary-750">
          {props.heading}
        </Text>
        <Text className="text-base font-normal font-airbnb_bk text-text-500">
          {props.description}
        </Text>
      </View>

      {/* Company Details */}
      {'companyDetails' in props && props.companyDetails && (
        <View>
          {props.companyDetails.map((detail, index) => (
            <View
              key={index}
              className="mb-3.5 flex-row items-center justify-between rounded-[4px] bg-primary-100 px-5 py-2"
            >
              <View className="flex-row items-center">
                {detail.icon && (
                  <Image source={detail.icon} className="mr-2 h-4 w-4" />
                )}
                <Text className="font-airbnb_bk text-xs font-normal text-primary-750">
                  {detail.title}
                </Text>
              </View>

              <View style={{ maxWidth: '60%' }}>
                <Text
                  numberOfLines={2}
                  ellipsizeMode="tail"
                  className="font-airbnb_md text-xs font-medium text-text-main700 text-right"
                >
                  {detail.value}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

export default AgentInfoCard;
