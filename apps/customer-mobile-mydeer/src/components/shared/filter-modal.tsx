import React, { useRef, useCallback, useState } from 'react';
import {
  View,
  Pressable,
  Modal,
  ScrollView,
  StyleSheet,
  Platform,
} from 'react-native';
import { Image } from 'expo-image';
import { Preference as PreferencesType } from '@/types';
import { FilterOptions } from './filter-options';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface FilterModalProps {
  onApplyFilters?: (data: PreferencesType) => void;
  onClose?: () => void;
}

export const FilterModal: React.FC<FilterModalProps> = ({
  onApplyFilters,
  onClose,
}) => {
  const insets = useSafeAreaInsets();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleOpenModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalVisible(false);
    if (onClose) {
      onClose();
    }
  }, [onClose]);


  return (
    <>
      <View>
        <Pressable
          className="mt-0.5 p-2 bg-primary-700 rounded-lg justify-items-center"
          onPress={handleOpenModal}
        >
          <Image
            source={require('@assets/icons/exploreFilterButton.png')}
            className="h-6 w-6"
          />
        </Pressable>
      </View>

      <Modal
        animationType="slide"
        visible={isModalVisible}
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalContainer}>
          <View
            className="px-5  w-full flex-row justify-end"
            style={{ paddingTop: Platform.OS === 'ios' ? insets.top : 0 }}
          >
            <Pressable onPress={handleCloseModal} className="px-5 py-3">
              <Image
                source={require('@assets/icons/cancel.png')}
                contentFit="contain"
                className="h-7 w-7"
                tintColor={'#252525'}
              />
            </Pressable>
          </View>

          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{ flex: 1, flexGrow: 1 }}
            contentContainerStyle={{ paddingBottom: 40 }}
          >
            <FilterOptions onPress={() => handleCloseModal()} />
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
});
