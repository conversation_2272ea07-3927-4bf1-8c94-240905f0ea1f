import React, { useState, useEffect } from 'react';
import { FieldError } from 'react-hook-form';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Platform,
  Pressable,
  Text,
  Modal,
  SafeAreaView,
  View,
  TextInput,
} from 'react-native';
import { Button } from '../ui';
import { Env } from '@env';
import { showMessage } from 'react-native-flash-message';
import { iosMapApiKey, androidMapApiKey } from '@/utils/google-api-keys';
import GooglePlacesTextInput from 'react-native-google-places-textinput';

interface InputGoogleAutoCompleteProps {
  onLatLngChange?: (lat: string, lng: string) => void;
  placeholder?: string;
  onAddressComponentsChange?: (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => void;
  children: React.ReactNode;
  value?: string;
  northMaxLat?: number | null;
  southMaxLat?: number | null;
  westMaxLng?: number | null;
  eastMaxLng?: number | null;
  error?: FieldError;
  getAddressValue?: (text: string) => void;
}

const TextInputGoogleAutoComplete = ({
  onLatLngChange,
  onAddressComponentsChange,
  error,
  value,
  placeholder,
  northMaxLat,
  southMaxLat,
  westMaxLng,
  eastMaxLng,
  children,
  ...props
}: InputGoogleAutoCompleteProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [addressComponents, setAddressComponents] = useState({
    state: '',
    country: '',
    googleAutocompletePlaceId: '',
    pincode: '',
  });
  const [showSuggestion, setShowSuggestion] = useState(true);
  console.log('query recieved ', query);

  if (northMaxLat && southMaxLat && westMaxLng && eastMaxLng) {
    console.log(
      'south, west, north, east',
      southMaxLat,
      westMaxLng,
      northMaxLat,
      eastMaxLng
    );
  }

  return (
    <>
      <Pressable onPress={() => setModalVisible(true)}>
        {children ? (
          children
        ) : (
          <Text>{addressComponents.state || 'Select Address'}</Text>
        )}
      </Pressable>

      <Modal
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
        animationType="slide"
        transparent={false}
        supportedOrientations={['portrait', 'landscape']}
      >
        <LinearGradient
          colors={['#FFFCFB', '#FACABD']}
          start={{ x: 0, y: 0.69 }}
          end={{ x: 0, y: 1 }}
          style={{ flex: 1 }}
        >
          <SafeAreaView className="flex-1">
            <View className="flex-1 m-5">
              <GooglePlacesTextInput
                apiKey={Platform.OS === 'ios' ? iosMapApiKey : androidMapApiKey}
                fetchDetails={true}
                includedRegionCodes={['in']}
                detailsFields={[
                  'addressComponents',
                  'formattedAddress',
                  'formatted_address',
                  'id',
                  'location',
                ]}
                onPlaceSelect={(place) => {
                  const details = place.details;
                  console.log('details', details);
                  if (details && details.location) {
                    const { latitude, longitude } = details.location;
                    console.log(latitude, longitude);
                    onAddressComponentsChange &&
                      onAddressComponentsChange({
                        lat: latitude.toString(),
                        lng: longitude.toString(),
                        place_id: details.id,
                        address_components: details.addressComponents,
                        address: details.formattedAddress,
                      });
                    setModalVisible(false);
                  } else {
                    showMessage({
                      message: 'No location found',
                      type: 'danger',
                    });
                  }
                }}
                showClearButton={false}
              />
            </View>
            <View style={{ paddingHorizontal: 15 }}>
              <Button label="Cancel" onPress={() => setModalVisible(false)} />
            </View>
          </SafeAreaView>
        </LinearGradient>
      </Modal>
    </>
  );
};

export default TextInputGoogleAutoComplete;
