import { Image as NImage } from 'expo-image';
import React, { useState } from 'react';
import { Pressable, TextInput, TextInputProps } from 'react-native';

interface TextInputComponentProps extends TextInputProps {
  background: string;
  placeholdercolor?: string;
  searchiconimagecolor?: string;
  textinputcolor?: string;
  bordercolor?: string;
  placeholdertext?: string;
  OnPress?: () => void;
  onTextChange?: (text: string) => void;
}

export default function TextInputComponent({
  background,
  placeholdercolor,
  searchiconimagecolor,
  textinputcolor,
  bordercolor,
  placeholdertext,
  OnPress,
  onTextChange,
  ...props
}: TextInputComponentProps) {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const handleChangeText = (text: string) => {
    if (onTextChange) {
      onTextChange(text);
    }
  };

  return (
    <Pressable
      className={`
        py-3 
        px-4 
        flex-row 
        items-center 
        rounded-2xl 
        border 
        ${background} 
        ${isFocused ? bordercolor : 'border-secondary-200'}
        z-10
      `}
      onPress={OnPress}
      disabled={isFocused}
    >
      <NImage
        source={require('@assets/icons/search.png')}
        className="mr-3 h-4 w-4 self-center"
        contentFit="contain"
        tintColor={searchiconimagecolor}
      />
      <TextInput
        className={`
          flex-1 
          text-base 
          font-normal 
          ${textinputcolor}
          z-20
          p-0
          leading-5
          android:py-0
          ios:py-0
        `}
        placeholder={placeholdertext}
        placeholderTextColor={placeholdercolor}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onChangeText={handleChangeText}
        textAlignVertical="center"
        {...props}
      />
    </Pressable>
  );
}
