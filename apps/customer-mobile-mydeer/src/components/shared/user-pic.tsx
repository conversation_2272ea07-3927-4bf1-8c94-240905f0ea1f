import React from 'react';
import { Image } from 'expo-image';
import FontAwesome from '@expo/vector-icons/FontAwesome';

const UserPic = ({
  picUrl,
  size,
  color,
  className,
}: {
  picUrl?: string;
  size?: number;
  color?: string;
  className: string;
}) => {
  return picUrl ? (
    <Image source={{ uri: picUrl }} className={className} />
  ) : (
    <FontAwesome
      name="user-circle"
      size={size ?? 24}
      color={color ?? 'red-500'}
      className={className}
    />
  );
};

export default UserPic;
