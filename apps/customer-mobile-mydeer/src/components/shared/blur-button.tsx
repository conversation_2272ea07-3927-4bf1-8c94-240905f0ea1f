import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import React from 'react';
import { Platform, Pressable, View } from 'react-native';

import { Button } from '../ui';

interface BlurButtonProps {
  onPrevious: () => void;
  onNext: () => void;
  label: string;
  loading?: boolean;
}

export const ButtonRow: React.FC<{
  onPrevious: () => void;
  onNext: () => void;
  label: string;
}> = ({ onPrevious, onNext, label }) => (
  <View className="flex-row items-center justify-between">
    <Pressable
      className="rounded-xl bg-secondary-100 px-6 py-3.5"
      onPress={onPrevious}
    >
      <NImage
        source={require('../../assets/icons/back.png')}
        contentFit="contain"
        className="h-7 w-7"
        tintColor={'#451F0A'}
      />
    </Pressable>
    <Button label={label} onPress={onNext} className="w-1/2" />
  </View>
);

export const BlurButtonWithBack: React.FC<BlurButtonProps> = ({
  onPrevious,
  onNext,
  label,
}) => {
  return (
    <View>
      {Platform.OS === 'ios' ? (
        <BlurView
          intensity={15}
          tint="light"
          blurReductionFactor={4}
          experimentalBlurMethod="dimezisBlurView"
          className="absolute bottom-0 mb-10 w-full flex-col justify-end bg-primary-50/30 px-5 py-3 blur-lg"
        >
          <ButtonRow onPrevious={onPrevious} onNext={onNext} label={label} />
        </BlurView>
      ) : Platform.OS === 'android' ? (
        <View className="absolute bottom-0 mb-10 w-full flex-col justify-end bg-primary-50/30 px-5 py-3 blur-lg">
          <ButtonRow onPrevious={onPrevious} onNext={onNext} label={label} />
        </View>
      ) : null}
    </View>
  );
};

export const BlurButton: React.FC<Omit<BlurButtonProps, 'onPrevious'>> = ({
  onNext,
  loading,
  label,
}) => {
  return (
    <View>
      {Platform.OS === 'ios' ? (
        <BlurView
          intensity={15}
          tint="light"
          blurReductionFactor={4}
          experimentalBlurMethod="dimezisBlurView"
          className="absolute bottom-0 mb-10 w-full flex-1 flex-col justify-end px-5 py-3"
        >
          <Button label={label} loading={loading} onPress={onNext} />
        </BlurView>
      ) : Platform.OS === 'android' ? (
        <View className="absolute bottom-0 mb-10 w-full flex-1 flex-col justify-end px-5 py-3">
          <Button label={label} loading={loading} onPress={onNext} />
        </View>
      ) : null}
    </View>
  );
};
