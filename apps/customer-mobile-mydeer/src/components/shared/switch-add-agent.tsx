import React, { useState } from 'react';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { Image } from 'expo-image';
import { AgentProps } from '@/types';
import { AgentCard } from '../routes/agents-component/agent-card';
import { CustomModal } from './custom-modal';
import AntDesign from '@expo/vector-icons/AntDesign';
import { api } from '@/utils/api';
import { Button } from '../ui';
import { showMessage } from 'react-native-flash-message';

interface SwitchAddAgentProps {
  isVisible: boolean;
  currentAgent?: AgentProps;
  newAgent: AgentProps;
  propertyId?: string;
  onClose: () => void;
  onSuccess?: () => void;
}

export const SwitchAddAgent: React.FC<SwitchAddAgentProps> = ({
  isVisible,
  currentAgent,
  newAgent,
  propertyId,
  onClose,
  onSuccess,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // checking if the user connected by the agent who listed this property
  //   const customerDetail = api.user.getProfile.useQuery();
  //   const connectedAgentId = customerDetail.data?.connections[0]?.agentId ?? '';
  const connectedAgentId = currentAgent?.id;
  const agentId = newAgent.id;
  const isConnectedAgent = agentId === connectedAgentId;

  // Get connection ID if current agent exists
  const connectionId = currentAgent?.coustomerConnections?.[0]?.id;

  // Mutations
  const sendConnectionRequestMutation =
    api.chat.sendNewConnectionRequest.useMutation();
  const updateConnectionRequestMutation =
    api.chat.updateConnectionRequest.useMutation();

  const handleSendConnectionRequest = () => {
    setIsLoading(true);
    sendConnectionRequestMutation.mutate(
      {
        agentId: newAgent.id,
        propertyId: propertyId,
      },
      {
        onSuccess: (opts) => {
          showMessage({
            message: opts.message,
            type: opts.warning ? 'warning' : 'success',
          });
          setIsLoading(false);
          if (opts.warning) {
            return;
          }
          onSuccess?.();
          onClose();
        },
        onError: () => {
          showMessage({
            message: 'Error in sending connection request',
            type: 'danger',
          });
          setIsLoading(false);
        },
      }
    );
  };

  const handleUpdateConnectionRequest = () => {
    if (!connectionId) return;

    setIsLoading(true);
    updateConnectionRequestMutation.mutate(
      {
        agentId: newAgent.id,
        propertyId: propertyId,
        connectionId,
      },
      {
        onSuccess: (_opts) => {
          showMessage({
            message: 'Connection request sent',
            type: 'success',
          });
          setIsLoading(false);
          onSuccess?.();
          onClose();
        },
        onError: () => {
          showMessage({
            message: 'Error in sending connection request',
            type: 'danger',
          });
          setIsLoading(false);
        },
      }
    );
  };

  return (
    <CustomModal
      visible={isVisible}
      onClose={onClose}
      Content={
        currentAgent ? (
          <View>
            <Pressable
              className="self-end items-center justify-center rounded-full p-2"
              onPress={onClose}
            >
              <Image
                source={require('@assets/icons/cancel.png')}
                className="h-5 w-5"
              />
            </Pressable>
            <View className="items-center justify-center mb-4">
              <Text className="text-center font-airbnb_xbd text-2xl font-extrabold text-primary-750">
                Switch Your Current Agent
              </Text>

              <Text className="text-center text-base font-airbnb_bk font-normal text-text-500 mt-2">
                Do you want to
                <Text className="font-airbnb_bd font-bold"> Replace </Text>
                your current Residential Agent with this Agent?
              </Text>
            </View>

            <View className="self-start bg-secondary-100 rounded-md px-3 py-1.5 mb-3">
              <Text className="font-airbnb_md text-sm font-medium text-text-600">
                Current Agent
              </Text>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 24,
              }}
            >
              <View style={{ flex: 1, marginRight: 8 }}>
                <AgentCard agent={currentAgent} agentPressed={onClose} />
              </View>
              <AntDesign
                name="arrowright"
                size={20}
                color="#080808"
                style={{ marginHorizontal: 8 }}
              />
              <View style={{ flex: 1, marginLeft: 8 }}>
                <AgentCard agent={newAgent} agentPressed={onClose} />
              </View>
            </ScrollView>

            <View className="px-2 flex-row items-center gap-3">
              <Button
                onPress={onClose}
                fullWidth={false}
                className="flex-2"
                variant="outline"
                label="No, I don't"
                disabled={isConnectedAgent}
              />
              <Button
                onPress={handleUpdateConnectionRequest}
                fullWidth={false}
                className="flex-1"
                loading={isLoading}
                label="Yes, Switch"
                disabled={isConnectedAgent}
              />
            </View>
          </View>
        ) : (
          <View>
            <Pressable
              className="self-end items-center justify-center rounded-full p-2"
              onPress={onClose}
            >
              <Image
                source={require('@assets/icons/cancel.png')}
                className="h-5 w-5"
              />
            </Pressable>
            <View className="items-center justify-center mb-4">
              <Text className="text-center font-airbnb_xbd text-2xl font-extrabold text-primary-750">
                Add Your Representative
              </Text>

              <Text className="text-center text-base font-airbnb_bk font-normal text-text-500 mt-2">
                Do you want to Add this Agent as your Representative Residential
                Agent?
              </Text>
            </View>

            <View className="items-center">
              <AgentCard agent={newAgent} agentPressed={onClose} />
            </View>

            <View className="px-2 flex-row items-center gap-3">
              <Button
                onPress={onClose}
                fullWidth={false}
                className="flex-2"
                variant="outline"
                label="No, I don't"
                disabled={isConnectedAgent}
              />
              <Button
                onPress={handleSendConnectionRequest}
                fullWidth={false}
                className="flex-1"
                loading={isLoading}
                label="Yes, Add"
              />
            </View>
          </View>
        )
      }
    />
  );
};
