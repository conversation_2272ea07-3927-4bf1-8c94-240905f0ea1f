import React, { useEffect } from 'react';
import { View, ScrollView, Pressable, Text } from 'react-native';
import { zodResolver } from '@hookform/resolvers/zod';
import { SingleSelectFilterComponent } from '@/components/routes/home-screens-components/single-select-filter';
import { MultiSelectFilterComponent } from '@/components/routes/home-screens-components/multi-select-filter';
import { MultiSliderr } from '../routes/home-screens-components/multi-slider';
import { Preference } from '@/types';
import { useForm } from 'react-hook-form';
import { CustomerPropertiesFilterSchema } from '@/utils/form-validators';
import { api } from '@/utils/api';
import { Button } from '../ui';
import { usePreferenceStore } from '@/lib/utils';

type FilterOptionsProps = {
  onPress: () => void;
  setOnPressHandler?: (handler: () => void) => void;
  step3?: boolean;
  initialValues?: Partial<Preference>;
};

export const FilterOptions: React.FC<FilterOptionsProps> = ({
  onPress,
  setOnPressHandler,
  step3,
  initialValues = {},
}) => {
  const { userPreference, setPropertyFilter } = usePreferenceStore();

  const {
    setValue,
    handleSubmit,
    trigger,
    formState: { errors },
  } = useForm<Preference>({
    resolver: zodResolver(
      CustomerPropertiesFilterSchema.omit({
        areaUnitId: true,
        take: true,
      })
    ),
    defaultValues: { ...userPreference, ...initialValues },
  });

  const onSubmit = async (data: Preference) => {
    const isValid = await trigger();
    if (!isValid) return;

    // Preserve the original propertyFor value
    setPropertyFilter(data);

    if (onPress) onPress();
  };

  useEffect(() => {
    if (setOnPressHandler) {
      setOnPressHandler(handleSubmit(onSubmit));
    }
  }, [setOnPressHandler, handleSubmit]);

  const { data: amenitiesData = [] } = api.property.getAmenities.useQuery();
  const { data: propertyCategories = [] } =
    api.property.getPropertyCategories.useQuery();

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        // contentContainerStyle={{ paddingBottom: 64 }}
      >
        <View className="mt-2 px-5 gap-4">
          <SingleSelectFilterComponent
            options={
              propertyCategories.map(({ id, name }) => ({
                id,
                value: name,
              })) || []
            }
            label={'Property Category'}
            onPress={(v) => setValue('propertyCategory', v?.value)}
          />
          <SingleSelectFilterComponent
            options={[
              { id: 'SEMIFURNISHED', value: 'Semi Furnished' },
              { id: 'FULLYFURNISHED', value: 'Fully Furnished' },
              { id: 'RAW', value: 'Raw' },
            ]}
            label={'Property Furnishing'}
            onPress={(v) =>
              setValue(
                'furnishType',
                v?.id as 'SEMIFURNISHED' | 'FULLYFURNISHED' | 'RAW' | undefined
              )
            }
          />
          <SingleSelectFilterComponent
            options={[
              { id: 'NEW', value: 'New Project' },
              { id: 'RESALE', value: 'Resale Property' },
              { id: 'UPCOMING', value: 'Upcoming Property' },
            ]}
            label={'Sales Type'}
            onPress={(v) =>
              setValue(
                'propertyState',
                v?.id as 'NEW' | 'RESALE' | 'UPCOMING' | undefined
              )
            }
          />
          <SingleSelectFilterComponent
            options={[
              { id: 'READY_TO_MOVE', value: 'Ready to move' },
              { id: 'UNDER_6_MONTHS', value: 'Under 6 month' },
              { id: 'UNDER_1_YEAR', value: 'Under 1 year' },
              { id: 'UNDER_3_YEARS', value: 'Below 3 years' },
            ]}
            label={'Possession State'}
            onPress={(v) =>
              setValue(
                'possessionState',
                v?.id as
                  | 'READY_TO_MOVE'
                  | 'UNDER_6_MONTHS'
                  | 'UNDER_1_YEAR'
                  | 'UNDER_3_YEARS'
                  | undefined
              )
            }
          />
          <MultiSliderr
            label={'Price Range'}
            minimumValue={10000}
            maximumValue={100000}
            onPress={(v) => {
              setValue('minPrice', v.min);
              setValue('maxPrice', v.max);
            }}
          />
          <MultiSliderr
            label={'Area Unit Range'}
            minimumValue={10000}
            maximumValue={100000}
            onPress={(v) => {
              setValue('minArea', v.min);
              setValue('maxArea', v.max);
            }}
          />
          <MultiSelectFilterComponent
            FilterOptions={
              amenitiesData.map(({ id, name }) => ({
                id,
                value: name,
              })) || []
            }
            label={'Amenities'}
            onPress={(v) =>
              setValue(
                'amenities',
                v?.map((val) => val.value)
              )
            }
          />
          <SingleSelectFilterComponent
            options={[
              { id: 'WEST', value: 'West Facing' },
              { id: 'NORTH', value: 'North Facing' },
              { id: 'SOUTH', value: 'South Facing' },
              { id: 'NORTH_EAST', value: 'North-East Facing' },
              { id: 'SOUTH_EAST', value: 'South-East Facing' },
              { id: 'NORTH_WEST', value: 'North-West Facing' },
              { id: 'SOUTH_WEST', value: 'South-West Facing' },
            ]}
            label={'Property Facing'}
            onPress={(v) =>
              setValue(
                'facing',
                v?.id as
                  | 'NORTH'
                  | 'SOUTH'
                  | 'EAST'
                  | 'WEST'
                  | 'NORTH_EAST'
                  | 'NORTH_WEST'
                  | 'SOUTH_EAST'
                  | 'SOUTH_WEST'
                  | undefined
              )
            }
          />
          {!step3 && (
            <View className="mt-6 mb-10">
              <Button label="Apply Filter" onPress={handleSubmit(onSubmit)} />
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};
