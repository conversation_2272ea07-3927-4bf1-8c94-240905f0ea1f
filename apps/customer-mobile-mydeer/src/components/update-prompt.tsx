import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Alert,
  Platform,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { getStoreUrl, VERSION_CONFIG } from '@/config/version-config';

interface UpdatePromptProps {
  currentVersion: string;
  minimumVersion: string;
  onDismiss?: () => void;
}

const UpdatePrompt: React.FC<UpdatePromptProps> = ({
  currentVersion,
  minimumVersion,
  onDismiss,
}) => {
  const { t } = useTranslation();

  const handleUpdate = () => {
    const storeUrl = getStoreUrl();

    Linking.canOpenURL(storeUrl)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(storeUrl);
        } else {
          Alert.alert(
            t('update.error.title', 'Update Error'),
            t('update.error.message', 'Unable to open app store. Please update manually.'),
            [{ text: t('common.ok', 'OK') }]
          );
        }
      })
      .catch(() => {
        Alert.alert(
          t('update.error.title', 'Update Error'),
          t('update.error.message', 'Unable to open app store. Please update manually.'),
          [{ text: t('common.ok', 'OK') }]
        );
      });
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Header with warning icon */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <FontAwesome5 name="exclamation-triangle" size={24} color="#FF6B35" />
          </View>
          <Text style={styles.title}>
            {t('update.required.title', 'Update Required')}
          </Text>
        </View>

        {/* Version information */}
        <View style={styles.versionInfo}>
          <Text style={styles.message}>
            {t('update.required.message', 'A new version of the app is required to continue.')}
          </Text>
          <View style={styles.versionContainer}>
            <Text style={styles.versionLabel}>
              {t('update.current.version', 'Current Version')}:
            </Text>
            <Text style={styles.versionText}>{currentVersion}</Text>
          </View>
          <View style={styles.versionContainer}>
            <Text style={styles.versionLabel}>
              {t('update.required.version', 'Required Version')}:
            </Text>
            <Text style={styles.versionText}>{minimumVersion}</Text>
          </View>
        </View>

        {/* Action buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.updateButton} onPress={handleUpdate}>
            <FontAwesome5 name="download" size={16} color="#FFFFFF" />
            <Text style={styles.updateButtonText}>
              {t('update.button', 'Update Now')}
            </Text>
          </TouchableOpacity>
          
          {onDismiss && VERSION_CONFIG.SETTINGS.ALLOW_DISMISS && (
            <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
              <Text style={styles.dismissButtonText}>
                {t('update.dismiss', 'Dismiss')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFF3F0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
  },
  versionInfo: {
    marginBottom: 24,
  },
  message: {
    fontSize: 16,
    color: '#525252',
    lineHeight: 24,
    marginBottom: 16,
  },
  versionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  versionLabel: {
    fontSize: 14,
    color: '#757575',
    fontWeight: '500',
  },
  versionText: {
    fontSize: 14,
    color: '#1A1A1A',
    fontWeight: '600',
  },
  buttonContainer: {
    gap: 12,
  },
  updateButton: {
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  updateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  dismissButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  dismissButtonText: {
    color: '#757575',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default UpdatePrompt; 