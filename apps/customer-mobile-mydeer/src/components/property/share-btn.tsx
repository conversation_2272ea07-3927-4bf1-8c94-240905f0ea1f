import { Pressable } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import { getBaseUrl } from '@/utils/base-url';
import * as Sharing from 'expo-sharing';

const PropertyShareBtn = ({
  propertyId,
  aboutProperty,
  propertyTitle,
  bg,
}: {
  propertyId: string;
  propertyTitle: string;
  aboutProperty: string;
  bg?: boolean;
}) => {
  const handleShare = () => {
    console.log('Share button clicked');
    // https://tabl3.com/property-listing?propertyFor=SALE&viewPropertyId=cm572y4gg001uurrgu2ky7xxk
    Sharing.shareAsync(`${getBaseUrl()}/?viewPropertyId=${propertyId}`, {
      dialogTitle: propertyTitle,
      // subject: aboutProperty,
      // excludedActivityTypes: [
      //   Sharing.ActivityType.ADD_TO_READS,
      //   Sharing.ActivityType.POST_TO_FACEBOOK,
      // ],
    });
  };

  return (
    <Pressable
      onPress={handleShare}
      className={` ${bg ? 'p-3 bg-[#F1F1F1] rounded-full' : ''}`}
    >
      <Image
        source={require('../../../assets/icons/share.png')}
        className="h-6 w-6"
        contentFit="contain"
      />
    </Pressable>
  );
};

export default PropertyShareBtn;
