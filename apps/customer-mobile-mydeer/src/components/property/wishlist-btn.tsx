import { ActivityIndicator, Pressable } from 'react-native';
import React from 'react';
import { api } from '@/utils/api';
import { Image } from 'expo-image';
import { showMessage } from 'react-native-flash-message';

const PropertyWishlistBtn = ({
  propertyId,
  isLiked,
  bg,
}: {
  propertyId: string;
  isLiked: boolean;
  bg?: boolean;
}) => {
  const utils = api.useUtils();

  const likePropertyMutation =
    api.user.handleAddRemoveFavouriteProperty.useMutation();

  const toggleLike = () => {
    likePropertyMutation.mutate(
      { propertyId },
      {
        onSuccess: (resp) => {
          utils.invalidate();
          utils.user.getPropertyHistory.invalidate();
          utils.agent.getAgentDetails.invalidate();
          showMessage({
            message: resp.message,
            type: 'success',
          });
        },
        onError: () => {
          showMessage({
            message: 'Error in removing property from favourites',
            type: 'danger',
          });
        },
      }
    );
  };

  return (
    <Pressable
      onPress={toggleLike}
      className={`${bg ? 'mr-5 p-3 bg-[#F1F1F1] rounded-full' : ''} `}
    >
      {likePropertyMutation.isPending ? (
        <ActivityIndicator size={24} color="" />
      ) : (
        <Image
          source={
            isLiked
              ? require('../../../assets/icons/like.png')
              : require('../../../assets/icons/unlike.png')
          }
          className="h-6 w-6"
          contentFit="contain"
        />
      )}
    </Pressable>
  );
};

export default PropertyWishlistBtn;
