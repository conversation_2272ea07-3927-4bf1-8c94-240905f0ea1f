import { Pressable, Text } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import { getBaseUrl } from '@/utils/base-url';
import * as Sharing from 'expo-sharing';

const AgentShareBtn = ({
  agentId,
  aboutAgent,
  agentName,
  bg,
}: {
  agentId: string;
  agentName: string;
  aboutAgent: string;
  bg?: boolean;
}) => {
  const handleShare = () => {
    Sharing.shareAsync(`${getBaseUrl()}/?viewAgentId=${agentId}`, {
      dialogTitle: agentName,
      // subject: aboutAgent,
      // excludedActivityTypes: [
      //   Sharing.ActivityType.ADD_TO_READS,
      //   Sharing.ActivityType.POST_TO_FACEBOOK,
      // ],
    });
  };

  return (
    <Pressable
      className={`w-1/3 flex-row items-center justify-center px-4 py-3 h-14 rounded-lg border-[1.5px] border-primary-750 ${bg ? 'p-3 bg-[#F1F1F1] rounded-full' : ''}`}
      onPress={handleShare}
    >
      <Image
        source={require('@assets/icons/share.png')}
        className="mr-3 h-6 w-6"
        tintColor={'#784100'}
        contentFit="contain"
      />
      <Text className="text-base text-primary-750 font-airbnb_md font-medium">
        Share
      </Text>
    </Pressable>
  );
};

export default AgentShareBtn;
