import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import { AgentProps } from '@/types';
import { useRouter } from 'expo-router';

const { height, width } = Dimensions.get('screen');

type AgentCardProps = {
  agent: AgentProps;
  agentPressed?: () => void;
};

export const AgentCard: React.FC<AgentCardProps> = ({
  agent,
  agentPressed,
}) => {
  const router = useRouter();

  const navigateToAgent = () => {
    router.navigate({
      pathname: `/agent-routes/[agentId]`,
      params: { agentId: agent.id },
    });
    agentPressed && agentPressed();
  };

  return (
    <Pressable
      key={agent.id}
      style={{
        width: width * 0.43,
        height: height * 0.27,
        position: 'relative',
        ...styles.shadowBox,
      }}
      className="bg-secondary-600 rounded-2xl"
      onPress={navigateToAgent}
    >
      <Image
        source={
          agent.cloudinaryProfileImageUrl
            ? { uri: agent.cloudinaryProfileImageUrl }
            : agent.filePublicUrl
              ? { uri: agent.filePublicUrl }
              : require('@assets/icons/default-user.png')
        }
        contentFit="cover"
        style={{ height: height * 0.18 }}
        className="w-full rounded-2xl"
      />
      {agent.rating && (
        <View
          className="px-2.5 py-2 absolute bg-secondary-100 flex-row items-center self-end"
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            zIndex: 10,
            borderBottomStartRadius: 12,
            borderTopRightRadius: 12,
          }}
        >
          <Image
            source={require('@assets/icons/star2.png')}
            className="mr-1 h-4 w-4"
          />
          <Text className="font-medium font-airbnb_md text-md text-secondary-650">
            {agent.rating}
          </Text>
        </View>
      )}

      <View
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
        }}
        className="m-2 p-3 rounded-lg bg-white"
      >
        <Text
          className="text-primary-800 font-airbnb_bd font-bold"
          numberOfLines={1}
          style={{ fontSize: 16 }}
        >
          {agent.name}
        </Text>

        <View className="mt-2 flex-row justify-between items-center">
          <View className="flex-row items-center">
            <Image
              source={require('@assets/icons/hut1.png')}
              className="mr-1 h-4 w-4"
            />
            <Text
              className="text-text-main700 font-normal text-[10px]"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {agent.propertiesSold ?? 0} Sold
            </Text>
          </View>

          <View className="flex-row items-center">
            <Image
              source={require('@assets/icons/experience2.png')}
              className="mr-1 h-4 w-4"
            />
            <Text
              className="text-text-main700 font-normal text-[10px]"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {agent.experience ?? 0}y Exp
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  shadowBox: {
    shadowColor: '#250000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
});
