import React from 'react';
import { useEffect, useState } from 'react';
import { Pressable, View, Text } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';

type FilterOption = {
  id: string;
  value: string;
};

type FilterProps = {
  FilterOptions: FilterOption[];
  label: string;
  onPress: (v: FilterOption[]) => void;
};

export const MultiSelectFilterComponent: React.FC<FilterProps> = ({
  FilterOptions,
  label,
  onPress,
}) => {
  const [isShown, setIsShown] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<FilterOption[]>([]);

  useEffect(() => {
    onPress(selectedFilter);
  }, [selectedFilter, onPress]);

  const handleShown = () => {
    setIsShown((prevState) => !prevState);
  };

  const toggleFilterType = (type: FilterOption) => {
    setSelectedFilter((prev) => {
      if (prev.some((p) => p.id === type.id)) {
        return prev.filter((p) => p.id !== type.id);
      } else {
        return [...prev, type];
      }
    });
  };

  return (
    <Pressable
      className={`py-4 px-5 border ${isShown ? 'border-secondary-300' : 'border-primary-200'} rounded-2xl`}
      onPress={handleShown}
    >
      <View className="mb-5 flex-row items-center justify-between">
        <Text
          className="font-airbnb_md font-medium text-lg text-text-600"
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{ maxWidth: '80%' }}
        >
          {label}
        </Text>
        <Pressable onPress={handleShown}>
          <FontAwesome6
            name={isShown ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#F04D24"
          />
        </Pressable>
      </View>

      {isShown && (
        <View>
          <View className="flex-row flex-wrap items-center">
            {selectedFilter.length > 0 ? (
              selectedFilter.map((type) => (
                <Pressable
                  key={type.id}
                  className="my-2 mr-2 self-start rounded-2xl bg-primary-700 px-5 py-3"
                  onPress={() => toggleFilterType(type)}
                >
                  <Text className="font-airbnb_bk text-sm font-normal text-white">
                    {type.value}
                  </Text>
                </Pressable>
              ))
            ) : (
              <Text className="mb-5 font-airbnb_md text-base text-text-600">
                No {label} selected.
              </Text>
            )}
          </View>

          <View className="flex-row flex-wrap items-center justify-start">
            {FilterOptions.filter(
              (type) => !selectedFilter.some((p) => p.id === type.id)
            ).map((type) => (
              <Pressable
                key={type.id}
                className="my-2 mr-2 self-start rounded-2xl border border-primary-100 bg-white px-5 py-3"
                onPress={() => toggleFilterType(type)}
              >
                <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                  {type.value}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      )}
    </Pressable>
  );
};
