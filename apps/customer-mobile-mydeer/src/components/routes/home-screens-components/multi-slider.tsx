import React from 'react';
import { useCallback, useState } from 'react';
import { Pressable, View, Text } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import MultiSlider from '@ptomasroos/react-native-multi-slider';

type SliderrProps = {
  label: string;
  minimumValue?: number;
  maximumValue?: number;
  onPress?: (v: { min: number; max: number }) => void;
};

export const MultiSliderr: React.FC<SliderrProps> = ({
  label,
  minimumValue = 0,
  maximumValue = 100,
  onPress,
}) => {
  const [isShown, setIsShown] = useState(true);
  const handleShown = useCallback(() => {
    setIsShown((prev) => !prev);
  }, []);

  const [sliderValues, setSliderValues] = useState<[number, number]>([10, 80]);

  const mapValue = useCallback(
    (val: number) => {
      return minimumValue + (maximumValue - minimumValue) * (val / 100);
    },
    [minimumValue, maximumValue]
  );

  const onValuesChangeFinish = useCallback(
    (values: number[]) => {
      setSliderValues([values[0], values[1]]);
      if (onPress) {
        onPress({
          min: mapValue(values[0]),
          max: mapValue(values[1]),
        });
      }
    },
    [mapValue, onPress]
  );

  const currentMinValue = mapValue(sliderValues[0]);
  const currentMaxValue = mapValue(sliderValues[1]);

  return (
    <Pressable
      className={`py-4 px-5 border ${
        isShown ? 'border-secondary-300' : 'border-primary-200'
      } rounded-2xl`}
      onPress={handleShown}
    >
      {/* Label */}
      <View className="flex-row items-center justify-between">
        <Text
          className="font-airbnb_md font-medium text-lg text-text-600"
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{ maxWidth: '80%' }}
        >
          {label}
        </Text>
        <Pressable onPress={handleShown}>
          <FontAwesome6
            name={isShown ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#F04D24"
          />
        </Pressable>
      </View>

      {isShown && (
        <>
          <View className="flex-1 items-center justify-center">
            <MultiSlider
              isMarkersSeparated={true}
              min={0}
              max={100}
              step={1}
              values={sliderValues}
              allowOverlap={false}
              snapped
              trackStyle={{ height: 4 }}
              selectedStyle={{ height: 6, backgroundColor: '#F04D24' }}
              // onValuesChangeFinish={onValuesChangeFinish}
              onValuesChange={onValuesChangeFinish}
            />
          </View>
          <View className="flex-row items-center justify-between">
            <Text className="font-airbnb_bk text-sm text-text-600">
              {Math.round(currentMinValue)}
            </Text>
            <Text className="font-airbnb_bk text-sm text-text-600">
              {Math.round(currentMaxValue)}
            </Text>
          </View>
        </>
      )}
    </Pressable>
  );
};
