import React from 'react';
import { useState } from 'react';
import { Pressable, View, Text } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';

type FilterOption = {
  id: string;
  value: string;
};

type FilterProps = {
  options: FilterOption[];
  label: string;
  onPress: (v: FilterOption | undefined) => void;
};

export const SingleSelectFilterComponent: React.FC<FilterProps> = ({
  options,
  label,
  onPress,
}) => {
  const [isShown, setIsShown] = useState(true);
  const handleShown = () => {
    setIsShown((prevState) => !prevState);
  };

  const [selected, setSelected] = useState<FilterOption | undefined>(undefined);

  const toggleSelect = (selectedValue: FilterOption) => {
    const newSelected =
      selected?.id === selectedValue.id ? undefined : selectedValue;
    setSelected(newSelected);
    onPress(newSelected);
  };

  return (
    <Pressable
      className={`py-4 px-5 border ${isShown ? 'border-secondary-300' : 'border-primary-200'} rounded-2xl`}
      onPress={handleShown}
    >
      <View className="mb-5 flex-row items-center justify-between">
        <Text
          className="font-airbnb_md font-medium text-lg text-text-600"
          numberOfLines={1}
          ellipsizeMode="tail"
          style={{ maxWidth: '80%' }}
        >
          {label}
        </Text>
        <Pressable onPress={handleShown}>
          <FontAwesome6
            name={isShown ? 'chevron-up' : 'chevron-down'}
            size={20}
            color="#F04D24"
          />
        </Pressable>
      </View>

      {isShown && (
        <View>
          {/* Display options */}
          <View className="flex-row flex-wrap items-center">
            {options.map((option) => (
              <Pressable
                key={option.id}
                className={`my-2 mr-2 self-start rounded-2xl ${
                  selected === option ? 'bg-primary-700' : 'bg-gray-100'
                } px-5 py-3`}
                onPress={() => toggleSelect(option)}
              >
                <Text
                  className={`font-airbnb_bk text-sm font-normal ${
                    selected === option ? 'text-white' : 'text-text-600'
                  }`}
                >
                  {option.value}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      )}
    </Pressable>
  );
};
