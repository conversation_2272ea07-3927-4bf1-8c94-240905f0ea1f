import React, { useEffect, useState } from 'react';
import {
  Pressable,
  View,
  Text,
  SafeAreaView,
  Alert,
  Platform,
  Dimensions,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { BlurView } from 'expo-blur';
import { Button } from '@/components/ui';
import { AgentProps, PropertyDetailsProps } from '@/types';
import MapView, { Marker, Polygon, PROVIDER_GOOGLE } from 'react-native-maps';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { formatTime } from '@/utils/format-terms';
import { router } from 'expo-router';
import PropertyWishlistBtn from '@/components/property/wishlist-btn';
import PropertyShareBtn from '@/components/property/share-btn';
import { styles } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetScrollable/BottomSheetFlashList';
import { CustomModal } from '@/components/shared/custom-modal';
import { AgentCard } from '../agents-component/agent-card';
import AntDesign from '@expo/vector-icons/AntDesign';
import { SwitchAddAgent } from '@/components/shared/switch-add-agent';
import { ScrollView } from 'react-native-gesture-handler';

const { height, width } = Dimensions.get('screen');

type FacilitiesProps = {
  facilityData: Pick<PropertyDetailsProps, 'amenities'>;
  onPress?: () => void;
};

const Facilities: React.FC<FacilitiesProps> = ({ onPress, facilityData }) => {
  return (
    <View className="flex-row flex-wrap justify gap-2.5">
      {facilityData?.amenities.map((data) => (
        <View key={data.id} className="mb-4 items-center w-20">
          <Pressable
            key={data.id}
            onPress={onPress}
            className={` self-center rounded-full`}
          >
            <Image source={data.filePublicUrl} className="h-12 w-12" />
          </Pressable>
          <Text
            className={`mt-1 font-airbnb_bk text-xs font-normal text-center`}
          >
            {data.name}
          </Text>
        </View>
      ))}
    </View>
  );
};

interface OptionsProps {
  options: string[];
}

const Options: React.FC<OptionsProps> = ({ options }) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  return (
    <View className="flex-row flex-wrap items-center">
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => setSelectedOption(option)}
          className={`mb-2 mr-3 self-start rounded-xl border border-primary-0 px-2 py-2.5 ${
            selectedOption === option ? 'bg-secondary-main700' : 'bg-white'
          }`}
        >
          <Text
            className={`font-airbnb_bk text-xs font-normal ${
              selectedOption === option ? 'text-primary-50' : 'text-text-600'
            }`}
          >
            {option}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

interface PropsType {
  title: string;
}

const Heading: React.FC<PropsType> = ({ title }) => (
  <Text className="mb-2 font-airbnb_bd text-base font-bold text-text-600">
    {title}
  </Text>
);

const Location: React.FC<PropsType> = ({ title }) => (
  <View className="flex-row items-center">
    <Image
      source={require('@assets/icons/location2.png')}
      className="mr-1 h-4 w-4"
    />
    <Text
      className="font-airbnb_bk text-sm font-normal text-text-600"
      numberOfLines={2}
      ellipsizeMode="tail"
    >
      {title}
    </Text>
  </View>
);

export const PropertyDetailsPage = ({
  item,
  dismissModal,
  addToUserHistory,
  isLiked: propIsLiked,
}: {
  item: PropertyDetailsProps;
  dismissModal?: () => void;
  addToUserHistory?: () => void;
  isLiked?: boolean;
}) => {
  // utils for invalidating the queries
  const utils = api.useUtils();
  // checking if the user connected by the agent who listed this property
  const customerDetail = api.user.getProfile.useQuery();
  const connectedAgentId = customerDetail.data?.connections[0]?.agentId ?? '';
  const agentId = item.userId;
  const isConnectedAgent = agentId === connectedAgentId;

  // checking if the property is liked by the user
  const { data: userData } = api.user.getFavouritesProperties.useQuery();
  const isLiked =
    propIsLiked ??
    userData?.some((fvrtProperties) => fvrtProperties.propertyId === item.id) ??
    false;

  // getting the connection id of with the connected agent
  const { data: connectedAgentData } = api.agent.getProfileById.useQuery({
    agentId: connectedAgentId,
  });
  const connectionId = connectedAgentData?.agent?.coustomerConnections[0]?.id;

  // modal for the connection request
  const [isModalVisible, setIsModalVisible] = useState(false);

  const imageUrls = item.mediaSections
    .map((mediaSections) =>
      mediaSections.media.map(
        (media) => media.cloudinaryUrl ?? media.filePublicUrl
      )
    )
    .flat();

  //images is the array of images
  const images = imageUrls.map((url, index) => ({
    id: index,
    source: url,
  }));

  const handleDismiss = () => {
    if (dismissModal) {
      dismissModal();
    }
  };

  useEffect(() => {
    if (addToUserHistory) addToUserHistory();
  }, []);

  // State to store property boundaries
  const [propertyBoundaries, setPropertyBoundaries] = useState<
    { lat: number; lng: number }[]
  >([]);

  useEffect(() => {
    if (item.propertyMarkersLatLng) {
      try {
        // Parse the propertyMarkersLatLng if it's a string
        let parsedBoundaries;
        if (typeof item.propertyMarkersLatLng === 'string') {
          parsedBoundaries = JSON.parse(item.propertyMarkersLatLng);
        } else {
          parsedBoundaries = item.propertyMarkersLatLng;
        }

        // Convert string coordinates to numbers if needed
        if (Array.isArray(parsedBoundaries) && parsedBoundaries.length >= 3) {
          const validBoundaries = parsedBoundaries.map((point) => ({
            lat:
              typeof point.lat === 'string' ? parseFloat(point.lat) : point.lat,
            lng:
              typeof point.lng === 'string' ? parseFloat(point.lng) : point.lng,
          }));

          setPropertyBoundaries(validBoundaries);
        }
      } catch (error) {
        console.error('Error processing property boundaries:', error);
      }
    }
  }, [item]);

  // Compute map region to fit the polygon
  const getPolygonRegion = () => {
    if (propertyBoundaries.length < 3) {
      // Default region if no polygon
      return {
        latitude: Number(item.propertyLatitude),
        longitude: Number(item.propertyLongitude),
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
    }

    // Calculate the center and span of the polygon
    let minLat = Infinity,
      maxLat = -Infinity;
    let minLng = Infinity,
      maxLng = -Infinity;

    propertyBoundaries.forEach((point) => {
      minLat = Math.min(minLat, point.lat);
      maxLat = Math.max(maxLat, point.lat);
      minLng = Math.min(minLng, point.lng);
      maxLng = Math.max(maxLng, point.lng);
    });

    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;
    // Add some padding around the polygon
    const latDelta = (maxLat - minLat) * 1.2;
    const lngDelta = (maxLng - minLng) * 1.2;

    return {
      latitude: centerLat,
      longitude: centerLng,
      latitudeDelta: Math.max(0.01, latDelta),
      longitudeDelta: Math.max(0.01, lngDelta),
    };
  };

  // loading for images
  const [isLoading, setIsLoading] = useState<Record<number, boolean>>({});

  return (
    <View>
      <View className="px-4 h-full w-full">
        <ScrollView showsVerticalScrollIndicator={false}>
          <Pressable
            onPress={() =>
              router.push({
                pathname: '/(app)/home/<USER>',
                params: {
                  images: JSON.stringify(
                    images.map((image, index) => ({
                      id: index,
                      source: image,
                    }))
                  ),
                },
              })
            }
            className="mt-2 py-[18px]"
          >
            {images && images.length > 0 ? (
              <Image
                source={images[0].source}
                contentFit="cover"
                className="h-[200px] w-full rounded-2xl"
              />
            ) : (
              <Text className="text-text-500">No images available</Text>
            )}
          </Pressable>
          <View className="rounded-2xl overflow-hidden">
            <BlurView
              intensity={80}
              className="mt-4 px-4 py-4"
              style={{
                backgroundColor: '#FFF8F4',
                borderRadius: 16,
                borderWidth: 0.5,
                borderColor: 'rgba(0,0,0,0.1)',
                overflow: 'hidden',
              }}
            >
              <View className="flex-row items-center justify-between gap-2">
                <View className=" ">
                  <Text className="font-airbnb_bd text-xl font-bold text-text-main700">
                    {item.propertyTitle}
                  </Text>
                  <Location title={item?.propertyAddress ?? 'dummy'} />
                </View>

                <View className="flex-row items-center">
                  <Pressable className="mr-4 rounded-lg bg-white p-2.5">
                    <PropertyWishlistBtn
                      propertyId={item.id}
                      isLiked={isLiked}
                    />
                  </Pressable>
                  <Pressable className="rounded-lg bg-white p-2.5">
                    <PropertyShareBtn
                      propertyId={item.id}
                      propertyTitle={item.propertyTitle ?? ''}
                      aboutProperty={item.aboutProperty ?? ''}
                    />
                  </Pressable>
                </View>
              </View>

              <View className="my-3 flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Image
                    source={require('@assets/icons/yostar.png')}
                    className="mr-2 h-4 w-4"
                  />
                  <View className="flex-row items-center">
                    <Text className="mr-1 font-airbnb_bd text-sm font-bold text-text-600">
                      {item.rating ?? 'N/A'}
                    </Text>
                    {/* <Text className="font-airbnb_bk text-sm font-normal text-text-400">
                      ({item.review ?? 'N/A'})
                    </Text> */}
                  </View>
                </View>

                <View>
                  <Text className="font-airbnb_md text-sm font-medium text-text-500">
                    {item.review ?? 'N/A'} reviews
                  </Text>
                </View>
              </View>

              <View className="mb-4">
                <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                  {item.aboutProperty}
                </Text>
                {/* <Pressable>
                  <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700 ">
                    Read more
                  </Text>
                </Pressable> */}
              </View>

              <Pressable onPress={() => setIsModalVisible(true)}>
                <Heading title="Gallery" />
              </Pressable>

              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <Pressable
                  className="flex-row"
                  onPress={async () => {
                    await handleDismiss();
                    router.push({
                      pathname: '/(app)/home/<USER>',
                      params: { images: JSON.stringify(images) },
                    });
                  }}
                >
                  {images.map((image) => (
                    <View
                      key={image.id}
                      className="mr-1.5 h-32 w-32 rounded-xl overflow-hidden"
                    >
                      {isLoading[image.id] && (
                        <ActivityIndicator
                          size="small"
                          color="#F04D24"
                          className="absolute top-0 bottom-0 left-0 right-0"
                        />
                      )}

                      <Image
                        source={image.source}
                        className="h-full w-full"
                        contentFit="cover"
                        transition={200}
                        onLoadStart={() =>
                          setIsLoading((prev) => ({
                            ...prev,
                            [image.id]: true,
                          }))
                        }
                        onLoadEnd={() =>
                          setIsLoading((prev) => ({
                            ...prev,
                            [image.id]: false,
                          }))
                        }
                      />
                    </View>
                  ))}
                </Pressable>
              </ScrollView>
            </BlurView>
          </View>

          <View className="mt-8 flex-row items-center justify-between">
            <Heading title="Location" />
            <Location title={item.propertyLocation ?? 'dummy address'} />
          </View>

          <View className="mt-3 items-center justify-center">
            {item.propertyLatitude && item.propertyLongitude ? (
              <View>
                <MapView
                  style={{
                    height: height * 0.25,
                    width: width * 0.928,
                    borderRadius: 16,
                  }}
                  provider={PROVIDER_GOOGLE}
                  userInterfaceStyle="light"
                  initialRegion={getPolygonRegion()}
                >
                  {/* Main property marker */}
                  <Marker
                    coordinate={{
                      latitude: Number(item.propertyLatitude),
                      longitude: Number(item.propertyLongitude),
                    }}
                  />

                  {/* Property boundary polygon */}
                  {propertyBoundaries.length >= 3 && (
                    <Polygon
                      coordinates={propertyBoundaries.map((point) => ({
                        latitude: point.lat,
                        longitude: point.lng,
                      }))}
                      strokeColor="#F04D24"
                      fillColor="rgba(240,77,36,0.2)"
                      strokeWidth={2}
                    />
                  )}
                </MapView>

                {/* Visual indicator of property area */}
                <View
                  style={{
                    position: 'absolute',
                    top: 10,
                    right: 10,
                    backgroundColor: 'white',
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    borderRadius: 8,
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#ECE9E8',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                    elevation: 2,
                  }}
                >
                  <View
                    style={{
                      width: 8,
                      height: 8,
                      backgroundColor: '#F04D24',
                      marginRight: 5,
                      borderRadius: 4,
                    }}
                  />
                  <Text
                    style={{
                      fontFamily: 'AirbnbW_Md',
                      fontSize: 10,
                      color: '#525252',
                    }}
                  >
                    Property Area
                  </Text>
                </View>
              </View>
            ) : (
              <Text className="font-medium text-base font-airbnb_md text-text-main700">
                Failed to Load Map
              </Text>
            )}
          </View>
          <View className="mb-6 mt-3 flex-row items-center gap-2">
            <Pressable
              className={
                ' flex-row items-center self-start rounded-xl border border-primary-0 bg-white px-2 py-2.5'
              }
            >
              <Image
                source={require('@assets/icons/metro.png')}
                className="mr-2 h-3 w-3"
              />
              <Text
                className={'font-airbnb_md text-xs font-medium text-text-600'}
              >
                Metro:{' '}
              </Text>
              <Text
                className={'font-airbnb_bk text-xs font-normal text-text-600'}
              >
                3km away from metro state
              </Text>
            </Pressable>
            <Pressable
              className={
                ' flex-row items-center self-start rounded-xl border border-primary-0 bg-white px-2 py-2.5'
              }
            >
              <Image
                source={require('@assets/icons/metro.png')}
                className="mr-2 h-3 w-3"
              />
              <Text
                className={'font-airbnb_md text-xs font-medium text-text-600'}
              >
                Mall:{' '}
              </Text>
              <Text
                className={'font-airbnb_bk text-xs font-normal text-text-600'}
              >
                3km walking
              </Text>
            </Pressable>
          </View>

          {/* Property section */}
          <View className="gap-3">
            <Heading title="About the property" />
            <Options options={item.utilities.map((data) => data.utility)} />
            <Text className="font-airbnb_bk text-sm font-normal text-text-main700">
              {item.aboutProperty}
            </Text>
            <Pressable>
              {/* <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">
                See All
              </Text> */}
            </Pressable>
          </View>

          {/* Facilities */}
          <View className="mt-2.5">
            <Heading title="Facilities" />
            <View>
              <Facilities facilityData={item} />
              <Pressable>
                <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">
                  See All
                </Text>
              </Pressable>
            </View>
          </View>

          {/* agent details */}
          <Pressable
            className="mt-2.5"
            onPress={() => {
              handleDismiss();
              router.push(`/agent-routes/${item.userId}`);
            }}
          >
            <Heading title={'Listed by '} />
            <View className="mb-4 mt-3 flex-row items-center justify-between rounded-xl border border-[#E9E2DD] px-4 py-3">
              {/* image and name part */}
              <View className="flex-row items-center">
                <Image
                  source={
                    item.user.filePublicUrl ||
                    require('@assets/icons/profile.png')
                  }
                  className="aspect-square w-[86px] rounded-md"
                />
                <View className="ml-3">
                  <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-750">
                    {item.user.name}
                  </Text>
                  <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                    {item.user.company?.companyName}
                  </Text>
                </View>
              </View>

              {/* rating & review part */}
              <View>
                <View className="mb-3 flex-row items-center self-end">
                  <Image
                    source={require('@assets/icons/star.png')}
                    className="h-4 w-4 rounded-2xl"
                  />
                  <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-text-600">
                    {item.user.rating}
                  </Text>
                </View>

                <View>
                  <Text className="font-airbnb_md text-xs font-medium text-text-600">
                    {item.user.reviews} reviews
                  </Text>
                </View>
              </View>
            </View>
            {/* <Pressable className="mb-6">
              <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">
                See All
              </Text>
            </Pressable> */}
          </Pressable>

          {/* Comments */}
          {item.comments.length > 0 && (
            <View>
              {/* comment filter heading */}
              <View className="mb-6 flex-row items-center justify-between">
                <Heading title={`${item.comments.length} Comments`} />
                {/* Sir asked to comment the filter comments option for now ↓ */}
                {/* <View className="flex-row items-center gap-2">
                  <Text className="font-airbnb_md text-xs font-medium text-[#838383]">
                    Sort By:
                  </Text>
                  <Pressable className="flex-row items-center rounded-[4px] border border-[#E9E2DD] py-1 pl-3 pr-1">
                    <Text className="font-airbnb_md text-sm font-medium text-text-550">
                      Newest
                    </Text>
                    <Entypo name="chevron-small-down" size={18} color="#838383" />
                  </Pressable>
                </View> */}
              </View>

              {/* all comments */}

              <View>
                {item.comments.map((comment, index) => (
                  <View
                    key={index}
                    className="mb-6 flex-row items-center justify-between"
                  >
                    <View className="flex-row items-center">
                      <Image
                        source={comment.user.filePublicUrl}
                        className="mr-3 h-14 w-14 rounded-full"
                        contentFit="contain"
                      />
                      <View>
                        <Text className="mb-1 font-airbnb_bd text-sm font-bold text-primary-750">
                          {comment.user.name}
                        </Text>
                        <Text className="font-airbnb_bk text-sm font-normal text-text-500">
                          {formatTime(comment.createdAt)}
                        </Text>
                      </View>
                    </View>
                    <Pressable>
                      <Image
                        source={require('@assets/icons/horizontaldot.png')}
                        className="h-5 w-5"
                      />
                    </Pressable>
                    <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                      {comment.description}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}
          <View className="mb-28" />
        </ScrollView>
      </View>

      {/* Bottom Button */}
      <View>
        <BlurView
          intensity={30}
          tint="light"
          blurReductionFactor={4}
          experimentalBlurMethod="dimezisBlurView"
          className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50/30 px-5 py-3"
        >
          <Button
            label={isConnectedAgent ? 'Connected' : 'Contact Agent'}
            disabled={isConnectedAgent || isModalVisible}
            loading={isModalVisible}
            onPress={() => setIsModalVisible(true)}
          />
        </BlurView>
      </View>

      <SwitchAddAgent
        isVisible={isModalVisible}
        currentAgent={connectedAgentData?.agent as AgentProps}
        newAgent={item.user as AgentProps}
        propertyId={item.id}
        onClose={() => setIsModalVisible(false)}
        onSuccess={() => {
          utils.invalidate();
        }}
      />
    </View>
  );
};
// AgentCard
