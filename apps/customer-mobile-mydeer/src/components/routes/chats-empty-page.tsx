import { Image as NImage } from 'expo-image';
import React from 'react';
import { Text, View } from 'react-native';

import { Button } from '../ui';
import { router, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function ChatsEmpty() {
  const { query } = useLocalSearchParams<{ query: string }>();
  const isSearched = query?.length;
  const insets = useSafeAreaInsets();
  
  return (
    <View className="flex-1 items-center justify-center" style={{ paddingTop: insets.top }}>
      <View className="flex px-5">
        <NImage
          source={require('@assets/images/pana2.png')}
          className="aspect-square w-full"
          contentFit="contain"
        />
      </View>

      <View className="h-1/2 flex items-center  px-4">
        <Text className="mb-3 text-center font-airbnb_bd text-lg font-bold text-text-600">
          {isSearched ? 'No results found' : 'No chats found'}
        </Text>
        <Text className="mb-8 text-center font-airbnb_bk text-base font-normal text-text-500">
          {isSearched ? '' : 'Start a new chat to connect with an agent'}
        </Text>
        {/*<Button*/}
        {/*  variant="chat"*/}
        {/*  label="Start Chat"*/}
        {/*  className="w-2/3"*/}
        {/*  onPress={() => router.push('/(app)')}*/}
        {/*/>*/}
      </View>
    </View>
  );
}
