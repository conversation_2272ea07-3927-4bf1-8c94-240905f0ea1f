import React from 'react';
import { Checkbox, Text, View } from '@/components/ui';
import * as WebBrowser from 'expo-web-browser';
import { showMessage } from 'react-native-flash-message';

interface TermsCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  testID?: string;
  error?: string;
}

export const TermsCheckbox: React.FC<TermsCheckboxProps> = ({
  checked,
  onChange,
  testID = 'terms-checkbox',
  error,
}) => {
  const handleLinkPress = async (url: string) => {
    try {
      await WebBrowser.openBrowserAsync(url);
    } catch (error) {
      showMessage({
        message: 'Unable to open the link',
        type: 'danger',
      });
    }
  };

  return (
    <>
      <View className="flex-row items-start space-x-2 px-4 py-2">
        <View className="pt-1">
          <Checkbox
            checked={checked}
            onChange={onChange}
            accessibilityLabel="Accept terms and conditions"
            testID={testID}
          />
        </View>
        <View className="flex-1 pl-2">
          <Text className="text-sm text-text-550 leading-5">
            I agree to the{' '}
            <Text
              className="text-secondary-650 underline"
              onPress={() => handleLinkPress('https://mydeer.net/terms-and-conditions')}
            >
              Terms of Service
            </Text>
            {' '}and{' '}
            <Text
              className="text-secondary-650 underline"
              onPress={() => handleLinkPress('https://mydeer.net/privacy-policy')}
            >
              Privacy Policy
            </Text>
          </Text>
        </View>
      </View>
      {error && (
        <Text className="px-4 text-sm text-danger-600">{error}</Text>
      )}
    </>
  );
}; 