import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm, Controller } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { signUpInputValidation } from '@/lib/schema/auth';
import { z } from 'zod';

import {
  Button,
  ControlledInput,
  ScrollView,
  Text,
  View,
} from '@/components/ui';
import { useRouter } from 'expo-router';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { TermsCheckbox } from './terms-checkbox';

export type FormType = z.infer<typeof signUpInputValidation>;

export const SignUpForm = () => {
  const router = useRouter();
  const { handleSubmit, control } = useForm<FormType>({
    resolver: zodResolver(signUpInputValidation),
    defaultValues: {
      acceptTerms: false,
    },
  });

  // const { data: cityData } = api.user.getCities.useQuery();
  const signUpMutation = api.user.signUp.useMutation({
    onSuccess: (opts, data) => {
      if (opts.warning) {
        showMessage({
          message: String(opts.message),
          type: 'warning',
        });
      } else {
        showMessage({
          message: String(opts.message),
          type: 'success',
        });

        router.push({
          pathname: '/auth/otp',
          params: {
            phoneNumber: data.phoneNumber,
          },
        });
      }
    },
  });

  const onSubmitInternal = (data: FormType) => {
    // Remove acceptTerms from the data before sending to API
    const { acceptTerms, ...apiData } = data;
    const formData = {
      ...apiData,
      adharcardNumber: apiData.adharcardNumber || undefined,
    };
    signUpMutation.mutate(formData, {
      onSuccess: () => {
        showMessage({
          message: 'Signup successful',
          type: 'success',
        });
        router.push({
          pathname: `/auth/otp`,
          params: {
            phoneNumber: data.phoneNumber,
          },
        });
      },
      onError: (error) => {
        showMessage({
          message:
            error.message ?? 'Something went wrong, Signup not successful',
          type: 'danger',
        });
      },
    });
  };

  const onFormError = (errors: any) => {
    console.log('errors', errors);
    showMessage({
      message: 'Please check form fields',
      type: 'danger',
    });
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      // keyboardVerticalOffset={10}
    >
      <View className="p-4">
        <Text
          testID="form-title"
          className="mb-2 font-airbnb_xbd text-2xl font-extrabold text-secondary-650"
        >
          Sign Up
        </Text>
        <Text className="mb-5 font-airbnb_bk text-base font-normal text-text-550">
          Create account to continue with 99 Deers
        </Text>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        // contentContainerStyle={{ flex: 1 }}
        className="flex-1"
      >
        <View className={`px-4`}>
          <ControlledInput
            // imagesource={require('@assets/icons/profile.png')}
            testID="name"
            control={control}
            name="name"
            label="Name"
          />
          <ControlledInput
            // imagesource={require('@assets/icons/profile.png')}
            testID="email-input"
            control={control}
            name="email"
            label="Email Id"
            autoCapitalize="none"
          />
          <ControlledInput
            testID="phone-number"
            control={control}
            keyboardType="numeric"
            name="phoneNumber"
            label="Phone Number"
          />
          <ControlledInput
            testID="aadhaar-number"
            control={control}
            keyboardType="numeric"
            name="adharcardNumber"
            label="Aadhaar Card Number (optional)"
          />
        </View>
      </ScrollView>
      <View className="p-4">
        <Controller
          name="acceptTerms"
          control={control}
          defaultValue={false}
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <TermsCheckbox
              checked={value}
              onChange={onChange}
              testID="accept-terms-checkbox"
              error={error?.message}
            />
          )}
        />
        <Button
          testID="continue-button"
          label="Continue"
          loading={signUpMutation.isPending}
          onPress={handleSubmit(onSubmitInternal, onFormError)}
        />
      </View>
    </KeyboardAvoidingView>
  );
};
