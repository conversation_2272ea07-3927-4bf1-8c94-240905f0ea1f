import { View, Text, Pressable, ImageSourcePropType } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';

interface SocialSignUpProps {
  social: string;
  imageSource: ImageSourcePropType;
  onPress?: () => void;
}

export const SocialSignUp = ({
  social,
  imageSource,
  onPress,
}: SocialSignUpProps) => {
  return (
    <Pressable
      className="items-center border border-secondary-200 rounded-xl mb-4 "
      onPress={onPress}
    >
      <View className="px-10 py-3.5 items-center flex-row ">
        <Image source={imageSource} className="h-5 w-5" />
        <Text className="ml-6 px-5">Continue with {social}</Text>
      </View>
    </Pressable>
  );
};
