import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import React from 'react';
import { useRouter } from 'expo-router';
import { useForm, SubmitHandler } from 'react-hook-form';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { Pressable } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import { z } from 'zod';

import {
  Button,
  ControlledInput,
  ScrollView,
  Text,
  View,
} from '@/components/ui';
// import { api } from '@/utils/api';
import Toast from 'react-native-toast-message';
import { signInSchema } from '@/lib/schema/auth';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';

// Create schema with explicit type

export type FormType = z.infer<typeof signInSchema>;

export type SignInFormProps = {
  onSubmit: SubmitHandler<FormType>;
};

export const SignInForm = ({ onSubmit }: SignInFormProps) => {
  const router = useRouter();
  const { handleSubmit, control } = useForm<FormType>({
    resolver: zodResolver(signInSchema),
  });

  const signInMutation = api.user.signIn.useMutation({
    onSuccess: (opts, data) => {
      if (opts.warning) {
        showMessage({
          type: 'warning',
          message: String(opts.message),
        });
      } else if (opts.message) {
        showMessage({
          type: 'success',
          message: String(opts.message),
        });
        router.push({
          pathname: '/auth/otp',
          params: {
            phoneNumber: data.phoneNumber,
          },
        });
      }
    },
  });

  const onSubmitInternal = async (data: FormType) => {
    const resp = await signInMutation.mutate(data);
  };

  const handleLinkPress = async (url: string) => {
    try {
      await WebBrowser.openBrowserAsync(url);
    } catch (error) {
      showMessage({
        message: 'Unable to open the link',
        type: 'danger',
      });
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      // keyboardVerticalOffset={10}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flex: 1 }}
      >
        <View className="p-4">
          <Text
            testID="form-title"
            className="mb-2 font-airbnb_xbd text-2xl font-extrabold text-secondary-650"
          >
            Login
          </Text>
          <Text className="mb-5 font-airbnb_bk text-base font-normal text-text-550">
            Enter your phone number and login into your account
          </Text>

          <View className="mb-6">
            <ControlledInput
              testID="phone-number"
              control={control}
              keyboardType="numeric"
              placeholder="Enter your phone number here"
              name="phoneNumber"
              label="Phone Number"
            />
          </View>

          <Button
            testID="continue-button"
            label="Continue"
            loading={signInMutation.isPending}
            disabled={signInMutation.isPending}
            onPress={handleSubmit(onSubmitInternal)}
          />

          <View className="mt-4 flex-row items-center justify-center gap-4">
            <Pressable onPress={() => router.push('/auth/sign-up')}>
              <Text className="text-secondary-650">
                Don't have an account? Sign up
              </Text>
            </Pressable>
          </View>
          <View className="w-full mt-10">
            <Text className="text-sm text-text-550 leading-5">
              By clicking on continue you agree to the platform{' '}
              <Text
                className="text-secondary-650 underline"
                onPress={() => handleLinkPress('https://mydeer.net/terms-and-conditions')}
              >
                Terms of Service
              </Text>
              {' '}and{' '}
              <Text
                className="text-secondary-650 underline"
                onPress={() => handleLinkPress('https://mydeer.net/privacy-policy')}
              >
                Privacy Policy
              </Text>
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};
