import { Image as NImage } from 'expo-image';
import React from 'react';
import { Dimensions, type ImageSourcePropType, Text, View } from 'react-native';
const { height, width } = Dimensions.get('screen');

type OnboardingComponentProps = {
  label: string;
  text: string;
  image: ImageSourcePropType;
};
const OnboardingComponent: React.FC<OnboardingComponentProps> = ({
  label,
  text,
  image,
}) => {
  return (
    <View className="items-center justify-center w-full">
      <NImage
        source={image}
        contentFit="contain"
        style={{
          width: Math.min(width * 0.7, 280),
          height: Math.min(width * 0.7, 280),
        }}
      />
      <View className="mt-4 w-full px-4">
        <Text
          className="text-center font-airbnb_xbd text-primary-700"
          style={{
            fontSize: Math.min(width * 0.055, 22),
          }}
          numberOfLines={2}
          adjustsFontSizeToFit
        >
          {label}
        </Text>
        <Text
          className="mt-2 text-center font-airbnb_bk text-text-main700"
          style={{
            fontSize: Math.min(width * 0.035, 14),
          }}
          numberOfLines={3}
          adjustsFontSizeToFit
        >
          {text}
        </Text>
      </View>
    </View>
  );
};

export default OnboardingComponent;
