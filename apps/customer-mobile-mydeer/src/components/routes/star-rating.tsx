import { useState } from 'react';
import { Pressable, View } from 'react-native';
import { Image as NImage } from 'expo-image';
import React from 'react';

type StarRatingProps = {
  onRatingSelected: (rating: number) => void;
};

// star rating function/component
export const StarRating: React.FC<StarRatingProps> = ({ onRatingSelected }) => {
  const [rating, setRating] = useState(0);

  const handlePress = (index: number) => {
    setRating(index + 1); // Update the rating
    if (onRatingSelected) {
      onRatingSelected(index + 1); // Call the callback with the new rating
    }
  };

  return (
    <View className="flex-row items-center">
      {Array.from({ length: 5 }, (_, index) => (
        <Pressable key={index} onPress={() => handlePress(index)}>
          <NImage
            source={require('../../../assets/icons/yostar.png')}
            className={`mr-6 h-10 w-10 ${index < rating ? 'opacity-100' : 'opacity-20'}`} // Adjust opacity based on rating
          />
        </Pressable>
      ))}
    </View>
  );
};
