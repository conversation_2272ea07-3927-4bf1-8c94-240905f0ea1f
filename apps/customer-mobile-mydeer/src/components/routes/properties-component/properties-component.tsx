/* eslint-disable max-lines-per-function */
import { Image as NImage, ImageBackground } from 'expo-image';
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { Button } from '@/components/ui';
import { FormatAreaIn, formatPriceAddLabels } from '@/utils/format-terms';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import PropertyWishlistBtn from '@/components/property/wishlist-btn';
import PropertyShareBtn from '@/components/property/share-btn';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import { AgentProps, IProperty } from '@/types';
import { SwitchAddAgent } from '@/components/shared/switch-add-agent';

const PropertiesComponent = ({
  item,
  handlePress,
  isLiked,
}: {
  item: IProperty;
  handlePress?: () => void;
  isLiked?: boolean;
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const likedState = isLiked ?? item.customerFavourites?.length > 0 ?? false;

  const utils = api.useUtils();
  const url =
    item.mediaSections &&
    item.mediaSections[0] &&
    ((item.mediaSections[0]?.media[0]?.cloudinaryUrl ||
      item.mediaSections[0]?.media[0]?.filePublicUrl) as string);

  // Get current agent data
  const customerDetail = api.user.getProfile.useQuery();
  const connectedAgentId = customerDetail.data?.connections[0]?.agentId ?? '';
  const { data: connectedAgentData } = api.agent.getProfileById.useQuery({
    agentId: connectedAgentId,
  });
  const agentId = item.userId;
  const isConnectedAgent = agentId === connectedAgentId;

  return (
    <Pressable
      onPress={() => handlePress && handlePress()}
      style={{ borderRadius: 6 }}
    >
      <View className="mt-5 flex-1 rounded-lg">
        <ImageBackground
          source={require('@assets/images/favbg.png')}
          contentFit="fill"
          style={{ flex: 1 }}
          imageStyle={{ borderRadius: 6 }}
        >
          <ImageBackground
            source={url ? { uri: url } : ''}
            contentFit="cover"
            style={{
              aspectRatio: 21 / 9,
              width: '100%',
              backgroundColor: url ? 'transparent' : '#898e8c',
              borderRadius: 6,
            }}
            imageStyle={{ borderRadius: 6 }}
          >
            <View className="flex-1 flex-col justify-between p-3">
              <View className="self-start rounded-md bg-[#F4FFFD] px-2 py-1.5">
                <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700">
                  {item.propertyFor ? 'For Sale' : 'Rent'}
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                {/*{item.features.map((label) => (*/}
                {/*  <View*/}
                {/*    key={label}*/}
                {/*    className="self-start rounded-[4px] bg-[#FFFBF9] p-1.5"*/}
                {/*  >*/}
                {/*    <Text className="font-airbnb_md text-[8px] font-medium text-primary-700">*/}
                {/*      {label}*/}
                {/*    </Text>*/}
                {/*  </View>*/}
                {/*))}*/}
              </View>
            </View>
          </ImageBackground>
          <View className="justify-center px-3 pt-3">
            <View className="flex-row items-center gap-10">
              <View className="flex-1">
                <Text
                  className="font-airbnb_bd text-base font-bold text-text-main700"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {item.propertyTitle}
                </Text>
                <View className="mt-0.5 flex-row items-center">
                  <NImage
                    source={require('@assets/icons/location2.png')}
                    className="w-2.5 h-2.5"
                  />
                  <Text
                    className="ml-1 font-airbnb_bk text-[10px] font-normal text-text-600"
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {item.propertyAddress}
                  </Text>
                </View>
              </View>

              <View>
                <Text className="font-airbnb_xbd text-lg font-extrabold text-primary-700 flex flex-row items-center">
                  <FontAwesome5 name="rupee-sign" size={14} color="#c58e00" />
                  {''}
                  {formatPriceAddLabels(
                    item.propertyPrice ? item.propertyPrice?.toString() : 'N/A'
                  )}
                </Text>

                <Text className="font-airbnb_xbd text-sm font-extrabold text-primary-700">
                  {item.areaInSqMeters
                    ? Number(
                        Number(item.areaInSqMeters).toFixed(2)
                      ).toLocaleString()
                    : 'N/A'}
                  /{FormatAreaIn('SQUAREMETER')}
                </Text>
              </View>
            </View>

            <Text
              className="mt-3 font-airbnb_bk text-xs font-normal text-text-600"
              ellipsizeMode="tail"
              numberOfLines={5}
            >
              {item.aboutProperty}
            </Text>

            <View className="flex-row items-center justify-between">
              <Button
                onPress={() => setIsModalVisible(true)}
                loading={isModalVisible}
                className="h-12 w-3/5"
                label={isConnectedAgent ? 'Connected' : 'Contact Agent'}
                disabled={isConnectedAgent || isModalVisible}
              />

              <View className="flex-row items-center">
                <PropertyWishlistBtn
                  propertyId={item.id}
                  isLiked={likedState}
                  bg={true}
                />
                <PropertyShareBtn
                  propertyId={item.id}
                  propertyTitle={item.propertyTitle ?? ''}
                  aboutProperty={item.aboutProperty ?? ''}
                  bg={true}
                />
              </View>
            </View>
          </View>
        </ImageBackground>
      </View>

      {!isConnectedAgent && (
        <SwitchAddAgent
          isVisible={isModalVisible}
          onClose={() => setIsModalVisible(false)}
          currentAgent={connectedAgentData?.agent as AgentProps}
          newAgent={item.user as unknown as AgentProps}
          propertyId={item.id}
          onSuccess={() => {
            utils.invalidate();
          }}
        />
      )}
    </Pressable>
  );
};

export default PropertiesComponent;
