import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Text,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import {
  signedUploadToCloudinary,
  deleteImageFromCloudinary,
} from '../api/cloudinary';
import { MaterialIcons } from '@expo/vector-icons';
import { api } from '@/utils/api';

interface CloudinaryImageUploadProps {
  initialImageUrl?: string;
  initialPublicId?: string;
  initialSelectedImageUri?: string | null;
  onImageUploaded: (imageData: { url: string; publicId: string }) => void;
  folder?: string;
  tags?: string[];
  width?: number;
  height?: number;
  borderRadius?: number;
}

export const CloudinaryImageUpload = forwardRef<
  any,
  CloudinaryImageUploadProps
>(
  (
    {
      initialImageUrl,
      initialPublicId,
      initialSelectedImageUri,
      onImageUploaded,
      folder,
      tags,
      width = 200,
      height = 200,
      borderRadius = 8,
    },
    ref
  ) => {
    const [imageUrl, setImageUrl] = useState<string | undefined>(
      initialImageUrl
    );
    const [publicId, setPublicId] = useState<string | undefined>(
      initialPublicId
    );
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [signature, setSignature] = useState<string | null>(null);

    const { data } = api.cloudinary.generateSignature.useQuery(
      {
        paramsToSign: {
          timestamp: Date.now(),
        },
      },
      {
        enabled: isLoading,
      }
    );
    useEffect(() => {
      if (data?.signature && data.signature.length > 0) {
        console.log('signature', data.signature);
        setSignature(data.signature);
      }
    }, [data]);
    console.log('data', data);
    // Add imperative handle to expose methods to the parent component
    useImperativeHandle(ref, () => ({
      uploadImage,
      removeImage,
    }));

    // Effect to handle initial image upload if provided
    useEffect(() => {
      if (initialSelectedImageUri) {
        console.log(
          '[DEBUG] Initial selected image URI provided, uploading automatically'
        );
        uploadImage(initialSelectedImageUri);
      }
    }, [initialSelectedImageUri]);

    const pickImage = async () => {
      try {
        setError(null);

        // Request permissions
        const permissionResult =
          await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (!permissionResult.granted) {
          setError('Permission to access media library was denied');
          return;
        }

        // Launch image picker
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const selectedAsset = result.assets[0];
          uploadImage(selectedAsset.uri);
        }
      } catch (err) {
        console.error('Error picking image:', err);
        setError('Failed to pick image');
      }
    };

    const takePhoto = async () => {
      try {
        setError(null);

        // Request camera permissions
        const permissionResult =
          await ImagePicker.requestCameraPermissionsAsync();

        if (!permissionResult.granted) {
          setError('Permission to access camera was denied');
          return;
        }

        // Launch camera
        const result = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const selectedAsset = result.assets[0];
          uploadImage(selectedAsset.uri);
        }
      } catch (err) {
        console.error('Error taking photo:', err);
        setError('Failed to take photo');
      }
    };

    const uploadImage = async (uri: string) => {
      try {
        setIsLoading(true);
        console.log('signature', signature);

        // If there's an existing image with a publicId, delete it from Cloudinary
        if (publicId) {
          console.log(
            '[DEBUG] Deleting existing Cloudinary image before upload'
          );
          await deleteImageFromCloudinary(publicId);
        }

        if (!signature) {
          throw new Error('Signature not found');
        }
        // Upload the new image
        const result = await signedUploadToCloudinary(uri, folder, signature);
        console.log('result', result);

        // Update state with new image data
        setImageUrl(result.secure_url);
        setPublicId(result.public_id);

        // Call the callback with the new image data
        onImageUploaded({
          url: result.secure_url,
          publicId: result.public_id,
        });

        setIsLoading(false);
        console.log('image uploaded');
      } catch (err) {
        console.error('Error uploading image:', err);
        setError('Failed to upload image');
        setIsLoading(false);
      }
    };

    const removeImage = async () => {
      try {
        console.log('[DEBUG] removeImage called, publicId:', publicId);

        setIsLoading(true);

        // Only attempt to delete from Cloudinary if we have a publicId
        if (publicId) {
          console.log(
            '[DEBUG] Calling deleteImageFromCloudinary with publicId:',
            publicId
          );
          const deleteResult = await deleteImageFromCloudinary(publicId);
          console.log('[DEBUG] Delete result:', deleteResult);
        } else {
          // Handle S3 image or image without publicId
          console.log(
            '[DEBUG] No Cloudinary publicId available, image might be from S3. Skipping Cloudinary deletion.'
          );
        }

        // Clear the state regardless of image source
        setImageUrl(undefined);
        setPublicId(undefined);

        // Call the callback with empty data to clear image reference in parent component
        console.log('[DEBUG] Calling onImageUploaded with empty data');
        onImageUploaded({
          url: '',
          publicId: '',
        });

        setIsLoading(false);
      } catch (err) {
        console.error('Error removing image:', err);
        console.log('[DEBUG] Delete error details:', JSON.stringify(err));
        setError('Failed to remove image');
        setIsLoading(false);
      }
    };

    return (
      <View style={styles.container}>
        {isLoading ? (
          <View
            style={[styles.imageContainer, { width, height, borderRadius }]}
          >
            <ActivityIndicator size="large" color="#0000ff" />
          </View>
        ) : imageUrl ? (
          <View>
            <Image
              source={{ uri: imageUrl }}
              style={[styles.image, { width, height, borderRadius }]}
            />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => {
                console.log('[DEBUG] Delete button pressed');
                removeImage();
              }}
            >
              <MaterialIcons name="delete" size={24} color="white" />
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity
            style={[styles.placeholder, { width, height, borderRadius }]}
            onPress={pickImage}
          >
            <MaterialIcons name="add-photo-alternate" size={40} color="#888" />
            <Text style={styles.placeholderText}>Add Image</Text>
          </TouchableOpacity>
        )}

        {error && <Text style={styles.errorText}>{error}</Text>}
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    overflow: 'hidden',
  },
  image: {
    resizeMode: 'cover',
  },
  placeholder: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  placeholderText: {
    marginTop: 8,
    color: '#888',
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.7)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3498db',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginHorizontal: 6,
  },
  buttonText: {
    marginLeft: 6,
    color: 'white',
    fontWeight: '500',
  },
  errorText: {
    marginTop: 8,
    color: 'red',
  },
});
