export interface CloudinaryUploadResponse {
  public_id: string;
  secure_url: string;
  asset_id: string;
  version_id: string;
  width: number;
  height: number;
  format: string;
  created_at: string;
  resource_type: string;
  tags?: string[];
  bytes: number;
  url: string;
  etag: string;
  original_filename: string;
}

export interface CloudinaryDeleteResponse {
  result: string; // 'ok' for successful deletion
  error?: {
    message: string;
  };
}
