/* eslint-disable react/react-in-jsx-scope */
import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React, { ReactNode } from 'react';
import { Alert, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as WebBrowser from 'expo-web-browser';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';

import { useAuth } from '@/lib';
import {
  FocusAwareStatusBar,
  Pressable,
  ScrollView,
  Text,
  View,
} from '@/components/ui';
import { api } from '@/utils/api';
import { getToken } from '@/lib/auth/utils';
import { getBaseUrl } from '@/utils/base-url';

type PropsType = {
  border: boolean;
  icon?: string;
  iconSvg?: ReactNode;
  label: string;
  onPress?: () => void;
  classname?: string;
  dangercolor?: string;
};

const SettingOptions: React.FC<PropsType> = ({
  border,
  icon,
  iconSvg,
  label,
  onPress,
  classname,
  dangercolor,
}) => {
  return (
    <Pressable
      className={`mb-2.5 flex-row items-center py-3 ${classname} ${border === true ? 'border-b border-[#CECECE]' : null}`}
      onPress={onPress}
    >
      {iconSvg || icon ? (
        <View className="w-12">
          {iconSvg ? (
            <View className="items-center mr-4">{iconSvg}</View>
          ) : (
            icon && <NImage source={icon} className={'mr-5 h-6 w-6'} />
          )}
        </View>
      ) : (
        <View className="m-0 p-0" />
      )}
      <View className="flex-1 flex-row items-center justify-between">
        <Text
          className={`font-airbnb_md text-base font-medium text-text-500 ${dangercolor}`}
        >
          {label}
        </Text>
        <NImage
          source={require('@assets/icons/arrow.png')}
          className="aspect-square w-4"
          tintColor={'#3A3A3A'}
        />
      </View>
    </Pressable>
  );
};

// eslint-disable-next-line max-lines-per-function

export default function Profile() {
  const { data: profile } = api.user.getProfile.useQuery();
  const signOut = useAuth.use.signOut();
  const insets = useSafeAreaInsets();
  console.log('profile', profile);

  const confirmSignOut = () => {
    Alert.alert('Confirm', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
        onPress: () => {},
      },
      {
        text: 'Yes',
        style: 'destructive',
        onPress: async () => {
          await signOut();
          router.replace('/auth/sign-in');
        },
      },
    ]);
  };

  const confirmDeleteAccount = () => {
    Alert.alert(
      'Confirm',
      'Are you sure you want to request deletion of your account?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {},
        },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: async () => {
            // TODO: delete account
            let result = await WebBrowser.openBrowserAsync(
              getBaseUrl() + '/delete-account'
            );
          },
        },
      ]
    );
  };

  const HandleOnPressPP = async () => {
    // TODO: take to web url of Privacy Policy
    await WebBrowser.openBrowserAsync(getBaseUrl() + '/privacy-policy');
  };
  const HandleOnPressTC = async () => {
    // TODO: take to web url of T&C
    await WebBrowser.openBrowserAsync(getBaseUrl() + '/terms-and-conditions');
  };

  return (
    <View className="flex-1 ">
      {/* <FocusAwareStatusBar /> */}
      {/* Header */}
      <LinearGradient
        colors={['#F04D24', '#C33D12', '#962C00']}
        start={[0, 0]}
        end={[0.7, 0]}
        style={[
          styles.gradient,
          {
            // paddingTop: insets.top
          },
        ]}
      >
        <BlurView
          intensity={50}
          style={[{ height: 96 + insets.top, paddingTop: insets.top }]}
          tint="light"
        >
          <View className="flex-row items-center justify-between px-5 pt-2.5">
            <View className="flex-row items-center">
              <NImage
                source={
                  profile?.cloudinaryImagePublicUrl
                    ? { uri: profile?.cloudinaryImagePublicUrl }
                    : profile?.profileImagePublicUrl
                      ? { uri: profile?.profileImagePublicUrl }
                      : require('@assets/icons/user-defaultimg.jpeg')
                }
                className="mr-3 h-16 w-16 rounded-xl"
              />
              <View>
                <Text className="mb-1 text-xl font-extrabold text-white">
                  {profile?.name}
                </Text>
              </View>
            </View>
            <View>
              <Pressable
                className="self-end"
                onPress={() => router.push('/profile-screens/edit-profile')}
              >
                <NImage
                  source={require('@assets/icons/edit1.png')}
                  className="h-6 w-6"
                />
              </Pressable>
            </View>
          </View>
        </BlurView>
      </LinearGradient>

      {/* subscription */}
      <ScrollView showsVerticalScrollIndicator={false}>
        {/*<Pressable className="px-5 pt-6">*/}
        {/*  <NImage*/}
        {/*    source={require('@assets/images/lol.png')}*/}
        {/*    className="h-[124px] w-full"*/}
        {/*    contentFit="contain"*/}
        {/*  />*/}
        {/*</Pressable>*/}

        <View className="flex-1 px-5 pt-4">
          <Text className="mt-1.5 mb-3 font-airbnb_xbd text-lg font-extrabold text-text-600">
            Settings
          </Text>

          <SettingOptions
            icon={require('@assets/profile-icons/propertyHistory.svg')}
            label={'Property History'}
            onPress={() => router.push('/profile-screens/property-history')}
            border={false}
          />

          <SettingOptions
            icon={require('@assets/profile-icons/reviews.svg')}
            label={'My Reviews'}
            onPress={() => router.push('/profile-screens/my-reviews')}
            border={false}
            classname="border-b border-text-100"
          />

          {/* <SettingOptions
            icon={require('@assets/profile-icons/settings.svg')}
            label="Settings"
            onPress={() => router.push('/profile-screens/settings')}
            border={false}
            classname="border-b border-text-100"
          /> */}

          <Text className="mt-4 pb-3 font-airbnb_xbd text-lg font-extrabold text-text-600">
            Report & Help
          </Text>

          {/* <SettingOptions
            icon={require('@assets/profile-icons/fraud.svg')}
            label={'Report Fraud'}
            onPress={() => router.push('/profile-screens/report-fraud')}
            border={false}
          /> */}

          <SettingOptions
            icon={require('@assets/profile-icons/help.svg')}
            label={'Help Center'}
            onPress={() => router.push('/profile-screens/help-center')}
            border={false}
            classname="border-b border-text-100"
          />

          <Text className="mt-1.5 mb-3 font-airbnb_xbd text-lg font-extrabold text-text-600">
            Referrals & Feedback
          </Text>

          <SettingOptions
            icon={require('@assets/profile-icons/refer.svg')}
            label={'Refer Your Friend'}
            onPress={() => router.push('/profile-screens/refer-friend')}
            border={false}
            classname="border-b border-text-100"
          />

          {/* <SettingOptions
            icon={require('@assets/profile-icons/feedback.svg')}
            label={'Give Us Feedback'}
            onPress={() => router.push('/profile-screens/feedback')}
            border={false}
            classname="border-b border-text-100"
          /> */}

          <Text className="mt-1.5 mb-3 font-airbnb_xbd text-lg font-extrabold text-text-600">
            Terms & Conditions
          </Text>

          <SettingOptions
            icon={require('@assets/profile-icons/termsconditions.svg')}
            label={'Privacy Policy'}
            onPress={() => HandleOnPressPP()}
            border={false}
          />

          <SettingOptions
            icon={require('@assets/profile-icons/termsconditions.svg')}
            label="Terms of Services"
            onPress={() => HandleOnPressTC()}
            border={false}
            classname="border-b border-text-100"
          />

          <SettingOptions
            label="Logout"
            onPress={confirmSignOut}
            border={false}
          />
          <SettingOptions
            label="Delete Account"
            dangercolor="text-red-800"
            onPress={confirmDeleteAccount}
            border={false}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  gradient: {
    // flex: 1,
    // height: 100,
  },
  blurContainer: {
    flex: 1,
    justifyContent: 'center',
  },
});
