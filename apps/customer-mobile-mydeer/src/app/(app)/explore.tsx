import {
  ActivityIndicator,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, { useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import TextInputHeader from '@/components/shared/text-input-header';
import { Image } from 'expo-image';
import { OneSignal } from 'react-native-onesignal';

import { api } from '@/utils/api';
import { Prisma } from '@repo/database';
import { router } from 'expo-router';
import Search from '@/components/shared/search';

type AgentProps = Prisma.UserGetPayload<{
  select: {
    name: true;
    id: true;
    verifiedAgent: true;
    experience: true;
    propertiesSold: true;
    rating: true;
    filePublicUrl: true;
    cloudinaryProfileImageUrl: true;
    latitude: true;
    longitude: true;
    companyDetails: true;
    createdAt: true;
  };
}>;

const AgentCard = ({
  item,
  onPress,
}: {
  item: AgentProps;
  onPress: () => void;
}) => {
  const { data: profile } = api.user.getProfile.useQuery();

  useEffect(() => {
    if (profile?.id) {
      OneSignal.login(profile.id.toString());
    } else {
      OneSignal.logout();
    }
  }, [profile]);
  return (
    <Pressable
      className="mb-4 flex-row items-center rounded-xl border border-[#E9E2DD] px-2.5 py-4"
      onPress={onPress}
    >
      <Image
        source={item.filePublicUrl || require('@assets/icons/profile.png')}
        className="aspect-square w-[48px] rounded-md"
      />

      <View className="flex-1 ml-3">
        <View className="flex-row items-center justify-between">
          <View style={{ width: '50%', justifyContent: 'flex-start' }}>
            <Text
              className="font-airbnb_xbd text-xl font-extrabold text-primary-750"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.name}
            </Text>
          </View>

          <View
            className="flex-row items-center"
            style={{ width: '50%', justifyContent: 'flex-end' }}
          >
            <Image
              source={require('@assets/icons/location5.png')}
              className="h-4 w-4"
            />
            <Text
              className="ml-0.5 font-airbnb_bk text-xs font-normal text-text-600"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.companyDetails?.companyLocation}
            </Text>
          </View>
        </View>

        <View className="flex-row items-center justify-between mt-1">
          <View style={{ width: '50%', justifyContent: 'flex-start' }}>
            <Text
              className="font-airbnb_bk text-sm font-normal text-text-600"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.companyDetails?.companyName}
            </Text>
          </View>

          <View
            className="flex-row items-center"
            style={{ width: '50%', justifyContent: 'flex-end' }}
          >
            <Text
              className="font-airbnb_bk text-[10px] font-normal text-primary-750"
              numberOfLines={1}
            >
              Experience:
            </Text>
            <Text
              className="ml-1 font-airbnb_bd text-[10px] font-normal text-primary-750"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.experience}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const Explore = () => {
  const insets = useSafeAreaInsets();

  return (
    <>
      <View className="bg-[#f9f7f7]" style={{ paddingTop: insets.top + 12 }}>
        {/* Top header */}
        <View className="mb-5">
          <Search />
        </View>
      </View>
    </>
  );
};

export default Explore;

const styles = StyleSheet.create({});
