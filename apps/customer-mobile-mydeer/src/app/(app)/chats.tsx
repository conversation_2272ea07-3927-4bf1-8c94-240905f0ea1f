import React, { useState } from 'react';
import { View } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import ChatPage from '../chat-screens/chats-page';

const Chats = () => {
  const insets = useSafeAreaInsets();
  return (
    <View className="bg-[#f9f7f7]" style={{ paddingTop: insets.top + 12 }}>
      <View className="w-full">
        <ChatPage />
      </View>
    </View>
  );
};

export default Chats;
