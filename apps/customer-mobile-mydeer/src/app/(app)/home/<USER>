import {
  Pressable,
  StyleSheet,
  View,
  ScrollView,
  Text,
  ActivityIndicator,
} from 'react-native';
import React, { useState } from 'react';
import { ImageBackground, Image as NImage } from 'expo-image';
import { BlurView } from 'expo-blur';
import Entypo from '@expo/vector-icons/Entypo';
import { router, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
interface ImageItem {
  id: number;
  source: string;
}

const Gallery = () => {
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();

  const images: ImageItem[] = params.images
    ? (JSON.parse(params.images as string) as ImageItem[])
    : [];

  const [selectedImageId, setSelectedImageId] = useState<number | null>(
    images.length > 0 ? images[0].id : null
  );

  const handleImagePress = (id: number) => {
    setSelectedImageId(id);
  };

  const [showUI, setShowUI] = useState(true);
  const toggleUI = () => {
    setShowUI(!showUI);
  };

  const selectedImage = images.find(
    (img: ImageItem) => img.id === selectedImageId
  );

  // loading for images
  const [isLoading, setIsLoading] = useState<Record<number, boolean>>({});

  return (
    <Pressable onPress={toggleUI} className="h-full w-full">
      <ImageBackground
        className="h-full w-full"
        source={selectedImage && { uri: selectedImage.source }}
        contentFit="contain"
        transition={200}
        onLoadStart={() =>
          setIsLoading((prev) => ({
            ...prev,
            [selectedImageId as number]: true,
          }))
        }
        onLoadEnd={() =>
          setIsLoading((prev) => ({
            ...prev,
            [selectedImageId as number]: false,
          }))
        }
      >
        {isLoading[selectedImageId as number] && (
          <ActivityIndicator
            size="small"
            color="#F04D24"
            className="absolute top-0 bottom-0 left-0 right-0"
          />
        )}
        <View className="h-full w-full justify-between">
          {showUI && (
            <BlurView
              intensity={60}
              className="pt-safe mt-2 pl-2.5 bg-[#F4F0EE80] py-5"
              style={{ paddingTop: insets.top }}
            >
              <Entypo
                name="chevron-small-left"
                size={24}
                color="#3A3A3A"
                onPress={() => router.back()}
              />
            </BlurView>
          )}

          {showUI && (
            <View
              className="mx-5 mb-3 p-4 bg-white rounded-3xl items-center"
              style={styles.cardWithShadow}
            >
              <View className="h-1 w-12 bg-text-500 rounded-full" />
              <Text className="self-start mt-4 mb-2 text-lg font-bold font-airbnb_bd text-text-600">
                Gallery
              </Text>

              {images.length > 0 ? (
                <ScrollView
                  horizontal
                  className="w-full"
                  showsHorizontalScrollIndicator={false}
                >
                  <View className="gap-2.5 flex-row items-center">
                    {images.map((image: ImageItem) => (
                      <Pressable
                        key={image.id}
                        onPress={(e) => {
                          e.stopPropagation();
                          handleImagePress(image.id);
                        }}
                        className={`rounded-[17px] ${
                          selectedImageId === image.id
                            ? 'border-[2.8px] border-primary-700'
                            : ''
                        }`}
                      >
                        <NImage
                          source={{ uri: image.source }}
                          className={`rounded-[12px]
                            ${selectedImageId === image.id ? 'w-[122px] h-[122px]' : 'w-[100px] h-[100px]'}
                          `}
                          contentFit="cover"
                          transition={200}
                          onLoadStart={() =>
                            setIsLoading((prev) => ({
                              ...prev,
                              [image.id]: true,
                            }))
                          }
                          onLoadEnd={() =>
                            setIsLoading((prev) => ({
                              ...prev,
                              [image.id]: false,
                            }))
                          }
                        />
                        {isLoading[image.id] && (
                          <ActivityIndicator
                            size="small"
                            color="#F04D24"
                            className="absolute top-0 bottom-0 left-0 right-0"
                          />
                        )}
                      </Pressable>
                    ))}
                  </View>
                </ScrollView>
              ) : (
                <Text>No images available</Text>
              )}
            </View>
          )}
        </View>
      </ImageBackground>
    </Pressable>
  );
};

export default Gallery;

const styles = StyleSheet.create({
  cardWithShadow: {
    borderRadius: 22,
    backgroundColor: '#FFF',
    shadowColor: 'rgba(41, 39, 58, 0.15)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 54,
    elevation: 15,
  },
});
