import { Link, Redirect, SplashScreen, Tabs, usePathname } from 'expo-router';
import React, { useCallback, useEffect } from 'react';
import { Image as NImage } from 'expo-image';

import { ActivityIndicator, Pressable, Text } from '@/components/ui';
import { useAuth, useIsFirstTime } from '@/lib';
import { api } from '@/utils/api';
import { View, StyleSheet, BackHandler } from 'react-native';

export default function TabLayout() {
  const pathname = usePathname();
  const activeTab = pathname.split('/').pop() || 'home';
  const [isFirstTime, setIsFirstTime] = useIsFirstTime();

  const { data: profile, isLoading, isError } = api.user.getProfile.useQuery();
  const { status, signOut } = useAuth();
  console.log('profile', profile);
  const hideSplash = useCallback(async () => {
    await SplashScreen.hideAsync();
  }, []);

  // useEffect(() => {
  //   if (isFirstTime === false) {
  //     const backAction = () => {
  //       BackHandler.exitApp();
  //       return true;
  //     };

  //     const backHandler = BackHandler.addEventListener(
  //       'hardwareBackPress',
  //       backAction
  //     );

  //     return () => backHandler.remove();
  //   }
  // }, [isFirstTime]);

  if (isError) {
    signOut();
    return <Redirect href="/auth/sign-in" />;
  }
  if (!isLoading && !profile?.onboardingStatus) {
    return <Redirect href="/home-screens/step1" />;
  }
  if (status === 'signOut') {
    return <Redirect href="/auth/sign-in" />;
  }

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#F04D24" />
      </View>
    );
  }

  return (
    <Tabs
      screenOptions={{
        tabBarStyle: styles.bottomTabStyles,
        tabBarActiveTintColor: '#F04D24',
        tabBarInactiveTintColor: '#525252',
        tabBarHideOnKeyboard: true,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor: activeTab === 'home' ? '#fff9f8' : 'transparent',
          },

          tabBarLabelStyle: {
            fontSize: 14,
            fontWeight: activeTab === 'home' ? '500' : '400',
            color: activeTab === 'home' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('@assets/icons/home.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="explore"
        options={{
          title: 'Explore',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor:
              activeTab === 'explore' ? '#fff9f8' : 'transparent',
          },

          tabBarLabelStyle: {
            fontSize: 14,
            fontWeight: activeTab === 'explore' ? '500' : '400',
            color: activeTab === 'explore' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('@assets/icons/explore.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="chats"
        options={{
          title: 'Chats',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor: activeTab === 'chats' ? '#fff9f8' : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 14,
            fontWeight: activeTab === 'chats' ? '500' : '400',
            color: activeTab === 'chats' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('@assets/icons/chats.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="favourites"
        options={{
          title: 'Favorites',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor:
              activeTab === 'Properties' || activeTab === 'Agents'
                ? '#fff9f8'
                : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 14,
            fontWeight:
              activeTab === 'Properties' || activeTab === 'Agents'
                ? '500'
                : '400',
            color:
              activeTab === 'Properties' || activeTab === 'Agents'
                ? '#F04D24'
                : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('@assets/icons/favourites.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          headerShown: false,
          tabBarItemStyle: {
            borderRadius: 12,
            margin: 5,
            backgroundColor:
              activeTab === 'profile' ? '#fff9f8' : 'transparent',
          },
          tabBarLabelStyle: {
            fontSize: 14,
            fontWeight: activeTab === 'profile' ? '500' : '400',
            color: activeTab === 'profile' ? '#F04D24' : '#525252',
          },
          tabBarIcon: ({ color }) => (
            <NImage
              source={require('@assets/icons/profile.png')}
              className="aspect-square h-5"
              contentFit="contain"
              tintColor={color}
            />
          ),
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  bottomTabStyles: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    backgroundColor: '#FFF',
    paddingHorizontal: 20,
    // For iOS shadow
    shadowColor: 'rgba(39, 39, 39, 0.1)',
    shadowOffset: {
      width: 0,
      height: -1,
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    // For Android shadow
    elevation: 10,
  },
});
