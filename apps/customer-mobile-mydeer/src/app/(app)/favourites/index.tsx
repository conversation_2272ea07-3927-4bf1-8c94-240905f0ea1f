import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import React from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Agents from './agents';
import Properties from './properties';

const { height, width } = Dimensions.get('screen');

const Tab = createMaterialTopTabNavigator();

const initialLayout = {
  width: Dimensions.get('window').width,
};

// Define the props interface for TabLabel
interface TabLabelProps {
  focused: boolean;
  title: string;
}

// Custom tab label component
const TabLabel: React.FC<TabLabelProps> = ({ focused, title }) => {
  return (
    <Text style={focused ? styles.activeLabel : styles.inactiveLabel}>
      {title}
    </Text>
  );
};

const CustomTabBarIndicator = ({ layout, navigation }: any) => {
  const activeIndex = navigation.getState().index; // Get the active tab index
  const tabWidth = layout.width / 2; // Assuming two tabs
  const indicatorPercentage = 0.55; // Control the width of the indicator as 30%
  const indicatorWidth = tabWidth * indicatorPercentage; // Calculate the width based on percentage

  // Calculate the left position for centering the indicator
  const leftPosition = activeIndex * tabWidth + (tabWidth - indicatorWidth) / 2; //Two tabs so

  return (
    <View style={styles.tabIndicatorContainer}>
      <View
        style={[
          styles.tabIndicator,
          { left: leftPosition, width: indicatorWidth },
        ]}
      />
    </View>
  );
};

const CustomTabBar = (props: {
  state: { routes: { name: string }[]; index: number };
  layout: any;
  navigation: any;
}) => {
  return (
    <View style={styles.tabBar}>
      {props.state.routes.map((route, index) => {
        const isFocused = index === props.state.index;

        return (
          <TouchableOpacity
            style={styles.tabItem}
            key={index}
            onPress={() => {
              props.navigation.navigate(route.name);
            }}
          >
            <TabLabel focused={isFocused} title={route.name} />
          </TouchableOpacity>
        );
      })}
      <CustomTabBarIndicator
        layout={props.layout}
        navigation={props.navigation}
      />
    </View>
  );
};

export default function FavouritesTopTabs() {
  const insets = useSafeAreaInsets();
  
  return (
    <View style={{ flex: 1, paddingTop: insets.top }}>
      <Tab.Navigator
        tabBar={(props) => <CustomTabBar {...props} />}
        initialLayout={initialLayout}
        screenOptions={{
          tabBarStyle: { marginTop: 0 }
        }}
      >
        <Tab.Screen
          name="Properties"
          component={Properties}
          options={{ tabBarLabel: 'Properties' }}
        />
        <Tab.Screen
          name="Agents"
          component={ Agents}
          options={{ tabBarLabel: 'Agents' }}
        />
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: 'rgba(255, 255, 255, 0.50)', // Tab bar background color
    height: height * 0.06, // Height of the tab bar
    position: 'relative',
    flexDirection: 'row',
    // marginHorizontal:20
  },
  activeLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'medium',
    fontFamily: 'AirbnbW_Md',
    color: '#252525',
  },
  inactiveLabel: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'normal',
    fontFamily: 'AirbnbW_Bk',
    color: '#252525',
  },
  tabItem: {
    flex: 1, // Each tab item takes equal space
    alignItems: 'center', // Center content
    justifyContent: 'center',
  },
  tabIndicatorContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 2,
    // backgroundColor: '#FDECC1', // Full background color
  },
  tabIndicator: {
    backgroundColor: '#C58E00', // Active tab indicator color
    height: '100%',
    position: 'absolute',
    bottom: 5,
  },
});
