import { Text, View } from 'react-native';
import React from 'react';

import { FlashList } from '@shopify/flash-list';
import { ScrollView, RefreshControl } from 'react-native';
import { useRouter } from 'expo-router';

import { api } from '@/utils/api';
import PropertiesComponent from '@/components/routes/properties-component/properties-component';

const Properties = () => {
  const {
    data: favouriteProperties,
    isLoading: isLoadingGetLikedProperties,
    refetch,
  } = api.user.getFavouritesProperties.useQuery();
  const router = useRouter();

  function handlePress(id: string): void {
    router.navigate({
      pathname: `/property-routes/[propertyId]`,
      params: { propertyId: id },
    });
  }

  if (!favouriteProperties?.length) {
    return (
      <ScrollView
        className="flex-1 h-full px-5"
        contentContainerStyle={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingGetLikedProperties}
            onRefresh={refetch}
          />
        }
      >
        <View>
          <Text className="text-xl font-semi-bold">
            Your favourite properties will appear here
          </Text>
        </View>
      </ScrollView>
    );
  }

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#f9f7f7',
      }}
      className="flex-1"
    >
      <ScrollView
        className="flex-1 px-5"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingGetLikedProperties}
            onRefresh={refetch}
          />
        }
      >
        <FlashList
          showsVerticalScrollIndicator={false}
          data={favouriteProperties}
          estimatedItemSize={210}
          keyExtractor={(item) => item.id.toString()}
          className="flex-1 pb-5"
          renderItem={({ item }) => {
            return (
              <PropertiesComponent
                item={item.property}
                handlePress={() => handlePress(item.propertyId)}
                isLiked={item.property.customerFavourites?.length > 0}
              />
            );
          }}
        />
      </ScrollView>
    </View>
  );
};

export default Properties;
