import { View, Text, ScrollView, RefreshControl, FlatList } from 'react-native';
import React from 'react';
import { api } from '@/utils/api';
import { AgentCard } from '@/components/routes/agents-component/agent-card';
import { FlashList } from '@shopify/flash-list';

const Agents = () => {
  const {
    data: favouriteAgents,
    isLoading: isLoadingGetLikedAgents,
    refetch,
  } = api.likeAgent.getFavouritesAgents.useQuery();

  if (!favouriteAgents?.length) {
    return (
      <ScrollView
        className="flex-1 h-full px-5"
        contentContainerStyle={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingGetLikedAgents}
            onRefresh={refetch}
          />
        }
      >
        <View>
          <Text className="text-xl font-semi-bold">
            Your favourite agents will appear here
          </Text>
        </View>
      </ScrollView>
    );
  }

  // const agentsData = favouriteAgents.map((p) => p.likedAgent) as AgentProps[];

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#f9f7f7',
      }}
      className="flex-1"
    >
      <FlashList
        data={favouriteAgents}
        numColumns={2}
        contentContainerStyle={{
          padding: 20,
        }}
        estimatedItemSize={200}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingGetLikedAgents}
            onRefresh={refetch}
          />
        }
        renderItem={({ item }) => (
          <View style={{ width: '48%', marginBottom: 16 }}>
            <AgentCard agent={item.likedAgent} />
          </View>
        )}
        keyExtractor={(item) => item.likedAgentId}
      />
    </View>
  );
};

export default Agents;
