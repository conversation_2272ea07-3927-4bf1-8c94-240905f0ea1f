import { useAuth, useIsFirstTime } from '@/lib';
import { api } from '@/utils/api';
import { Redirect } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { checkUpdateStatus } from '@/utils/version-check';
import UpdatePrompt from '@/components/update-prompt';
import { VERSION_CONFIG } from '@/config/version-config';
import { ActivityIndicator, View } from 'react-native';

const index = () => {
  const { status } = useAuth();
  const [isFirstTime, setIsFirstTime] = useIsFirstTime();
  // first time after sign in so go to onboarding
  const { data: user, isLoading, error } = api.user.getProfile.useQuery();
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<{
    currentVersion: string;
    minimumVersion: string;
  } | null>(null);

  // Check for minimum version requirement
  useEffect(() => {
    if (VERSION_CONFIG.SETTINGS.CHECK_ON_START) {
      // Use user's minimum version requirement if available, otherwise use config
      const minimumRequiredVersion = user?.minAppVersion ?? VERSION_CONFIG.MINIMUM_REQUIRED_VERSION;
      
      const status = checkUpdateStatus(minimumRequiredVersion);
      
      if (status.needsUpdate) {
        setUpdateInfo({
          currentVersion: status.currentVersion,
          minimumVersion: status.minimumVersion,
        });
        setShowUpdatePrompt(true);
      }
    }
  }, [user]);

  // Show update prompt if version is below minimum
  if (showUpdatePrompt && updateInfo) {
    return (
      <UpdatePrompt
        currentVersion={updateInfo.currentVersion}
        minimumVersion={updateInfo.minimumVersion}
        onDismiss={VERSION_CONFIG.SETTINGS.BLOCK_APP_USAGE ? undefined : () => setShowUpdatePrompt(false)}
      />
    );
  }

  // Show loading state while checking auth and loading user data
  if (isLoading || status === 'idle') {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size={40} color="#d6330a" />
      </View>
    );
  }

  // If not authenticated or error loading user, go to onboarding
  if (status === 'signOut' || error || !user) {
    return <Redirect href="/auth/onboarding" />;
  }

  if (!user.onboardingStatus) {
    return <Redirect href="/auth/onboarding" />;
  }

  return <Redirect href="/home" />;
};

export default index;
