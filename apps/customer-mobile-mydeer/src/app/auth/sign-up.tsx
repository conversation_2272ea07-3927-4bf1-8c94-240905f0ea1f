import Entypo from '@expo/vector-icons/Entypo';
import { useRouter, useNavigation } from 'expo-router';
import React from 'react';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import type { SignUpFormProps } from '@/components/routes/auth-components/sign-up-form';
import { SignUpForm } from '@/components/routes/auth-components/sign-up-form';
import { useAuth } from '@/lib';

import { Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function SignUp() {
  const router = useRouter();
  const navigation = useNavigation();
  const signIn = useAuth.use.signIn();

  const onSubmit: SignUpFormProps['onSubmit'] = (data) => {
    //data);
    // signIn({ access: 'access-token', refresh: 'refresh-token' });
    // router.push('/auth/otp');
  };

  const insets = useSafeAreaInsets();
  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView
        style={{ flex: 1, paddingTop: insets.top + 16 }}
        edges={['bottom']}
      >
        <Pressable
          onPress={() => router.replace('/auth/onboarding')}
          className="px-5 flex-row items-center justify-between"
        >
          <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
        </Pressable>

        <SignUpForm onSubmit={onSubmit} />
      </SafeAreaView>
    </LinearGradient>
  );
}
