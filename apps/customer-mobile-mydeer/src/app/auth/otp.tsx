import Entypo from '@expo/vector-icons/Entypo';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useRouter, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Dimensions,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { OtpInput } from 'react-native-otp-entry';
import { SafeAreaView } from 'react-native-safe-area-context';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import { setToken } from '@/lib/auth/utils';
import Toast from 'react-native-toast-message';
import { signIn } from '@/lib';
import { Button } from '@/components/ui';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
const { width } = Dimensions.get('screen');

// eslint-disable-next-line max-lines-per-function
export default function OTP() {
  const utils = api.useUtils();
  const navigation = useNavigation();
  const params = useLocalSearchParams();
  const [otp, setOtp] = useState('');
  const router = useRouter();
  const [error, setError] = useState('');
  const [timer, setTimer] = useState(0);
  const verifyOtpMutation = api.user.verifyOtp.useMutation();
  const resendOtpMutation = api.user.resendOtp.useMutation();

  const handleSubmit = () => {
    if (otp.length === 4) {
      //`Submitted OTP: ${otp}`);
      verifyOtpMutation.mutate(
        { phoneNumber: params.phoneNumber as string, otp },
        {
          onSuccess: async (resp) => {
            if (resp && resp.jwtToken) {
              await setToken(resp.jwtToken);
              signIn(resp.jwtToken);
              await utils.invalidate();
              router.replace('/home-screens/step1');
            } else {
              showMessage({
                message: 'Invalid OTP',
                type: 'danger',
              });
            }
          },
          onError: (err) => {
            //'err', err);
            showMessage({
              message: 'Invalid OTP',
              type: 'danger',
            });
            // router.push('/sign-up');
          },
        }
      );
      // router.push('/home-screens/step1');
    } else {
      setError('Enter 4 digits OTP');
      //'OTP must be 4 digits');
    }
  };

  const handleResend = () => {
    resendOtpMutation.mutate(
      {
        phoneNumber: params.phoneNumber as string,
      },
      {
        onSuccess: () => {
          Toast.show({
            text1: 'OTP sent successfully',
            type: 'success',
          });
        },
        onError: (err) => {
          Toast.show({
            text1: 'Error in sending OTP',
            type: 'error',
          });
        },
      }
    );
    setTimer(30);
    //'Resending OTP...');

    const countdown = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(countdown);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    return () => clearInterval(timer);
  }, [timer]);

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <KeyboardAvoidingView behavior="padding" style={{ flex: 1 }}>
        <SafeAreaView>
          <View className="p-5">
            <View className="flex-row items-center justify-between">
              <Pressable onPress={() => navigation.goBack()}>
                <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
              </Pressable>
              <Pressable onPress={() => navigation.goBack()}>
                <Text className="font-airbnb_md text-base font-medium text-text-500">
                  Change Number
                </Text>
              </Pressable>
            </View>

            <View className="mt-9 items-center">
              <Text
                testID="form-title"
                className="mb-2 font-airbnb_xbd text-2xl font-extrabold text-secondary-650"
              >
                Enter Verification Code
              </Text>
              <Text className="mb-5 text-center font-airbnb_bk text-base font-normal text-text-550">
                enter the 4 digit otp that has been sent to your phone number
                +91
                {params.phoneNumber} via message or sms
              </Text>
            </View>

            <View className="mt-5">
              <OtpInput
                numberOfDigits={4}
                focusColor="#C58E00"
                focusStickBlinkingDuration={500}
                onTextChange={(text) => {
                  setOtp(text);
                  if (text.length === 4) setError('');
                }}
                onFilled={(text) => setOtp(text)}
                type="numeric"
                textInputProps={{
                  accessibilityLabel: 'One-Time Password',
                }}
                theme={{
                  containerStyle: styles.container,
                  pinCodeContainerStyle: styles.pinCodeContainer,
                  pinCodeTextStyle: styles.pinCodeText,
                  focusStickStyle: styles.focusStick,
                  focusedPinCodeContainerStyle: styles.activePinCodeContainer,
                }}
              />
              {error ? (
                <View className="mt-5 items-center">
                  <Text className="text-red-500 ">{error}</Text>
                </View>
              ) : null}
              <View className="mt-5 items-center">
                <Text className="items-center font-airbnb_md text-base font-medium text-secondary-650">
                  {timer > 0 ? `Resend in ${timer}s` : null}
                </Text>
              </View>
            </View>

            <View className="mt-16">
              <Button
                label="Resend"
                variant="otp"
                disabled={timer > 0}
                onPress={timer > 0 ? null : handleResend}
                loading={resendOtpMutation.isPending}
              />
              <Button
                label="Verify"
                loading={verifyOtpMutation.isPending}
                onPress={() => handleSubmit()}
              />
            </View>
          </View>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: { paddingHorizontal: width * 0.125 },
  pinCodeContainer: {
    borderColor: '#ECE9E8',
    borderRadius: 12,
    // padding: 30,
    borderWidth: 2,
  },
  pinCodeText: {
    fontSize: 20,
    fontWeight: '400',
    color: '#252525',
    fontFamily: 'AirbnbW_Bk',
  },
  focusStick: {},
  activePinCodeContainer: {
    backgroundColor: '#FFFFFF',
  },
});
