import { Image as NImage } from 'expo-image';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { StatusBar, Text, View, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import OnboardingComponent from '../../components/routes/auth-components/onboarding-component';
// import { useIsFirstTime } from '@/lib';
import { Button } from '@/components/ui';
import ArrowIcon from '@assets/icons/arrow.png';

export default function Onboarding() {
  const [currentStep, setCurrentStep] = useState(0);
  // const [_, setIsFirstTime] = useIsFirstTime();
  const router = useRouter();

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % onboardingData.length);
  };

  return (
    <SafeAreaView className="flex-1">
      <StatusBar barStyle="dark-content" backgroundColor="#FFFAF9" />
      <View className="h-full w-full px-5">
        <View className="flex-1 justify-between">
          {/* Header */}
          <View className="w-full flex-row items-center justify-between py-4">
            <NImage
              source={require('@assets/images/mydeer1.png')}
              style={{ width: 86.5, height: 51.75 }}
              contentFit="contain"
            />
            <Pressable
              className="rounded-xl bg-[#FFF9F5] px-6 py-3.5"
              onPress={nextStep}
            >
              <NImage
                source={ArrowIcon}
                contentFit="contain"
                className="h-7 w-7"
                tintColor={'#1E1E1E'}
              />
            </Pressable>
          </View>

          {/* Content - Give it flex-1 to take available space */}
          <View className="flex-1 justify-center">
            <OnboardingComponent
              label={onboardingData[currentStep].label}
              text={onboardingData[currentStep].text}
              image={onboardingData[currentStep].image}
            />
          </View>

          {/* Footer */}
          <View className="py-4">
            {/* <SocialSignUp
              social={'Apple'}
              imageSource={require('@assets/icons/social/apple.png')}
            />
            <SocialSignUp
              social={'Google'}
              imageSource={require('@assets/icons/social/google.png')}
            /> */}
            <Button
              variant="onboarding"
              size="onboarding"
              fullWidth={true}
              label="Create Account"
              onPress={() => {
                // setIsFirstTime(false);
                // router.replace('/auth/sign-up');
                router.push('/auth/sign-up');
              }}
            />
            <View className="mt-2 flex-row items-center justify-center gap-4">
              <Text className="font-airbnb_bk text-base font-normal text-text-main700 w-auto">
                Already have a account?
              </Text>
              <Pressable
                className="flex-row items-center"
                onPress={() => router.push('/auth/sign-in')}
              >
                <Text className="font-airbnb_md text-base font-medium text-secondary-650 w-auto">
                  Login
                </Text>
                <NImage
                  source={ArrowIcon}
                  className="aspect-square w-4"
                  tintColor={'#F15F3A'}
                />
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const onboardingData = [
  {
    label: 'Sell, Buy, Rent',
    text: 'Connect, Collaborate & Conquer',
    image: require('@assets/images/illustration.png'),
  },
  {
    label: 'Trusted  Partners',
    text: 'Authentic, Secure and Fair Build your profile and connect with Verified Brokers',
    image: require('@assets/images/business.png'),
  },
  {
    label: 'Unlimited Free Listings & \nNationwide Reach',
    text: 'Connect with a click & List properties at no Cost',
    image: require('@assets/images/pricecuate.png'),
  },
];
