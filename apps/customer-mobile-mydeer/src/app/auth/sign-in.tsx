import { LinearGradient } from 'expo-linear-gradient';
import Entypo from '@expo/vector-icons/Entypo';
import { useRouter } from 'expo-router';
import React from 'react';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { Pressable, StyleSheet, View } from 'react-native';

import type { SignInFormProps } from '@/components/routes/auth-components/sign-in-form';
import { SignInForm } from '@/components/routes/auth-components/sign-in-form';
import { Button } from '@/components/ui';

export default function SignIn() {
  const router = useRouter();

  // Update the onSubmit handler to match the correct type
  const onSubmit: SignInFormProps['onSubmit'] = (data) => {
    //data);
    router.push('/auth/otp');
  };
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <SafeAreaView
        style={{ flex: 1, paddingTop: insets.top + 16 }}
        edges={['bottom']}
      >
        <Pressable
          onPress={() => router.replace('/auth/onboarding')}
          className="px-5 flex-row items-center justify-between "
        >
          <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
        </Pressable>
        <SignInForm onSubmit={onSubmit} />
      </SafeAreaView>
    </LinearGradient>
  );
}

// StyleSheet for better performance
const styles = StyleSheet.create({
  gradient: {
    flex: 1,
  },
});
