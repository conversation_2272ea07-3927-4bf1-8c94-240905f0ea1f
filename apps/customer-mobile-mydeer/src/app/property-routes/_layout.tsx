import { router, Stack } from 'expo-router';
import React from 'react';
import { Pressable } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';

// import { StatusBar } from 'react-native';

export default function Layout() {
  return (
    <Stack>
      <Stack.Screen
        name="[propertyId]"
        options={{
          headerTintColor: '#F04D24',
          title: '',
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
    </Stack>
  );
}
