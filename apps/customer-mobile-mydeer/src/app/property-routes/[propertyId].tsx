import { View, Text, ActivityIndicator } from 'react-native';
import React from 'react';
import { api } from '@/utils/api';
import { router, useLocalSearchParams } from 'expo-router';
import { PropertyDetailsPage } from '@/components/routes/property-details-page-component/property-details-page';
import { SafeAreaView } from 'react-native-safe-area-context';
import { PropertyDetailsProps } from '@/types';
const PropertyDetails = ({}) => {
  const { propertyId } = useLocalSearchParams<{ propertyId: string }>();

  const {
    data: propertyData,
    isLoading: isLoadingPropertyData,
    isError: isErrorPropertyData,
  } = api.property.getPropertyDetails.useQuery({
    id: propertyId,
  });

  const { mutate: addToViewingHistory } =
    api.user.addPropertyToCustomerViewingHistory.useMutation();

  const addToHistory = (propertyId: string) => {
    addToViewingHistory({ propertyId: propertyId });
  };

  if (isLoadingPropertyData) {
    return (
      <View className="flex-1 w-full justify-center items-center">
        <ActivityIndicator size={40} color="#f04d24" />
      </View>
    );
  } else if (isErrorPropertyData || !propertyData) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text>Error loading Property Data</Text>
      </View>
    );
  }

  return (
    <SafeAreaView edges={['bottom']}>
      <View className="px-1">
        <PropertyDetailsPage
          item={propertyData as PropertyDetailsProps}
          addToUserHistory={() => addToHistory(propertyId)}
        />
      </View>
    </SafeAreaView>
  );
};

export default PropertyDetails;
