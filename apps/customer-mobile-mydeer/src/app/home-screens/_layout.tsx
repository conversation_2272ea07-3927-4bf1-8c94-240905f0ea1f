/* eslint-disable max-lines-per-function */
import { useAuth } from '@/lib';
import { api } from '@/utils/api';
import Entypo from '@expo/vector-icons/Entypo';
import { Redirect, Stack, useNavigation } from 'expo-router';
import React from 'react';
import { Pressable, Text } from 'react-native';

export default function Layout() {
  const navigation = useNavigation();
  const { status } = useAuth();
  if (status === 'signOut') {
    return <Redirect href="/auth/sign-in" />;
  }
  const { data } = api.user.getProfile.useQuery();
  if (data?.onboardingStatus === true) {
    return <Redirect href="/home" />;
  }
  return (
    <Stack>
      <Stack.Screen
        name="news"
        options={{
          title: 'News',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />

      <Stack.Screen
        name="notification"
        options={{
          title: 'Notification',
          headerTintColor: '#F04D24',
          headerTitleAlign: 'center',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerLeft: () => (
            <Pressable
              onPress={() => navigation.goBack()}
              className="p-3 rounded-lg"
            >
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
          // headerRight: () => (
          //   <Pressable>
          //     <Text className="font-airbnb_md text-sm font-medium text-primary-700">
          //       Mark all as read
          //     </Text>
          //   </Pressable>
          // ),
        }}
      />
      <Stack.Screen name="auto-complete" options={{ headerShown: false }} />
      <Stack.Screen name="property-details" options={{ headerShown: false }} />
      <Stack.Screen name="step1" options={{ headerShown: false }} />
      <Stack.Screen name="step2" options={{ headerShown: false }} />
      <Stack.Screen name="step3" options={{ headerShown: false }} />
    </Stack>
  );
}
