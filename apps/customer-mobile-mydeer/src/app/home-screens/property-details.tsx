/* eslint-disable max-lines-per-function */
import Entypo from '@expo/vector-icons/Entypo';
import { BlurView } from 'expo-blur';
import { Image as NImage } from 'expo-image';
import { useNavigation } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
// import MapView from 'react-native-maps';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button } from '@/components/ui';
const { height, width } = Dimensions.get('screen');

interface FacilitiesProps {
  onPress: () => void;
}

const facilitiessource = [
  require('@assets/icons/car.png'),
  require('@assets/icons/swimming.png'),
  require('@assets/icons/gym.png'),
  require('@assets/icons/restaurant.png'),
  require('@assets/icons/wifi.png'),
  require('@assets/icons/pet.png'),
  require('@assets/icons/sport.png'),
  require('@assets/icons/laundry.png'),
];

//Facilities Component
const Facilities: React.FC<FacilitiesProps> = ({ onPress }) => {
  const options = [
    { label: 'Car Parking', icon: facilitiessource[0] },
    { label: 'Swimming Pool', icon: facilitiessource[1] },
    { label: 'Gym & Fitness', icon: facilitiessource[2] },
    { label: 'Restaurant', icon: facilitiessource[3] },
    { label: 'Wifi', icon: facilitiessource[4] },
    { label: 'Pet Center', icon: facilitiessource[5] },
    { label: 'Sport Center', icon: facilitiessource[6] },
    { label: 'Laundry', icon: facilitiessource[7] },
  ];

  return (
    <View className="flex-row flex-wrap justify gap-2.5">
      {options.map(({ label, icon }) => (
        <View className="mb-4 items-center w-20">
          <Pressable
            key={label}
            onPress={onPress}
            className={` self-center rounded-full bg-[#DED3CD4D] p-4`}
          >
            <NImage source={icon} className="h-6 w-6" />
          </Pressable>
          <Text className={`font-airbnb_bk text-xs font-normal text-center`}>
            {label}
          </Text>
        </View>
      ))}
    </View>
  );
};

interface OptionsProps {
  options: string[];
}

//Options Component
const Options: React.FC<OptionsProps> = ({ options }) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  return (
    <View className="flex-row flex-wrap items-center">
      {options.map((option) => (
        <Pressable
          key={option}
          onPress={() => setSelectedOption(option)}
          className={`mb-2 mr-3 self-start rounded-xl border border-primary-0 px-2 py-2.5 ${
            selectedOption === option ? 'bg-secondary-main700' : 'bg-white'
          }`}
        >
          <Text
            className={`font-airbnb_bk text-xs font-normal ${
              selectedOption === option ? 'text-primary-50' : 'text-text-600'
            }`}
          >
            {option}
          </Text>
        </Pressable>
      ))}
    </View>
  );
};

// heading and location component
interface PropsType {
  title: string;
}

const Heading: React.FC<PropsType> = ({ title }) => (
  <Text className="mb-2 font-airbnb_bd text-base font-bold text-text-600">
    {title}
  </Text>
);

const Location: React.FC<PropsType> = ({ title }) => (
  <View className="flex-row items-center">
    <NImage
      source={require('@assets/icons/location2.png')}
      className="mr-1 h-4 w-4"
    />
    <Text className="font-airbnb_bk text-sm font-normal text-text-600">
      {title}
    </Text>
  </View>
);

const source = [
  require('@assets/images/1.png'),
  require('@assets/images/2.png'),
  require('@assets/images/3.png'),
  require('@assets/images/3.png'),
  require('@assets/images/3.png'),
];

const comments = [
  {
    name: 'Guy Hawkins',
    time: '1 day ago',
    avatar: require('@assets/icons/randomicon.png'),
  },
  {
    name: 'Jane Doe',
    time: '2 days ago',
    avatar: require('@assets/icons/randomicon.png'),
  },
  {
    name: 'John Smith',
    time: '3 days ago',
    avatar: require('@assets/icons/randomicon.png'),
  },
];

const PropertyDetails = () => {
  const navigation = useNavigation();
  return (
    <SafeAreaView>
      <View className="h-full w-full px-5">
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Top header */}
          <View className="mt-2.5 flex-row items-center justify-between px-5">
            <Pressable onPress={() => navigation.goBack()}>
              <Entypo name="chevron-thin-left" size={16} color="#451F0A" />
            </Pressable>
            <Pressable>
              <NImage
                source={require('@assets/icons/dot3.png')}
                className="h-8 w-8"
              />
            </Pressable>
          </View>

          {/* Details blur view */}
          <View className="rounded-2xl">
            <BlurView
              intensity={80}
              className="mt-4 rounded-2xl px-4 pb-4 pt-6"
              style={{
                backgroundColor: '#FFF8F4',
                borderRadius: 16,
                borderWidth: 0.1,
              }}
            >
              {/* Top Part */}
              <View className="flex-row items-center justify-between">
                <View>
                  <Text className="font-airbnb_bd text-xl font-bold text-text-main700">
                    Platina House
                  </Text>
                  <Location title="Delhi, Sector 48, NCR" />
                </View>

                <View className="flex-row items-center">
                  <Pressable className="mr-4 rounded-lg bg-white p-2.5">
                    <NImage
                      source={require('@assets/icons/like.png')}
                      className="h-5 w-5"
                    />
                  </Pressable>
                  <Pressable className="rounded-lg bg-white p-2.5">
                    <NImage
                      source={require('@assets/icons/share.png')}
                      className="h-5 w-5"
                    />
                  </Pressable>
                </View>
              </View>

              {/* Middle part */}
              <View className="my-3 flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <NImage
                    source={require('@assets/icons/yostar.png')}
                    className="mr-2 h-4 w-4"
                  />
                  <View className="flex-row items-center">
                    <Text className="mr-1 font-airbnb_bd text-sm font-bold text-text-600">
                      4.8
                    </Text>
                    <Text className="font-airbnb_bk text-sm font-normal text-text-400">
                      (335)
                    </Text>
                  </View>
                </View>

                <View>
                  <Text className="font-airbnb_md text-sm font-medium text-text-500">
                    212 reviews
                  </Text>
                </View>
              </View>

              {/* Bottom text part */}
              <View className="mb-4">
                <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                  This upscale hill in a contemporary high-rise is a 4-minute
                  walk from Surfers Paradise Beach...
                </Text>
                <Pressable>
                  <Text className="font-airbnb_bd text-sm font-bold text-secondary-main700 ">
                    Read more
                  </Text>
                </Pressable>
              </View>

              <Heading title="Gallery" />
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View className="flex-row">
                  {source.map((image, index) => (
                    <NImage
                      key={index}
                      source={image}
                      className="mr-1.5 h-32 w-32 rounded-xl"
                    />
                  ))}
                </View>
              </ScrollView>
            </BlurView>
          </View>

          {/* Map */}
          <View className="mt-8 flex-row items-center justify-between">
            <Heading title="Location" />
            <Location title="Delhi, Sector 48, NCR" />
          </View>
          {/* Map View */}
          {/* <View className="mt-3 items-center justify-center">
            <MapView
              style={{
                height: height * 0.1857,
                width: width * 0.928,
                borderRadius: 16,
              }}
              userInterfaceStyle="light"
              initialRegion={{
                latitude: 37.78825,
                longitude: -122.4324,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              }}
            />
          </View> */}
          <View className="mb-6 mt-3 flex-row items-center gap-2">
            <Pressable
              className={
                ' flex-row items-center self-start rounded-xl border border-primary-0 bg-white px-2 py-2.5'
              }
            >
              <NImage
                source={require('@assets/icons/metro.png')}
                className="mr-2 h-3 w-3"
              />
              <Text
                className={'font-airbnb_md text-xs font-medium text-text-600'}
              >
                Metro:{' '}
              </Text>
              <Text
                className={'font-airbnb_bk text-xs font-normal text-text-600'}
              >
                3km away from metro state
              </Text>
            </Pressable>
            <Pressable
              className={
                ' flex-row items-center self-start rounded-xl border border-primary-0 bg-white px-2 py-2.5'
              }
            >
              <NImage
                source={require('@assets/icons/metro.png')}
                className="mr-2 h-3 w-3"
              />
              <Text
                className={'font-airbnb_md text-xs font-medium text-text-600'}
              >
                Mall:{' '}
              </Text>
              <Text
                className={'font-airbnb_bk text-xs font-normal text-text-600'}
              >
                3km walking
              </Text>
            </Pressable>
          </View>

          {/* Property section */}
          <View className="gap-3">
            <Heading title="About the property" />
            <Options
              options={[
                'Hospital',
                'Petrol Pump',
                'Mall',
                'Gym',
                'Restaurant',
                'ATM',
              ]}
            />
            <Text className="font-airbnb_bk text-sm font-normal text-text-main700">
              Spacious 1 bedroom 1 bathroom unit with open floor plan with
              natural light. Located on the 3rd floor of an elevator building,
              sliding doors lead to a balcony with a beautiful view. Large
              eat-in kitchen, living room and dining room. In-unit washer and
              dryer
            </Text>
            <Pressable>
              <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">
                See All
              </Text>
            </Pressable>
          </View>

          {/* Facilities */}
          <View className="mt-2.5">
            <Heading title="Facilities" />
            <View>
              <Facilities
                onPress={() => Alert.alert('Functionality not implemented!')}
              />
              {/* <Pressable>
                <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">
                  See All
                </Text>
              </Pressable> */}
            </View>
          </View>

          {/* agent details */}
          <View className="mt-2.5">
            {' '}
            // "mt-10"
            <Heading title={'Listed by '} />
            <View className="mb-4 mt-3 flex-row items-center justify-between rounded-xl border border-[#E9E2DD] px-4 py-3">
              {/* image and name part */}
              <View className="flex-row items-center">
                <NImage
                  source={require('@assets/images/profile7.png')}
                  className="aspect-square w-[86px]"
                />
                <View className="ml-3">
                  <Text className="font-airbnb_xbd text-xl font-extrabold text-primary-750">
                    Kabir Singh
                  </Text>
                  <Text className="font-airbnb_bk text-sm font-normal text-text-600">
                    Omkiron Properties
                  </Text>
                </View>
              </View>

              {/* rating & review part */}
              <View>
                <View className="mb-3 flex-row items-center self-end">
                  <NImage
                    source={require('@assets/icons/star.png')}
                    className="h-4 w-4 rounded-2xl"
                  />
                  <Text className="ml-0.5 font-airbnb_md text-sm font-medium text-text-600">
                    4.5
                  </Text>
                </View>

                <View>
                  <Text className="font-airbnb_md text-xs font-medium text-text-600">
                    59 Reviews
                  </Text>
                </View>
              </View>
            </View>
            <Pressable className="mb-6">
              <Text className="font-airbnb_md text-base font-medium text-secondary-main700 ">
                See All
              </Text>
            </Pressable>
          </View>

          {/* Comments */}
          <View>
            {/* comment filter heading */}
            <View className="mb-6 flex-row items-center justify-between">
              <Heading title={'3 Comments'} />
              {/* Sir asked to comment the filter comments option for now ↓ */}
              {/* <View className="flex-row items-center gap-2">
                <Text className="font-airbnb_md text-xs font-medium text-[#838383]">
                  Sort By:
                </Text>
                <Pressable className="flex-row items-center rounded-[4px] border border-[#E9E2DD] py-1 pl-3 pr-1">
                  <Text className="font-airbnb_md text-sm font-medium text-text-550">
                    Newest
                  </Text>
                  <Entypo name="chevron-small-down" size={18} color="#838383" />
                </Pressable>
              </View> */}
            </View>

            {/* all comments */}
            <View>
              {comments.map((comment, index) => (
                <View
                  key={index}
                  className="mb-6 flex-row items-center justify-between"
                >
                  <View className="flex-row items-center">
                    <NImage
                      source={comment.avatar}
                      className="mr-3 h-14 w-14 rounded-full"
                      contentFit="contain"
                    />
                    <View>
                      <Text className="mb-1 font-airbnb_bd text-sm font-bold text-primary-750">
                        {comment.name}
                      </Text>
                      <Text className="font-airbnb_bk text-sm font-normal text-text-500">
                        {comment.time}
                      </Text>
                    </View>
                  </View>
                  <Pressable>
                    <NImage
                      source={require('@assets/icons/horizontaldot.png')}
                      className="h-5 w-5"
                    />
                  </Pressable>
                </View>
              ))}
            </View>
          </View>
          <View className="mb-28" />
        </ScrollView>
      </View>

      {/* Bottom Button */}
      <View>
        {Platform.OS === 'ios' ? (
          <BlurView
            intensity={30}
            tint="light"
            blurReductionFactor={4}
            experimentalBlurMethod="dimezisBlurView"
            className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50/30 px-5 py-3"
          >
            <Button label={'Contact Agent'} />
          </BlurView>
        ) : Platform.OS === 'android' ? (
          <View className="absolute bottom-0 w-full flex-1 flex-col justify-end bg-primary-50 px-5 py-3">
            <Button label={'Contact Agent'} />
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
};

export default PropertyDetails;
