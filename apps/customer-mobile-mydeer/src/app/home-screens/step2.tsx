import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useState } from 'react';
import { Pressable, Text, View, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  getCurrentPositionAsync,
  requestForegroundPermissionsAsync,
  reverseGeocodeAsync,
} from 'expo-location';
import Toast from 'react-native-toast-message';
import TextInputGoogleAutoComplete from '@/components/shared/text-input-google-auto-complete';
import AntDesign from '@expo/vector-icons/AntDesign';
import { Button, Input } from '@/components/ui';
import { cn, useIsFirstTime, usePreferenceStore } from '@/lib';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';
import { setItem } from '@/lib/storage';
import { Preference } from '@/types';
import { SingleSelectFilterComponent } from '@/components/routes/home-screens-components/single-select-filter';

const formSchema = z.object({
  location: z.string().min(2),
  latitude: z.string(),
  longitude: z.string(),
  cityId: z.string(),
});

// eslint-disable-next-line max-lines-per-function
const Step2 = () => {
  const [detectingLocation, setDetectingLocation] = useState<boolean>(false);
  const [isFirstTime, setIsFirstTime] = useIsFirstTime();
  const { control, setValue, reset, handleSubmit, watch } = useForm<
    z.infer<typeof formSchema>
  >({
    resolver: zodResolver(formSchema),
    defaultValues: {
      location: '',
      latitude: '',
      longitude: '',
      cityId: '',
    },
  });
  const step2Mutation = api.onboarding.step2.useMutation({
    onSuccess: ({ message }) => {
      showMessage({
        type: 'success',
        message: message,
      });
      // Set isFirstTime to false to indicate onboarding is complete

      setIsFirstTime(false);
      router.push(`/home-screens/step3`);
    },
    onError: (opt) => {
      showMessage({
        type: 'danger',
        message: opt.message,
      });
    },
  });
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    if (data) {
      step2Mutation.mutate(
        {
          latitude: data.latitude,
          longitude: data.longitude,
          location: data.location,
          cityId: data.cityId,
        },
        {
          onSuccess: () => {
            showMessage({
              type: 'success',
              message: 'Location updated successfully',
            });
          },
          onError: (opt) => {
            showMessage({
              type: 'danger',
              message: opt.message,
            });
          },
        }
      );
    } else {
      showMessage({
        type: 'warning',
        message: 'Please fill all the fields',
      });
    }
  };

  async function getCurrentLocation() {
    setDetectingLocation(true);
    let { status } = await requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      setDetectingLocation(false);
      showMessage({
        message: 'Permission to access location was denied',
        type: 'warning',
      });
      return;
    }
    try {
      let location = await getCurrentPositionAsync({});
      // console.log(location);
      const reverseGeocode = await reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const address = reverseGeocode[0];
      if (!address) {
        Toast.show({
          text1: 'Error',
          text2: 'Unable to get address',
          type: 'error',
        });
        return;
      }
      setValue(
        'location',
        address.formattedAddress
          ? address.formattedAddress
          : `${address.streetNumber}, ${address.street}, ${address.district}, ${address.city}, ${address.region}, ${address.isoCountryCode}`
      );
      setValue('latitude', location.coords.latitude.toString());
      setValue('longitude', location.coords.longitude.toString());
    } catch (error) {
      showMessage({
        type: 'danger',
        message: 'Unable to get location',
      });
    }
    setDetectingLocation(false);
  }

  const onAddressComponentsChange = (data: {
    lat: string;
    lng: string;
    place_id: string;
    address_components: any;
    address: string;
  }) => {
    setValue('location', data.address);
    setValue('latitude', data.lat);
    setValue('longitude', data.lng);
  };

  const {
    data: Cities,
    isLoading: isLoadingCities,
    isError: isErrorCities,
  } = api.onboarding.getCitites.useQuery();
  const skipOnboardingMutation =
    api.onboarding.updateOnboardingStatus.useMutation({
      onSuccess: () => {
        showMessage({
          type: 'success',
          message: 'Onboarding skipped successfully',
        });
      },
    });
  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView>
        <View className="h-[0.5%] w-2/3 rounded-[4px] bg-secondary-main700" />

        <View className="h-full w-full px-5">
          <View className="mb-20 mt-5 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-xl font-semibold text-secondary-850">
                2/
              </Text>
              <Text className="text-xl font-semibold text-text-600">3</Text>
            </View>
            <Pressable onPress={() => router.push('/')}>
              <NImage
                source={require('../../../assets/icons/cancel.png')}
                contentFit="contain"
                className="h-8 w-8"
                tintColor={'#252525'}
              />
            </Pressable>
          </View>

          <View>
            <Text className="font-airbnb_xbd text-3xl font-extrabold text-secondary-main700">
              Choose Your Location
            </Text>
            <Text className="mt-1 font-normal text-sm font-airbnb_bk text-text-550">
              Choose your location to get customised suggestions
            </Text>
          </View>

          <View className="mt-8">
            <Controller
              control={control}
              name="location"
              render={({ field, fieldState: { error } }) => {
                return (
                  <TextInputGoogleAutoComplete
                    onAddressComponentsChange={onAddressComponentsChange}
                    placeholder={'data'}
                  >
                    <View className="mb-1">
                      <Text
                        className={cn(
                          'mb-2 font-airbnb_md text-base font-medium text-primary-800',
                          error && 'text-danger-600'
                        )}
                      >
                        Your Location
                      </Text>
                      <View
                        className={cn(
                          'mt-0 flex-row items-center rounded-xl border border-secondary-main700 bg-primary-50',
                          error && 'border-danger-600'
                        )}
                      >
                        <Text className="flex-1 px-5 py-3.5 font-airbnb_bk text-sm font-normal leading-5 text-text-600">
                          {field.value || ' '}
                        </Text>
                      </View>
                      {error?.message && (
                        <Text className="text-sm text-danger-400 ">
                          {error?.message}
                        </Text>
                      )}
                    </View>
                  </TextInputGoogleAutoComplete>
                );
              }}
            />
            {/* Auto location detection button */}
            <Pressable
              onPress={getCurrentLocation}
              className="my-3 flex flex-row items-center gap-2"
            >
              {detectingLocation ? (
                <ActivityIndicator color="#5f2800" size={18} />
              ) : (
                <NImage
                  source={require('../../../assets/icons/autolocate.png')}
                  className="h-5 w-5"
                  contentFit="contain"
                />
              )}
              <Text className="font-airbnb_bk text-sm font-normal text-primary-800">
                Autodetect my location
              </Text>
            </Pressable>

            <View className="mt-3">
              <SingleSelectFilterComponent
                label={'Cities'}
                options={
                  Cities?.map(({ id, name }) => ({
                    id,
                    value: name,
                  })) ?? [
                    {
                      id: '',
                      value: '',
                    },
                  ]
                }
                onPress={(p) => {
                  if (p) {
                    setValue('cityId', p.id);
                  }
                }}
              />
            </View>
          </View>

          {/* Bottom navigation buttons */}
          <View className="mb-20 flex-1 flex-col items-end justify-end">
            <View className="w-full flex-row items-center justify-between">
              <Pressable
                onPress={() => {
                  setIsFirstTime(false);
                  skipOnboardingMutation.mutate();
                }}
                className="rounded-xl bg-[#F1F1F1] px-6 py-3.5 border border-[#6B6B6B]"
              >
                <Text className="text-text-600  font-medium text-base font-airbnb_md">
                  Skip
                </Text>
              </Pressable>
              <Button
                variant="step"
                size="step"
                label="Continue"
                loading={step2Mutation.isPending}
                onPress={handleSubmit(onSubmit)}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default Step2;
