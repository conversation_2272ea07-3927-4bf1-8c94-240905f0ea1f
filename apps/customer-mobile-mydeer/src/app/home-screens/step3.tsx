import { Button } from '@/components/ui';
import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useRouter } from 'expo-router';
import React, { useRef } from 'react';
import { Pressable, Text, View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Entypo from '@expo/vector-icons/Entypo';
import { FormProvider, useForm } from 'react-hook-form';

import { FilterOptions } from '@/components/shared/filter-options';
import { useIsFirstTime } from '@/lib';
import { api } from '@/utils/api';
import { showMessage } from 'react-native-flash-message';

const Step3 = () => {
  const router = useRouter();
  const filterOptionsRef = useRef<(() => void) | null>(null);
  const utils = api.useUtils();

  const [isFirstTime, setIsFirstTime] = useIsFirstTime();

  const handlePress = () => {
    if (filterOptionsRef.current) {
      filterOptionsRef.current();
    }

    // Set isFirstTime to false to indicate onboarding is complete
    setIsFirstTime(false);

    // Show success message
    showMessage({
      type: 'success',
      message: 'Onboarding completed successfully',
    });

    // Navigate to home screen
    router.dismissAll();
    router.replace('/(app)/home');
  };

  const setFilterOptionsHandler = (handler: () => void) => {
    filterOptionsRef.current = handler;
  };

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView>
        <View className="h-[0.5%] w-3/3 rounded-[4px] bg-secondary-main700" />

        <View className="h-full w-full">
          <View className="mb-10 mt-5 px-5 flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="text-xl font-semibold text-secondary-850">
                3/
              </Text>
              <Text className="text-xl font-semibold text-text-600">3</Text>
            </View>
            {/* <Pressable
              onPress={() => {
                setIsFirstTime(false);
                router.replace('/(app)/home');
              }}
            >
              <NImage
                source={require('../../../assets/icons/cancel.png')}
                contentFit="contain"
                className="h-8 w-8"
                tintColor={'#252525'}
              />
            </Pressable> */}
          </View>

          <View className="mb-2 px-5">
            <Text className="font-airbnb_xbd text-3xl font-extrabold text-secondary-main700">
              Rental
            </Text>
          </View>

          {/* Filter component */}
          <FilterOptions
            onPress={() => {}}
            setOnPressHandler={setFilterOptionsHandler}
            step3={true}
          />

          {/* Bottom navigation buttons */}
          <View className="px-5 mb-8 mt-2">
            <View className="w-full flex-row items-center justify-between">
              <Pressable
                onPress={() => router.back()}
                className="rounded-xl bg-[#FAFAFA] px-6 py-3.5"
              >
                <Entypo name="chevron-thin-left" size={24} color="black" />
              </Pressable>
              <Button
                variant="step"
                size="step"
                label="Continue"
                onPress={handlePress}
              />
            </View>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default Step3;

const styles = StyleSheet.create({
  shadowBox: {
    shadowColor: '#250000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
});
