// Import  global CSS file
import '../../global.css';

import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { ThemeProvider } from '@react-navigation/native';
import { ErrorBoundary, Redirect, Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useFonts } from 'expo-font';
import { StyleSheet, Platform, StatusBar as RNStatusBar } from 'react-native';
import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';

import { APIProvider } from '@/api';
import { hydrateAuth, loadSelectedTheme, useAuth } from '@/lib';
import { useThemeConfig } from '@/lib/use-theme-config';
import { useEffect } from 'react';
import React from 'react';
import { TRPCProvider } from '@/utils/api';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LogLevel, OneSignal } from 'react-native-onesignal';

export { ErrorBoundary } from 'expo-router';

hydrateAuth();
loadSelectedTheme();

SplashScreen.preventAutoHideAsync();

SplashScreen.setOptions({
  duration: 500,
  fade: true,
});

export default function RootLayout() {
  const { status } = useAuth();
  const [fontsLoaded] = useFonts({
    Inter: require('@assets/fonts/Inter.ttf'),
    AirbnbW_XBd: require('@assets/fonts/AirbnbCereal_W_XBd.otf'),
    AirbnbW_Md: require('@assets/fonts/AirbnbCereal_W_Md.otf'),
    AirbnbW_Lt: require('@assets/fonts/AirbnbCereal_W_Lt.otf'),
    AirbnbW_Blk: require('@assets/fonts/AirbnbCereal_W_Blk.otf'),
    AirbnbW_Bk: require('@assets/fonts/AirbnbCereal_W_Bk.otf'),
    AirbnbW_Bd: require('@assets/fonts/AirbnbCereal_W_Bd.otf'),
  });

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  // Initialize OneSignal in useEffect to ensure it runs only once
  useEffect(() => {
    // Enable verbose logging for debugging (remove in production)
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    // Initialize with your OneSignal App ID
    OneSignal.initialize('************************************');
    // Use this method to prompt for push notifications.
    // We recommend removing this method after testing and instead use In-App Messages to prompt for notification permission.
    OneSignal.Notifications.requestPermission(false);
  }, []); // Ensure this only runs once on app mount

  useEffect(() => {
    if (Platform.OS === 'android') {
      RNStatusBar.setTranslucent(true);
      RNStatusBar.setBackgroundColor('transparent');
    }
    RNStatusBar.setBarStyle('dark-content');
  }, []);

  return (
    <TRPCProvider>
      <Providers>
        <Stack>
          <Stack.Screen name="index" options={{ headerShown: false }} />
          <Stack.Protected guard={status === 'signOut'}>
            <Stack.Screen name="auth" options={{ headerShown: false }} />
          </Stack.Protected>
          <Stack.Screen name="(app)" options={{ headerShown: false }} />
          <Stack.Screen name="home-screens" options={{ headerShown: false }} />
          <Stack.Screen
            name="profile-screens"
            options={{ headerShown: false }}
          />
          <Stack.Screen name="chat-screens" options={{ headerShown: false }} />
          <Stack.Screen name="agent-routes" options={{ headerShown: false }} />
          <Stack.Screen
            name="property-routes"
            options={{ headerShown: false }}
          />
        </Stack>
      </Providers>
    </TRPCProvider>
  );
}

function Providers({ children }: { children: React.ReactNode }) {
  const theme = useThemeConfig();
  const behavior = Platform.OS === 'ios' ? 'height' : 'padding';
  const insets = useSafeAreaInsets();
  return (
    <GestureHandlerRootView
      style={styles.container}
      className={theme.dark ? `dark` : undefined}
    >
      <StatusBar
        style="dark"
        backgroundColor="transparent"
        translucent={true}
        networkActivityIndicatorVisible={true}
      />

      <KeyboardProvider enabled={true}>
        <ThemeProvider value={theme}>
          <APIProvider>
            <BottomSheetModalProvider>
              {children}
              <FlashMessage
                position="top"
                style={{
                  paddingTop: Platform.OS === 'ios' ? 0 : insets.top,
                }}
              />
            </BottomSheetModalProvider>
          </APIProvider>
        </ThemeProvider>
      </KeyboardProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
