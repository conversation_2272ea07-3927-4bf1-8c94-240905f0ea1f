import { api } from '@/utils/api';
import { Image as NImage } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native';
import * as Clipboard from 'expo-clipboard';
import { showMessage } from 'react-native-flash-message';
import * as Linking from 'expo-linking';
import { Share } from 'react-native';

import { getBaseUrl } from '@/utils/base-url';

const { height, width } = Dimensions.get('screen');

const ReferFriend = () => {
  const { data: profile, isPending } = api.user.getProfile.useQuery();
  const inviteCode = profile?.inviteCode ?? 'N/A';
  const referralLink = `${getBaseUrl()}/sign-up?referralCode=${inviteCode}`;

  const handleCopyCode = async () => {
    try {
      await Clipboard.setStringAsync(inviteCode);
      showMessage({
        message: 'Invite code copied to clipboard!',
        type: 'success',
      });
    } catch (error) {
      showMessage({
        message: 'Failed to copy invite code.',
        type: 'danger',
      });
    }
  };

  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: `Check out this link: ${referralLink}`,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('shared');
        }
      } else if (result.action === Share.dismissedAction) {
      }
    } catch (error) {
      console.log(error);
      alert(error);
    }
  };

  return (
    <LinearGradient
      colors={['#FFFCFB', '#FACABD']}
      start={{ x: 0, y: 0.69 }}
      end={{ x: 0, y: 1 }}
      style={styles.gradient}
    >
      <View className="mt-6 items-center px-5">
        <Text className="font-airbnb_bk text-sm font-normal text-text-550">
          Invite Agent
        </Text>
        <Text className="font-airbnb_blk text-xl font-black text-secondary-main700">
          Invite Agents & Earn
        </Text>
        <Text className="mt-3 font-airbnb_bk text-sm font-normal text-text-500">
          Refer agents to join our platform & earn rewards.
        </Text>

        <View className="py-6">
          <NImage
            source={require('../../../assets/images/cuate.png')}
            style={{ height: height * 0.3464, width: width * 0.7523 }}
          />
        </View>

        <Pressable
          className="flex-row items-center rounded-xl bg-primary-100 px-5 py-2.5"
          onPress={handleCopyCode}
        >
          <Text className="font-airbnb_bk text-xs font-normal text-text-600">
            Invite code : {inviteCode}
          </Text>
          <NImage
            source={require('../../../assets/icons/copyicon.png')}
            className="ml-2 h-3 w-3"
          />
        </Pressable>

        <View className="w-full flex-row items-center justify-between pt-8 px-5">
          <Pressable
            className="flex-1 mr-3 flex-row items-center justify-center rounded-xl bg-secondary-main700 px-6 py-3.5"
            onPress={() =>
              Linking.openURL(
                `whatsapp://send?text=Hey! I'm inviting you to join MyDeer, a great real estate platform. Use my referral link to sign up: ${referralLink}`
              )
            }
          >
            <NImage
              source={require('../../../assets/icons/whatsapp.png')}
              className="mr-2 h-5 w-5"
            />
            <Text
              className="font-airbnb_md text-sm font-medium text-white"
              numberOfLines={2}
            >
              WhatsApp
            </Text>
          </Pressable>

          <Pressable
            className="flex-1 ml-3 flex-row items-center justify-center rounded-xl bg-secondary-100 px-6 py-3.5"
            onPress={handleShare}
          >
            <NImage
              source={require('../../../assets/icons/share2.png')}
              className="mr-2 h-5 w-5"
            />
            <Text
              className="font-airbnb_md text-sm font-medium text-secondary-main700"
              numberOfLines={2}
            >
              More Options
            </Text>
          </Pressable>
        </View>
      </View>
    </LinearGradient>
  );
};

export default ReferFriend;

const styles = StyleSheet.create({
  gradient: {
    flex: 1,
    height: 100,
  },
  blurContainer: {
    flex: 1,
    justifyContent: 'center',
  },
});
