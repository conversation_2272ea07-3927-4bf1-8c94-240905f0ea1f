import {
  View,
  ScrollView,
  Pressable,
  Text,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import React, { useState, useEffect, useRef } from 'react';
import { Button, ControlledInput } from '@/components/ui';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import Entypo from '@expo/vector-icons/Entypo';
import { Image as NImage } from 'expo-image';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { EditProfileSchema } from '@/utils/form-validators';
import { zodResolver } from '@hookform/resolvers/zod';
import { PhotoOptionsModal } from '@/components/shared/photo-options-modal';
import { showMessage } from 'react-native-flash-message';
import * as ImagePicker from 'expo-image-picker';
import { api } from '@/utils/api';
import { CustomerEditProfileSchema } from '@repo/validators';
import { signedUploadToCloudinary } from '@/api/cloudinary';
import {
  KeyboardAvoidingView,
  KeyboardAwareScrollView,
} from 'react-native-keyboard-controller';

export type FormType = z.infer<typeof CustomerEditProfileSchema>;

interface CloudinaryImageData {
  url: string;
  publicId: string;
}

// Function to check if running in simulator
const isSimulator = () => {
  // In development mode with iOS, assume simulator if running in debug
  // This is a simple heuristic that works for most simulator cases
  return Platform.OS === 'ios' && __DEV__;
};

const EditProfile = () => {
  const [isFocused, setIsFocused] = useState(false);
  const utils = api.useUtils();
  const { data: profile } = api.user.getProfile.useQuery(undefined, {
    retry: false,
  });

  const profileUpdateMutation = api.user.updateProfile.useMutation({
    onSuccess: ({ message, warning }) => {
      if (!warning) {
        showMessage({
          message,
          type: 'success',
        });
      } else {
        showMessage({
          message,
          type: 'warning',
        });
      }

      void utils.invalidate();
    },
    onError: ({ message }) => {
      showMessage({
        message: message,
        type: 'danger',
      });
    },
  });

  // Add updateUserProfileMedia mutation
  const updateUserProfileMediaMutation =
    api.user.updateUserProfileMedia.useMutation({
      onSuccess: ({ message }) => {
        showMessage({
          message,
          type: 'success',
        });
        void utils.invalidate();
      },
      onError: ({ message }) => {
        showMessage({
          message: message,
          type: 'danger',
        });
      },
    });

  const insets = useSafeAreaInsets();
  const [modalVisible, setModalVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Create a stable timestamp for the signature query
  const timestampRef = useRef(Math.round(Date.now() / 1000));

  // Fetch signature for Cloudinary upload
  const { data: signatureData } = api.cloudinary.generateSignature.useQuery({
    paramsToSign: {
      timestamp: timestampRef.current,
      folderFor: 'customers',
      forlderPurpose: 'profile',
    },
  });

  // Initialize cloudinaryImage state with appropriate values
  const [cloudinaryImage, setCloudinaryImage] = useState<CloudinaryImageData>({
    // Prioritize Cloudinary URL if available
    url: '',
    publicId: '',
  });

  // Update cloudinaryImage when profile data is loaded
  useEffect(() => {
    if (profile) {
      console.log('[DEBUG] Profile loaded, initializing image data');

      // Access these properties safely with type assertions
      const cloudinaryImageUrl = (profile as any).cloudinaryImageUrl;
      const profileImagePublicUrl = profile.profileImagePublicUrl;
      const cloudinaryPublicId = (profile as any).cloudinaryPublicId;

      console.log('[DEBUG] cloudinaryImageUrl:', cloudinaryImageUrl);
      console.log('[DEBUG] profileImagePublicUrl:', profileImagePublicUrl);

      // Prioritize Cloudinary URL if available
      const imageUrl = cloudinaryImageUrl || profileImagePublicUrl || '';
      const publicId = cloudinaryPublicId || '';

      setCloudinaryImage({
        url: imageUrl,
        publicId: publicId,
      });
    }
  }, [profile]);

  const { handleSubmit, control } = useForm<FormType>({
    resolver: zodResolver(EditProfileSchema),
    defaultValues: {
      adharcardNumber: profile?.adharcardNumber ?? undefined,
      bio: profile?.bio ?? undefined,
      email: profile?.email,
      name: profile?.name,
      phoneNumber: profile?.phoneNumber,
    },
  });

  // Request permissions for camera and media library
  const requestPermissions = async () => {
    const { status: cameraStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: libraryStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
      Alert.alert(
        'Permission denied',
        'Camera and photo permissions are required.'
      );
      return false;
    }
    return true;
  };

  // Function to upload image to Cloudinary
  const uploadImage = async (uri: string) => {
    try {
      setIsUploading(true);
      setModalVisible(false);

      if (!signatureData || !signatureData.signature) {
        throw new Error('Signature not available');
      }

      // Upload the new image
      const result = await signedUploadToCloudinary({
        imageUri: uri,
        folder: signatureData.uploadFolderUrl,
        signature: signatureData.signature,
        timestamp: timestampRef.current.toString(),
        preset: signatureData.cloudPreset,
        apiKey: signatureData.apiKey,
        cloudName: signatureData.cloudName,
      });

      // Update state with new image data
      const newImageData = {
        url: result.secure_url,
        publicId: result.public_id,
      };

      setCloudinaryImage(newImageData);

      // Call the updateUserProfileMedia mutation immediately after upload
      await updateUserProfileMediaMutation.mutateAsync({
        cloudinaryUrl: result.secure_url,
        cloudinaryPublicId: result.public_id,
      });
    } catch (err) {
      console.error('Error uploading image:', err);
      showMessage({
        message: 'Failed to upload image',
        type: 'danger',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Function to handle image selection
  const pickImage = async () => {
    if (!(await requestPermissions())) return;

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.75,
    });

    if (!result.canceled && result.assets[0]) {
      await uploadImage(result.assets[0].uri);
    }
    setModalVisible(false);
  };

  // Function to handle photo capture
  const takePhoto = async () => {
    if (!(await requestPermissions())) return;

    // Check if running in simulator
    if (isSimulator()) {
      Alert.alert(
        'Camera Unavailable',
        'Camera is not available in simulator environment. Would you like to choose a photo from your library instead?',
        [
          {
            text: 'Cancel',
            onPress: () => setModalVisible(false),
            style: 'cancel',
          },
          { text: 'Choose from Library', onPress: pickImage },
        ]
      );
      return;
    }

    try {
      let result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        await uploadImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);

      // Offer the user an alternative when camera fails
      Alert.alert(
        'Camera Error',
        'Failed to access camera. Would you like to choose a photo from your library instead?',
        [
          {
            text: 'Cancel',
            onPress: () => setModalVisible(false),
            style: 'cancel',
          },
          { text: 'Choose from Library', onPress: pickImage },
        ]
      );
    } finally {
      setModalVisible(false);
    }
  };

  // Function to delete the image
  const deleteProfileImage = async () => {
    try {
      setIsUploading(true);

      // Reset the local state
      setCloudinaryImage({
        url: '',
        publicId: '',
      });

      // Call the updateUserProfileMedia mutation with empty values to clear the image
      await updateUserProfileMediaMutation.mutateAsync({
        cloudinaryUrl: '',
        cloudinaryPublicId: '',
      });

      showMessage({
        message: 'Profile image removed',
        type: 'success',
      });
    } catch (err) {
      console.error('Error removing image:', err);
      showMessage({
        message: 'Failed to remove image',
        type: 'danger',
      });
    } finally {
      setIsUploading(false);
      setModalVisible(false);
    }
  };

  const onSubmit = async (data: FormType) => {
    try {
      console.log(
        '[DEBUG] onSubmit - cloudinaryImage:',
        JSON.stringify(cloudinaryImage)
      );

      // Submit only the form data without cloudinary image data
      // since it's already been handled by updateUserProfileMedia
      await profileUpdateMutation.mutateAsync(data);
    } catch (error) {
      console.log(error);
    }
  };

  // const behavior = Platform.OS === 'ios' ? 'padding' : 'padding';

  const handleInputFocus = () => setIsFocused(true);
  const handleInputBlur = () => setIsFocused(false);

  // Get the appropriate image URL to display
  const displayImageUrl =
    profile?.cloudinaryImagePublicUrl ?? profile?.profileImagePublicUrl ?? '';

  return (
    <>
      <View
        // behavior={behavior}
        style={{ flex: 1 }}
        // keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -150}
      >
        <View className="flex-1">
          <LinearGradient
            colors={['#F04D24', '#C33D12', '#962C00']}
            start={[0, 0]}
            end={[0.7, 0]}
          >
            <BlurView intensity={50} tint="light">
              <View
                className="h-[250px] px-5"
                style={{ paddingTop: insets.top }}
              >
                <View className="my-2.5 flex-row items-center justify-between">
                  <Pressable onPress={() => router.back()} className="w-[85px]">
                    <Entypo
                      name="chevron-thin-left"
                      size={18}
                      color="#FFFBF9"
                    />
                  </Pressable>

                  <Text className="font-airbnb_xbd text-xl font-extrabold text-white">
                    Edit Profile
                  </Text>
                  <Text className="w-[85px]"> </Text>
                </View>

                <View className="flex-1 items-center justify-center">
                  {isUploading ? (
                    <View className="h-32 w-32 items-center justify-center rounded-full bg-gray-200">
                      <ActivityIndicator size="large" color="#F04D24" />
                    </View>
                  ) : (
                    <>
                      <NImage
                        source={
                          displayImageUrl
                            ? { uri: displayImageUrl }
                            : require('@assets/icons/user-defaultimg.jpeg')
                        }
                        className="h-32 w-32 rounded-full"
                      />

                      <Pressable
                        className="absolute bottom-6 flex-row items-center rounded-xl bg-primary-0 p-1.5"
                        onPress={() => setModalVisible(true)}
                      >
                        <NImage
                          source={require('../../../assets/icons/camera.png')}
                          className="mr-1 h-3 w-3"
                        />
                        <Text className="font-airbnb_md text-[10px] font-medium text-[#5F3924]">
                          Add Photo
                        </Text>
                      </Pressable>
                    </>
                  )}
                </View>
              </View>
            </BlurView>
          </LinearGradient>

          <KeyboardAwareScrollView
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
            className="flex-1"
          >
            <View className={`px-4 pt-6`}>
              <ControlledInput
                testID="name"
                control={control}
                name="name"
                label="Name"
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
              <ControlledInput
                testID="email-input"
                control={control}
                name="email"
                label="Email Id"
                autoCapitalize="none"
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
              <ControlledInput
                testID="phone-number"
                control={control}
                keyboardType="numeric"
                name="phoneNumber"
                label="Phone Number"
                editable={false}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
              <ControlledInput
                testID="aadhaar-number"
                control={control}
                keyboardType="numeric"
                name="adharcardNumber"
                label="Aadhaar Card Number"
                editable={false}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
              <ControlledInput
                testID="bio"
                control={control}
                name="bio"
                label="Bio"
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
              />
            </View>
          </KeyboardAwareScrollView>

          <View
            className="px-5 pb-3"
            style={{
              marginBottom: isFocused ? 0 : insets.bottom,
            }}
          >
            <Button
              onPress={handleSubmit(onSubmit, (e) =>
                console.log('error submittin', e)
              )}
              label="Save Changes"
              loading={profileUpdateMutation.isPending}
            />
          </View>
        </View>

        <PhotoOptionsModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          onChoosePhoto={pickImage}
          onTakePhoto={takePhoto}
          onRemovePhoto={deleteProfileImage}
        />
      </View>
    </>
  );
};

export default EditProfile;
