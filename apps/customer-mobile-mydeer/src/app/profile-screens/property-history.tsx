import { View, Text, ScrollView, RefreshControl } from 'react-native';
import React from 'react';
import { useRouter } from 'expo-router';
import { api } from '@/utils/api';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FlashList } from '@shopify/flash-list';

import { IProperty } from '@/types';
import PropertiesComponent from '@/components/routes/properties-component/properties-component';

const PropertyHistory = () => {
  const {
    data: propertyHistory,
    isLoading: isLoadingGetLikedProperties,
    refetch,
  } = api.user.getPropertyHistory.useQuery();
  const router = useRouter();

  const insets = useSafeAreaInsets();

  function handlePress(id: string): void {
    router.navigate({
      pathname: `/property-routes/[propertyId]`,
      params: { propertyId: id },
    });
  }

  if (!propertyHistory?.length) {
    return (
      <ScrollView
        className="flex-1 h-full px-5"
        contentContainerStyle={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingGetLikedProperties}
            onRefresh={refetch}
          />
        }
      >
        <View>
          <Text className="text-xl font-semi-bold ">
            Your viewed properties will appear here
          </Text>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView
      className="flex-1 px-5"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isLoadingGetLikedProperties}
          onRefresh={refetch}
        />
      }
    >
      <FlashList
        showsVerticalScrollIndicator={false}
        data={propertyHistory}
        estimatedItemSize={210}
        keyExtractor={(item) => item.id.toString()}
        className="flex-1 pb-12"
        renderItem={({ item }) => {
          return (
            <PropertiesComponent
              item={item.property}  
              handlePress={() => handlePress(item.propertyId)}
              isLiked={item.property.customerFavourites?.length > 0}
            />
          );
        }}
      />
    </ScrollView>
  );
};

export default PropertyHistory;
