import { router, Stack } from 'expo-router';
import React from 'react';
import { Pressable } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';

export default function Layout() {
  return (
    <Stack>
      <Stack.Screen name="edit-profile" options={{ headerShown: false }} />
      <Stack.Screen
        name="property-history"
        options={{
          title: 'Property History',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="my-reviews"
        options={{
          title: 'My Reviews',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="settings"
        options={{
          title: 'Settings',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="report-fraud"
        options={{
          title: 'Report Fraud',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="help-center"
        options={{
          title: 'Help Center',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="refer-friend"
        options={{
          title: '',
          headerTintColor: '#F04D24',

          headerStyle: { backgroundColor: '#FFFAF9' },
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="feedback"
        options={{
          title: 'Give Your Feedback',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="privacy-policy"
        options={{
          title: 'Privacy Policy',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
      <Stack.Screen
        name="terms-conditions"
        options={{
          title: 'Terms of Services',
          headerTintColor: '#F04D24',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: '800',
          },
          headerStyle: { backgroundColor: '#FFFAF9' },
          headerTitleAlign: 'center',
          headerLeft: () => (
            <Pressable onPress={() => router.back()} className="p-3 rounded-lg">
              <Entypo name="chevron-thin-left" size={18} color="#451F0A" />
            </Pressable>
          ),
        }}
      />
    </Stack>
  );
}
