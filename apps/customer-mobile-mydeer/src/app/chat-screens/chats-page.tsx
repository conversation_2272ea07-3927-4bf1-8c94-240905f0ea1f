import { FlashList } from '@shopify/flash-list';
import { Image as NImage } from 'expo-image';
import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import {
  Pressable,
  Text,
  View,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import ChatsEmpty from '@/components/routes/chats-empty-page';

import { skipToken } from '@tanstack/react-query';
import UserPic from '@/components/shared/user-pic';
import { format } from 'date-fns';
import { api } from '@/utils/api';
import { Path } from 'react-native-svg';

const ChatPage = () => {
  const { query } = useLocalSearchParams<{ query: string }>();

  const {
    data: activeConversation,
    isLoading: isLoadingConversation,
    isError: isErrorConversations,
    refetch: refetchConversations,
  } = api.chat.getActiveConversation.useQuery();

  const onRefresh = async () => {
    await refetchConversations();
  };

  const HandleConditions = () => {
    if (isLoadingConversation) {
      return (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size={40} color="#fff" />
        </View>
      );
    }

    if (isErrorConversations) {
      return (
        <View className="items-center justify-center">
          <Text>Error loading conversations</Text>
        </View>
      );
    }

    if (!activeConversation || Object.keys(activeConversation).length === 0) {
      return (
        <View className="h-full w-full items-center justify-center">
          <Text className="text-xl font-semi-bold ">
            Your chats will appear here
          </Text>
        </View>
      );
    }
  };

  return (
    <ScrollView
      className="h-full"
      refreshControl={
        <RefreshControl
          refreshing={isLoadingConversation}
          onRefresh={onRefresh}
        />
      }
    >
      <View className="flex-1">
        {!activeConversation ? (
          <ChatsEmpty />
        ) : (
          <>
            {HandleConditions()}
            {activeConversation &&
              !isLoadingConversation &&
              !isErrorConversations && (
                <FlashList
                  showsVerticalScrollIndicator={false}
                  data={[activeConversation]}
                  keyExtractor={(item) => item.id.toString()}
                  estimatedItemSize={200}
                  renderItem={({ item, index }) => {
                    const user = item.agent;
                    const noOfMessages = item.agentUnseenMessagesCount;
                    const lastMessage = item.messages[0];
                    return (
                      <Pressable
                        className={`mx-5 ${index === 1 ? 'mt-4' : ''} border-b border-[#DED3CD]`}
                        onPress={() =>
                          router.push(
                            `/chat-screens/${item.id}?name=${item.agent.name}&image=${item.agent.cloudinaryProfileImageUrl || item.agent.filePublicUrl}&agentId=${item.agentId}&rated=${item.rating.length > 0}`
                          )
                        }
                      >
                        <View
                          className={`my-2 rounded-lg ${index % 2 === 0 ? 'bg-primary-50' : 'bg-[#FAFAFA]'} p-3`}
                        >
                          <View className="flex-row">
                            <UserPic
                              picUrl={
                                item.agent.cloudinaryProfileImageUrl ||
                                item.agent.filePublicUrl ||
                                undefined
                              }
                              color="#F04D24"
                              size={54}
                              className="mr-2.5 aspect-square h-16 rounded-full"
                            />
                            <View className="flex-1">
                              <View className="flex-row items-start justify-between">
                                {/* Left side (name, detail) */}
                                <View className="flex-1 pr-2 max-w-[70%]">
                                  <Text className="font-airbnb_bd text-base font-bold text-primary-700">
                                    {user.name}
                                  </Text>
                                  <Text
                                    className="mt-2 font-airbnb_bk text-xs font-normal text-text-500"
                                    numberOfLines={2}
                                    ellipsizeMode="tail"
                                  >
                                    {lastMessage?.content}
                                  </Text>
                                </View>
                                {/* Right side (type, time, no of messages) */}
                                <View className="flex-shrink-0">
                                  <View className="items-end">
                                    <View className="rounded-lg bg-[#F4F0EE] p-2.5 mb-1">
                                      <Text className="font-airbnb_md text-xs font-medium text-secondary-main700">
                                        Agent
                                      </Text>
                                    </View>
                                    <Text className="mt-1 font-airbnb_md text-xs font-medium text-text-500">
                                      {format(item.createdAt, 'dd/MM/yyyy')}
                                    </Text>
                                    <Text className="mt-1 font-airbnb_md text-xs font-medium text-text-500">
                                      {format(item.createdAt, 'h:mm a')}
                                    </Text>
                                  </View>
                                </View>
                              </View>
                            </View>
                          </View>
                        </View>
                      </Pressable>
                    );
                  }}
                />
              )}
          </>
        )}
      </View>
    </ScrollView>
  );
};

export default ChatPage;
