import { Stack } from 'expo-router';
import React from 'react';

// import { StatusBar } from 'react-native';
import TextInputHeader from '@/components/shared/text-input-header';

export default function Layout() {
  return (
    <>
      {/* <StatusBar barStyle="default" backgroundColor="#f9f7f7" /> */}
      <Stack>
        <Stack.Screen
          name="chats-page"
          options={{
            header: () => (
              <TextInputHeader placeholderText="Search your agent" />
            ),
          }}
        />
        <Stack.Screen name="[id]" options={{}} />
      </Stack>
    </>
  );
}
