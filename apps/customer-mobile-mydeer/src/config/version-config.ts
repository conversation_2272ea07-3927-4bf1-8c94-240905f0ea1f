import { Env } from '@env';
import { Platform } from 'react-native';

/**
 * Version configuration for the app
 * This can be updated to change minimum version requirements
 */
export const VERSION_CONFIG = {
  // Minimum required version for the app to function
  // Update this when you want to force users to update
  MINIMUM_REQUIRED_VERSION: '0.0.4',
  
  // Current app version (automatically pulled from package.json)
  CURRENT_VERSION: Env.VERSION,
  
  // App store URLs for updates
  STORE_URLS: {
    ios: 'https://apps.apple.com/in/app/mydeer/id6748201287', // Replace with actual App Store URL
    android: 'https://play.google.com/store/apps/details?id=com.nextflytech.mydeer',
  },
  
  // Version check settings
  SETTINGS: {
    // Whether to show update prompt on app start
    CHECK_ON_START: true,
    
    // Whether to allow dismissing the update prompt
    ALLOW_DISMISS: true,
    
    // Whether to block app usage if version is below minimum
    BLOCK_APP_USAGE: true,
  },
} as const;

/**
 * Get the appropriate store URL based on platform
 */
export const getStoreUrl = (): string => {
  return Platform.OS === 'ios' 
    ? VERSION_CONFIG.STORE_URLS.ios 
    : VERSION_CONFIG.STORE_URLS.android;
};

/**
 * Check if the current version meets the minimum requirement
 */
export const isVersionCompatible = (): boolean => {
  const currentVersion = VERSION_CONFIG.CURRENT_VERSION;
  const minimumVersion = VERSION_CONFIG.MINIMUM_REQUIRED_VERSION;
  
  const v1Parts = currentVersion.split('.').map(Number);
  const v2Parts = minimumVersion.split('.').map(Number);
  
  const maxLength = Math.max(v1Parts.length, v2Parts.length);
  while (v1Parts.length < maxLength) v1Parts.push(0);
  while (v2Parts.length < maxLength) v2Parts.push(0);
  
  for (let i = 0; i < maxLength; i++) {
    if (v1Parts[i] > v2Parts[i]) return true;
    if (v1Parts[i] < v2Parts[i]) return false;
  }
  return true;
}; 