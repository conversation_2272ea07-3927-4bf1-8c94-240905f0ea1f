import { Env } from '@env';
import * as Updates from 'expo-updates';

/**
 * Compares two version strings and returns true if version1 is greater than or equal to version2
 * @param version1 - First version string (e.g., "1.2.3")
 * @param version2 - Second version string (e.g., "1.2.0")
 * @returns true if version1 >= version2, false otherwise
 */
export const compareVersions = (version1: string, version2: string): boolean => {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);

  // Pad with zeros if needed
  const maxLength = Math.max(v1Parts.length, v2Parts.length);
  while (v1Parts.length < maxLength) v1Parts.push(0);
  while (v2Parts.length < maxLength) v2Parts.push(0);

  for (let i = 0; i < maxLength; i++) {
    if (v1Parts[i] > v2Parts[i]) return true;
    if (v1Parts[i] < v2Parts[i]) return false;
  }
  return true; // Versions are equal
};

/**
 * Checks if the current app version meets the minimum required version
 * @param minimumVersion - The minimum required version string
 * @returns true if current version meets minimum requirement, false otherwise
 */
export const checkMinimumVersion = (minimumVersion: string): boolean => {
  const currentVersion = Env.VERSION;
  return compareVersions(currentVersion, minimumVersion);
};

/**
 * Gets the current app version
 * @returns The current app version string
 */
export const getCurrentVersion = (): string => {
  return Env.VERSION;
};

/**
 * Gets the runtime version from Expo Updates
 * @returns The runtime version string or null if not available
 */
export const getRuntimeVersion = (): string | null => {
  return Updates.runtimeVersion || null;
};

/**
 * Checks if an update is available and required
 * @param minimumVersion - The minimum required version
 * @returns Object with update status information
 */
export const checkUpdateStatus = (minimumVersion: string) => {
  const currentVersion = getCurrentVersion();
  const runtimeVersion = getRuntimeVersion();
  const meetsMinimum = checkMinimumVersion(minimumVersion);

  return {
    currentVersion,
    runtimeVersion,
    minimumVersion,
    meetsMinimum,
    needsUpdate: !meetsMinimum,
  };
}; 