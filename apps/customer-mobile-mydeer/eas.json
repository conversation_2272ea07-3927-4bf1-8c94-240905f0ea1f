{"cli": {"version": ">= 3.8.1"}, "build": {"production": {"channel": "production", "distribution": "store", "node": "22.14.0", "yarn": "1.22.22", "ios": {"image": "latest"}, "android": {"buildType": "app-bundle", "image": "latest"}, "env": {"EXPO_NO_DOTENV": "1", "APP_ENV": "production", "FLIPPER_DISABLE": "1"}}, "staging": {"channel": "staging", "distribution": "internal", "node": "22.14.0", "yarn": "1.22.22", "ios": {"image": "latest"}, "android": {"buildType": "apk", "image": "latest"}, "env": {"APP_ENV": "staging", "EXPO_NO_DOTENV": "1", "FLIPPER_DISABLE": "1"}}, "development": {"developmentClient": true, "distribution": "internal", "node": "22.14.0", "yarn": "1.22.22", "ios": {"image": "latest"}, "android": {"image": "latest"}, "env": {"APP_ENV": "development", "EXPO_NO_DOTENV": "1"}}, "simulator": {"node": "22.14.0", "yarn": "1.22.22", "ios": {"simulator": true, "image": "latest"}, "android": {"image": "latest"}, "env": {"APP_ENV": "development", "EXPO_NO_DOTENV": "1"}}}, "submit": {"production": {}}}