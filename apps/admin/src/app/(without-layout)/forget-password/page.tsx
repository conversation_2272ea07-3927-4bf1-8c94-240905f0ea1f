"use client";
import { z } from "zod";
import Link from "next/link";
import { <PERSON><PERSON> } from "@repo/ui/components/ui/button";
import { Input } from "@repo/ui/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { type FieldErrors, useForm } from "react-hook-form";
import { toast } from "@repo/ui/components/ui/sonner";
import { api } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/ui/card";

export default function LoginPage() {
  const { mutate: forgetPassword, isPending } =
    api.admin.forgetPassword.useMutation();

  const loginFormSchema = z.object({
    email: z.string().email(),
  });

  const form = useForm<z.infer<typeof loginFormSchema>>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async ({ email }: z.infer<typeof loginFormSchema>) => {
    forgetPassword(
      { email },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  const onError = async (
    errors: FieldErrors<z.infer<typeof loginFormSchema>>,
  ) => {
    toast.error("Check form validation");
  };

  return (
    <div className="flex w-full items-center justify-center py-12">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Forget Password</CardTitle>
          <CardDescription>
            Enter your email below to receive link to reset your password
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4">
          <Form {...form}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Link
                href="/auth/login"
                className="ml-auto inline-block text-sm underline"
              >
                Go to login ?
              </Link>

              {isPending ? (
                <LoadingButton className="w-full py-3" loading>
                  Requesting...
                </LoadingButton>
              ) : (
                <Button
                  onClick={form.handleSubmit(onSubmit, onError)}
                  type="submit"
                  className="w-full py-3"
                >
                  Request Reset Link
                </Button>
              )}
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
