"use client";

import type { z } from "zod";
import { useEffect, useState } from "react";
import { signIn, useSession } from "next-auth/react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/ui/card";
import { Input } from "@repo/ui/components/ui/input";
import { Button } from "@repo/ui/components/ui/button";

import adminLoginSchema from "~/server/api/validations/admin-login.validation";
import { useRouter } from "next/navigation";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { toast } from "@repo/ui/components/ui/sonner";
import Link from "next/link";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
// import { toast } from "@repo/ui/hooks/use-toast";

const AdminLoginPage = () => {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);

  const form = useForm<z.infer<typeof adminLoginSchema>>({
    resolver: zodResolver(adminLoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  useEffect(() => {
    const isLoggedIn = () => {
      if (session) router.push("/");
    };

    isLoggedIn();
  }, [router, session]);

  const onSubmit = async (values: z.infer<typeof adminLoginSchema>) => {
    const { email, password } = values;

    setIsLoading((prev) => !prev);
    const resp = await signIn("credentials", {
      email: email,
      password: password,
      redirect: false,
    });
    setIsLoading((prev) => !prev);

    if (resp?.status === 200 && resp?.error === null) {
      router.push("/");
    } else toast.error("Unauthorized !");
  };

  return (
    <section className="flex min-h-screen w-full items-center justify-center">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>
            Enter your email below to login to your account.
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              <div className="grid gap-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid gap-2">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center justify-between">
                          <span>Password</span>
                          <Link
                            href="/forget-password"
                            className="underline underline-offset-4"
                          >
                            Forget Password
                          </Link>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={visible ? "text" : "password"}
                            placeholder=""
                            className="pr-11"
                            {...field}
                          />
                          <div className="absolute right-3 top-4">
                            {visible ? (
                              <EyeIcon
                                onClick={() => setVisible(!visible)}
                                className="cursor-pointer"
                              />
                            ) : (
                              <EyeOffIcon
                                onClick={() => setVisible(!visible)}
                                className="cursor-pointer"
                              />
                            )}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="w-full pt-4">
                {isLoading ? (
                  <LoadingButton loading className="w-full py-3">
                    Authenticating...
                  </LoadingButton>
                ) : (
                  <Button className="w-full py-3" type="submit">
                    Authenticate
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </section>
  );
};

export default AdminLoginPage;
