import "@repo/ui/globals.css";
import React from "react";

import { TRPCReactProvider } from "~/trpc/react";
import Providers from "../providers";
import { HydrateClient } from "~/trpc/server";
import { Toaster } from "@repo/ui/components/ui/sonner";
import { auth } from "~/server/auth";
import { redirect } from "next/navigation";

type Props = {
  children: React.ReactNode;
};

const Layout = async ({ children }: Props) => {
  const session = await auth();

  if (session?.user) {
    redirect("/");
  }

  return (
    <html>
      <body>
        <TRPCReactProvider>
          <Providers>
            <HydrateClient>{children}</HydrateClient>
            <Toaster richColors />
          </Providers>
        </TRPCReactProvider>
      </body>
    </html>
  );
};

export default Layout;
