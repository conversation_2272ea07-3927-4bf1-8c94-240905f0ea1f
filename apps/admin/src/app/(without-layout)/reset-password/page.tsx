"use client";
import { z } from "zod";
import Link from "next/link";
import { <PERSON><PERSON> } from "@repo/ui/components/ui/button";
import { Input } from "@repo/ui/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { type FieldErrors, useForm } from "react-hook-form";
import { toast } from "@repo/ui/components/ui/sonner";
import { api } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/ui/card";
import { useRouter, useSearchParams } from "next/navigation";
import Loading from "~/app/components/loading";
import { EyeIcon, EyeOffIcon, X } from "lucide-react";
import { Suspense, useEffect, useState } from "react";
import { resetPasswordFormSchema } from "~/server/api/validations/reset-password.validation";

const ResetPasswordPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [visible, setVisible] = useState<boolean>(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] =
    useState<boolean>(false);

  const token = searchParams.get("resetPasswordToken");
  const { mutate: resetPassword, isPending } =
    api.admin.resetPassword.useMutation();

  const form = useForm<z.infer<typeof resetPasswordFormSchema>>({
    resolver: zodResolver(resetPasswordFormSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    if (token) form.setValue("token", token);
  }, [token, searchParams, form]);

  const onSubmit = async ({
    password,
    confirmPassword,
    token,
  }: z.infer<typeof resetPasswordFormSchema>) => {
    resetPassword(
      { password, confirmPassword, token },
      {
        onSuccess: (opts) => {
          router.push("/auth/login");
          toast.success(opts.message);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  const onError = async (
    errors: FieldErrors<z.infer<typeof resetPasswordFormSchema>>,
  ) => {
    toast.error("Check form validation");
  };

  return (
    <div className="flex w-full items-center justify-center py-12">
      <Card className="w-full max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Reset Password</CardTitle>
          <CardDescription>
            Enter your email below to receive link to reset your password
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4">
          <Form {...form}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={visible ? "text" : "password"}
                            placeholder="password"
                            className="pr-11"
                            {...field}
                          />
                          <div className="absolute right-3 top-4">
                            {visible ? (
                              <EyeIcon
                                onClick={() => setVisible(!visible)}
                                className="cursor-pointer"
                              />
                            ) : (
                              <EyeOffIcon
                                onClick={() => setVisible(!visible)}
                                className="cursor-pointer"
                              />
                            )}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={confirmPasswordVisible ? "text" : "password"}
                            placeholder="confirm password"
                            className="pr-11"
                            {...field}
                          />
                          <div className="absolute right-3 top-4">
                            {confirmPasswordVisible ? (
                              <EyeIcon
                                onClick={() =>
                                  setConfirmPasswordVisible(
                                    !confirmPasswordVisible,
                                  )
                                }
                                className="cursor-pointer"
                              />
                            ) : (
                              <EyeOffIcon
                                onClick={() =>
                                  setConfirmPasswordVisible(
                                    !confirmPasswordVisible,
                                  )
                                }
                                className="cursor-pointer"
                              />
                            )}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {isPending ? (
                <LoadingButton className="py-3" loading>
                  Updating
                </LoadingButton>
              ) : (
                <Button
                  onClick={form.handleSubmit(onSubmit, onError)}
                  type="submit"
                  className="py-3"
                >
                  Update Password
                </Button>
              )}
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

const MainPage = () => {
  return (
    <Suspense>
      <ResetPasswordPage />
    </Suspense>
  );
};

export default MainPage;
