"use client";

import { SessionProvider } from "next-auth/react";

import React from "react";
import { GoogleMapsProvider } from "@repo/ui/components/context/google-maps-provider";
import { env } from "~/env";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import NextTopLoader from "nextjs-toploader";

interface ProvidersProps {
  children: React.ReactNode;
}

const Providers: React.FC<ProvidersProps> = ({ children }) => {
  return (
    <SessionProvider>
      <NuqsAdapter>
        <GoogleMapsProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
          {children}
        </GoogleMapsProvider>
      </NuqsAdapter>
      <NextTopLoader
        color="#fc4103"
        initialPosition={0.08}
        crawlSpeed={200}
        height={3}
        crawl={true}
        showSpinner={true}
        easing="ease"
        speed={200}
        shadow="0 0 10px #2299DD,0 0 5px #2299DD"
        template='<div class="bar" role="bar"><div class="peg"></div></div>
  <div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'
        zIndex={1600}
        showAtBottom={false}
      />
    </SessionProvider>
  );
};

export default Providers;
