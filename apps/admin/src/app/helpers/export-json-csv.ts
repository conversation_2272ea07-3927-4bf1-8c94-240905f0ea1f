import jsonToCsvExport from "json-to-csv-export";
import { type IAdminCsv } from "../types/admin";
import { type IPartnerCsv } from "../types/partner";

const exportJsonToCsv = ({
  data,
  fileName = "mydeer-admins-data",
  delimiter = ",",
  headers,
}: {
  data: Array<IAdminCsv> | Array<IPartnerCsv>;
  fileName?: string;
  delimiter?: string;
  headers?: Array<string>;
}): boolean => {
  let isSuccess = false;
  try {
    jsonToCsvExport({
      data,
      filename: fileName,
      delimiter: delimiter,
      headers: headers,
    });

    return (isSuccess = true);
  } catch (err) {
    console.log(err);
    return isSuccess;
  }
};

export default exportJsonToCsv;
