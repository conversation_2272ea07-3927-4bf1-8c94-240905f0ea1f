import {
  Building2,
  LayoutDashboard,
  HelpCircle,
  Newspaper,
  Languages,
  UserCog,
  LandPlot,
  HandshakeIcon,
  MapPinned,
  BarChartHorizontalBig,
  MessageSquareWarning,
  Ruler,
  WorkflowIcon,
  UserX,
  ShoppingBag,
  Star,
  Home,
  Hotel,
  ImageIcon,
  FileText,
  Award,
  Share2,
  Package,
  Contact,
} from "lucide-react";
import type { NavLink } from "../types";

export const AdminParamName = "admin_id";
export const FaqParamName = "faq_id";
export const PropertyTypeParamName = "property_type_id";
export const NewsParamName = "news_id";
export const LanguageParamName = "language_id";
export const PartnerParamName = "partner_id";
export const PartnerStatusParamName = "status";
export const PartnerViewParamName = "view";
export const PostPropertyParamName = "post-property-form";
export const AgentIdParamName = "agentId";
export const CategoryParamName = "category_id";
export const AreaUnitParamName = "area_unit_id";
export const AmenityParamName = "amenity_id";
export const CityParamName = "city_id";
export const PostUserIdParamName = "user_id";
export const PostIdParamName = "post_id";
export const JobRoleParamName = "job_role_id";
export const TestimonialParamName = "testimonial_id";
export const FaqProjectParamName = "project";
export const FaqPageParamName = "page";

export const adminLinks: NavLink[] = [
  {
    name: "Welcome",
    link: "/",
    icon: LayoutDashboard,
  },
  {
    name: "Admins",
    link: "/admins",
    icon: UserCog,
  },
  {
    name: "FAQs",
    link: "/faq",
    icon: HelpCircle,
  },
  {
    name: "News",
    link: "/news",
    icon: Newspaper,
  },
  {
    name: "Languages",
    link: "/languages",
    icon: Languages,
  },
  {
    name: "Partner",
    link: "#",
    icon: HandshakeIcon,
    subLinks: [
      {
        name: "Partners",
        link: "/partners",
        icon: HandshakeIcon,
      },
      {
        name: "Delete Account",
        link: "/delete-partner-account",
        icon: UserX,
      },
    ],
  },
  {
    name: "Customer",
    link: "#",
    icon: Contact,
    subLinks: [
      {
        name: "Customers",
        link: "/customers",
        icon: Contact,
      },
      {
        name: "Delete Account",
        link: "/delete-customer-account",
        icon: UserX,
      },
    ],
  },
  {
    name: "Property",
    link: "#",
    icon: Home,
    subLinks: [
      {
        name: "Cities",
        link: "/cities",
        icon: MapPinned,
      },
      {
        name: "Area Units",
        link: "/area-units",
        icon: Ruler,
      },
      {
        name: "Amenities",
        link: "/amenities",
        icon: Hotel,
      },
      {
        name: "Properties",
        link: "/properties",
        icon: LandPlot,
      },
      {
        name: "Property Types",
        link: "/property-types",
        icon: Building2,
      },
      {
        name: "Property Categories",
        link: "/property-categories",
        icon: BarChartHorizontalBig,
      },
    ],
  },
  {
    name: "Social media posts",
    link: "#",
    icon: Share2,
    subLinks: [
      { name: "Posts", link: "/posts", icon: ImageIcon },
      {
        name: "Reported Posts",
        link: "/reported-posts",
        icon: MessageSquareWarning,
      },
    ],
  },
  {
    name: "Job applications",
    link: "#",
    icon: ShoppingBag,
    subLinks: [
      {
        name: "Resumes",
        link: "/resumes",
        icon: FileText,
      },
      {
        name: "Job Roles",
        link: "/job-roles",
        icon: WorkflowIcon,
      },
    ],
  },
  {
    name: "Testimonails",
    link: "#",
    icon: Star,
    subLinks: [
      {
        name: "Customer Testimonials",
        link: "/customer-testimonials",
        icon: Award,
      },
    ],
  },
];

export const managerLinks: NavLink[] = [
  {
    name: "Dashboard",
    link: "/",
    icon: Package,
  },
  {
    name: "Partners",
    link: "/partners",
    icon: HandshakeIcon,
  },
];
