import { Prisma } from "@repo/database";
import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
  formatDistance,
} from "date-fns";

export const formatPriceAddLabels = (
  price: Prisma.Decimal | null | string,
): string => {
  if (!price) {
    return "0.00";
  }

  const numPrice =
    price instanceof Prisma.Decimal ? price.toNumber() : Number(price);

  if (isNaN(numPrice)) {
    return "0.00";
  }

  if (numPrice < 100000) {
    // Less than 1 lakh
    return numPrice.toLocaleString("en-IN", {
      maximumFractionDigits: 2,
    });
  } else if (numPrice < 10000000) {
    // Less than 1 crore
    return (numPrice / 100000).toFixed(2) + " Lac";
  } else {
    return (numPrice / 10000000).toFixed(2) + " Cr";
  }
};

export const calcPerUnitPriceAccordingToAreaUnit = ({
  areaIn,
  area,
  propertyPrice,
}: {
  areaIn: string;
  area: number | null;
  propertyPrice: Prisma.Decimal | null | string;
}) => {
  const price =
    propertyPrice instanceof Prisma.Decimal
      ? propertyPrice.toNumber()
      : Number(propertyPrice);

  if (isNaN(price) || !area) {
    return "0.00";
  }

  switch (areaIn) {
    case "SQUAREFEET":
      return String(price / area);

    case "SQUAREMETER":
      return String(price / area);

    case "SQUAREYARD":
      return String(price / area);

    default:
      return "0.00";
  }
};

// calc distance show date like - 1s ago, 2h ago, 1d ago
export const formatTime = (inputDate: Date) => {
  const now = new Date();
  const targetDate = new Date(inputDate);

  const seconds = differenceInSeconds(now, targetDate);
  const minutes = differenceInMinutes(now, targetDate);
  const hours = differenceInHours(now, targetDate);
  const days = differenceInDays(now, targetDate);

  if (seconds < 60) {
    return `${seconds}s ago`;
  } else if (minutes < 60) {
    return `${minutes}m ago`;
  } else if (hours < 24) {
    return `${hours}h ago`;
  } else if (days < 30) {
    return `${days}d ago`;
  } else {
    return formatDistance(targetDate, now, { addSuffix: true });
  }
};

// Formats numbers to a human-readable string with k (thousands) and m (millions) notation
export const formatLikesViewsComments = (number: number): string => {
  if (number >= 1_000_000) {
    if (number >= 100_000_000) {
      return `${Math.round(number / 1_000_000)}m`;
    }
    return `${(number / 1_000_000).toFixed(1)}m`.replace(/\.0m$/, "m");
  }

  if (number >= 1_000) {
    if (number >= 100_000) {
      return `${Math.round(number / 1_000)}k`;
    }
    return `${(number / 1_000).toFixed(1)}k`.replace(/\.0k$/, "k");
  }

  return number.toString();
};

export function FormatAreaIn(term: string) {
    switch (term) {
      case "SQUAREMETER":
        return "sqmt";
      case "SQUAREYARD":
        return "sqyd";
      case "SQUAREFEET":
        return "sqft";
    }
  }
  
  export function FormatFacing(term: string | null) {
    switch (term) {
      case "NORTH":
        return "North";
      case "SOUTH":
        return "South";
      case "EAST":
        return "East";
      case "WEST":
        return "West";
      case "NORTH_EAST":
        return "North East";
      case "NORTH_WEST":
        return "North West";
      case "SOUTH_EAST":
        return "South East";
      case "SOUTH_WEST":
        return "South West";
    }
  }
  
  export function FormatPossesionState(term: string | null) {
    switch (term) {
      case "READY_TO_MOVE":
        return "Ready to move";
      case "UNDER_6_MONTHS":
        return "Under 6 months";
      case "UNDER_1_YEAR":
        return "Under 1 year";
      case "UNDER_3_YEARS":
        return "Under 3 years";
    }
  }
  
  export function FormatFurnishing(term: string | null) {
    switch (term) {
      case "SEMIFURNISHED":
        return "Semi-furnished";
      case "RAW":
        return "Raw";
      case "FULLYFURNISHED":
        return "Fully-furnished";
      default:
        return " ";
    }
  }
  