import { cn } from "@repo/ui/lib/utils";
import React from "react";

interface LoadingProps {
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({ className }) => {
  return (
    <div className="flex min-h-60 flex-col">
      <div className="flex flex-auto flex-col items-center justify-center">
        <div className="flex justify-center">
          <div
            className={cn(
              "inline-block size-6 animate-spin rounded-full border-[3px] border-current border-t-transparent text-blue-600",
              className,
            )}
            role="status"
            aria-label="loading"
          >
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Loading;
