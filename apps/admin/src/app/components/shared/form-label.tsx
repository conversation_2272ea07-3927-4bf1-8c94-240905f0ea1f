import { Label } from "@repo/ui/components/ui/label";
import { cn } from "@repo/ui/lib/utils";
import React from "react";

const FormLabel = ({
  className,
  children,
  ...props
}: React.ComponentProps<"label">) => {
  return (
    <Label
      className={cn(
        className,
        "font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl",
      )}
      {...props}
    >
      {children}
    </Label>
  );
};

export default FormLabel;
