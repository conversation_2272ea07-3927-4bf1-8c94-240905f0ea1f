"use client";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@repo/ui/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  //   useSidebar,
} from "@repo/ui/components/ui/sidebar";
import { ChevronDown, CircleUser, Link } from "lucide-react";
import React from "react";
import ToggleDarkMode from "../toggle-dark-mode";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import { Button } from "@repo/ui/components/ui/button";
import UpdatePasswordDialog from "./update-password-dialog";
import { usePathname, useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import {
  LandPlot,
  LayoutPanelLeft,
  MessageCircleQuestion,
  Package,
  ShipWheel,
  Users,
  ListCollapseIcon,
  NewspaperIcon,
  HandshakeIcon,
  MapPinned,
  BarChartHorizontalBig,
  StickyNote,
  MessageSquareWarning,
  Ruler,
  FileBadge,
  WorkflowIcon,
} from "lucide-react";
const adminLinks = [
  {
    name: "Dashboard",
    link: "/",
    icon: Package,
  },
  {
    name: "Admins",
    link: "/admins",
    icon: Users,
  },
  {
    name: "Property",
    // link: "#",
    icon: Users,
    subLinks: [
      {
        name: "Property Categories",
        link: "/property-categories",
        icon: BarChartHorizontalBig,
      },
      {
        name: "Property Types",
        link: "/property-types",
        icon: LayoutPanelLeft,
      },
      {
        name: "Area Units",
        link: "/area-units",
        icon: Ruler,
      },

      {
        name: "Amenities",
        link: "/amenities",
        icon: ShipWheel,
      },
      {
        name: "Properties",
        link: "/properties",
        icon: LandPlot,
      },
      {
        name: "Cities",
        link: "/cities",
        icon: MapPinned,
      },
    ],
  },
  {
    name: "Partners",
    link: "/partners",
    icon: HandshakeIcon,
  },

  {
    name: "Enquiries",
    link: "/enquiries",
    icon: MessageCircleQuestion,
  },
  {
    name: "FAQ",
    link: "/faq",
    icon: ListCollapseIcon,
  },
  {
    name: "News",
    link: "/news",
    icon: NewspaperIcon,
  },

  {
    name: "Social media posts",
    link: "#",
    icon: StickyNote,
    subLinks: [
      { name: "Posts", link: "/posts", icon: StickyNote },
      {
        name: "Reported Posts",
        link: "/reported-posts",
        icon: MessageSquareWarning,
      },
    ],
  },
  {
    name: "Job applications",
    link: "#",
    icon: FileBadge,
    subLinks: [
      {
        name: "Resumes",
        link: "/resumes",
        icon: FileBadge,
      },
      {
        name: "Job Roles",
        link: "/job-roles",
        icon: WorkflowIcon,
      },
    ],
  },
];

const managerLinks = [
  {
    name: "Dashboard",
    link: "/",
    icon: Package,
  },
  {
    name: "Partners",
    link: "/partners",
    icon: HandshakeIcon,
  },
];
const SidebarBodyContent = ({ role }: { role: string | undefined }) => {
  const pathName = usePathname();
  const router = useRouter();
  //   const sidebar = useSidebar();
  const session = useSession();
//   const role = session.data?.user.role;

  const hasAccess = () => {
    if (role === "ADMIN") {
      for (const item of adminLinks) {
        // Check main link
        if (item.link === pathName) {
          return true;
        }
        // Check subLinks if they exist
        if (item.subLinks) {
          for (const subItem of item.subLinks) {
            if (subItem.link === pathName) {
              return true;
            }
          }
        }
      }
      return false;
    } else {
      for (const item of managerLinks) {
        if (item.link === pathName) {
          return true;
        }
      }
      return false;
    }
  };

  if (!hasAccess() || !role) {
    router.push("/");

    return null;
  }

  const logout = async () => {
    await signOut();
    router.push("/auth/login");
  };
  return (
    <SidebarGroup>
      <SidebarGroupLabel>Navbar</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {role === "ADMIN"
            ? adminLinks.map((item, idx) =>
                item.subLinks ? (
                  <Collapsible key={idx} className="group/collapsible">
                    <SidebarGroup>
                      <SidebarGroupLabel asChild>
                        <CollapsibleTrigger>
                          {item.name}
                          <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                        </CollapsibleTrigger>
                      </SidebarGroupLabel>
                      <CollapsibleContent>
                        {/* <SidebarGroupContent /> */}
                        {item.subLinks.map((item) => {
                          return (
                            <SidebarMenuItem key={item.name}>
                              <SidebarMenuButton>
                                <Link
                                  key={idx}
                                  href={item.link}
                                  className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground`}
                                >
                                  <item.icon className="h-5 w-5" />
                                  {item.name}
                                </Link>{" "}
                              </SidebarMenuButton>
                            </SidebarMenuItem>
                          );
                        })}
                      </CollapsibleContent>
                    </SidebarGroup>
                  </Collapsible>
                ) : (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton>
                      <Link
                        key={idx}
                        href={item.link}
                        className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
                      >
                        <item.icon className="h-5 w-5" />
                        {item.name}
                      </Link>{" "}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ),
              )
            : managerLinks.map((item, idx) => {
                return (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton>
                      <Link
                        key={idx}
                        href={item.link}
                        className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
                      >
                        <item.icon className="h-5 w-5" />
                        {item.name}
                      </Link>{" "}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}

          <div className="flex items-center gap-2">
            <ToggleDarkMode />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="secondary"
                  size="icon"
                  className="rounded-full"
                >
                  <CircleUser className="h-5 w-5" />
                  <span className="sr-only">Toggle user menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <UpdatePasswordDialog />
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={logout}
                  className="cursor-pointer"
                  asChild
                >
                  <Button variant="ghost" className="w-full text-sm">
                    Logout
                  </Button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
};

export default SidebarBodyContent;
