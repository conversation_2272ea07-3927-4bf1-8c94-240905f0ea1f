"use client";

import * as React from "react";
import { useEffect } from "react";
import Image from "next/image";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
  useSidebar,
} from "@repo/ui/components/ui/sidebar";
import Link from "next/link";
import { Plus, Minus } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@repo/ui/components/ui/collapsible";
import { usePathname, useRouter } from "next/navigation";
import type { Session } from "next-auth";
import SidebarFooterContent from "./sidebar-footer-content";
import { Separator } from "@repo/ui/components/ui/separator";
import type { NavLink, UserRole } from "~/app/types";
import { adminLinks, managerLinks } from "~/app/helpers/constants";

interface AppSidebarProps {
  session: Session | null;
}

export function AppSidebar({
  session,
}: AppSidebarProps): React.ReactElement | null {
  const { open } = useSidebar();
  const pathName = usePathname();
  const router = useRouter();
  const role = session?.user.role as UserRole;

  const hasAccess = React.useCallback((): boolean => {
    if (!role) return pathName === "/";
    const linksToCheck = role === "ADMIN" ? adminLinks : managerLinks;
    const hasDirectAccess = linksToCheck.some(
      (item) => item.link === pathName && item.link !== "#",
    );
    if (hasDirectAccess) return true;
    return linksToCheck.some((item) =>
      item.subLinks?.some((subItem) => subItem.link === pathName),
    );
  }, [pathName, role]);

  useEffect(() => {
    if (role && !hasAccess()) {
      router.push("/");
    }
  }, [role, hasAccess, router]);

  if (!role) {
    return null;
  }

  const links = role === "ADMIN" ? adminLinks : managerLinks;

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        {open ? (
          <Link href="/" className="flex items-center justify-center">
            <Image
              src="/logos/new-logo.svg"
              alt="MyDeer"
              height={1000}
              width={1000}
              className="h-14 w-auto dark:invert"
            />
          </Link>
        ) : (
          <Image
            src="/icon.png"
            alt="MyDeer"
            width={500}
            height={500}
            className="aspect-square w-10 shrink-0 dark:invert"
          />
        )}
      </SidebarHeader>
      <Separator orientation="horizontal" />
      <SidebarContent className="mt-2">
        <SidebarGroup>
          <SidebarMenu>
            {links.map((item, idx) => renderMenuItem(item, idx, pathName))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
      <Separator orientation="horizontal" />
      <SidebarFooter>
        <SidebarFooterContent session={session} />
      </SidebarFooter>
    </Sidebar>
  );
}

function renderMenuItem(
  item: NavLink,
  idx: number,
  pathName: string,
): React.ReactNode {
  if (item.subLinks) {
    return (
      <Collapsible
        key={`${item.name}-${idx}`}
        className="group/collapsible"
        defaultOpen={item.subLinks.some((sub) => sub.link === pathName)}
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton>
              <item.icon className="mr-2 h-5 w-5" />
              {item.name}
              <Plus className="ml-auto h-4 w-4 group-data-[state=open]/collapsible:hidden" />
              <Minus className="ml-auto h-4 w-4 group-data-[state=closed]/collapsible:hidden" />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.subLinks.map((subItem) => (
                <SidebarMenuSubItem key={subItem.name}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={pathName === subItem.link}
                  >
                    <Link
                      href={subItem.link}
                      className="flex items-center gap-2 text-nowrap"
                    >
                      <subItem.icon className="mr-2 h-4 w-4" />
                      {subItem.name}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    );
  }

  return (
    <SidebarMenuItem key={`${item.name}-${idx}`}>
      <SidebarMenuButton asChild isActive={pathName === item.link}>
        <Link href={item.link} className="flex items-center gap-2 text-nowrap">
          <item.icon className="mr-2 h-5 w-5" />
          {item.name}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
