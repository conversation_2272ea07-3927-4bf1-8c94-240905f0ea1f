import { Button } from "@repo/ui/components/ui/button";
import type { Column } from "@tanstack/react-table";
import { ArrowDown, ArrowUp } from "lucide-react";

const SortableColumnHeader = <T,>({
  column,
  title,
}: {
  column: Column<T, unknown>;
  title: string;
}) => {
  const isSorted = column.getIsSorted();

  return (
    <Button
      variant="ghost"
      size="sm"
      className="flex h-8 items-center gap-1 px-2 pl-0 font-medium hover:bg-muted/30"
      onClick={() => {
        const currentSort = column.getIsSorted();
        if (!currentSort) {
          column.toggleSorting(false);
        } else {
          column.toggleSorting(currentSort === "asc");
        }
      }}
    >
      {title}
      {isSorted ? (
        isSorted === "asc" ? (
          <ArrowUp className="text-primary ml-1 h-4 w-4" />
        ) : (
          <ArrowDown className="text-primary ml-1 h-4 w-4" />
        )
      ) : (
        <ArrowUp className="ml-1 h-4 w-4 opacity-30" />
      )}
    </Button>
  );
};

export default SortableColumnHeader;
