"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
// import { toast } from "@repo/ui/hooks/use-toast";
import { signOut, useSession } from "next-auth/react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { api } from "~/trpc/react";
import { EyeIcon, EyeOffIcon } from "lucide-react";

const adminPasswordChangeSchema = z.object({
  id: z.string().optional(),
  oldPassword: z
    .string()
    .min(8, { message: "Password must be of minimum 8 characters " }),
  newPassword: z
    .string()
    .min(8, { message: "Password must be of minimum 8 characters " }),
});

const UpdatePasswordDialog = () => {
  //   const { data: session } = useSession();
  const [open, setOpen] = useState<boolean>(false);
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const [newPasswordVisible, setNewPasswordVisible] = useState<boolean>(false);
  const changePasswordMutation = api.admin.updatePassword.useMutation();

  const form = useForm<z.infer<typeof adminPasswordChangeSchema>>({
    resolver: zodResolver(adminPasswordChangeSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
    },
  });

  const onSubmit = (values: z.infer<typeof adminPasswordChangeSchema>) => {
    console.log(values);

    const { oldPassword, newPassword } = values;

    changePasswordMutation.mutate(
      {
        // id: session.user.id,
        oldPassword: oldPassword,
        newPassword: newPassword,
      },
      {
        onSuccess: () => {
          toast.success("success");
          signOut().catch((err) => console.log(err));
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <span
          className="cursor-pointer text-sm"
          onClick={(e) => e.stopPropagation()}
        >
          Change password
        </span>
      </DialogTrigger>
      <DialogContent
        className="sm:max-w-[425px]"
        onClick={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>Change Password</DialogTitle>
          <DialogDescription>Change your password</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="grid gap-4 py-4"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <div>
              <FormField
                control={form.control}
                name="oldPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Old Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={passwordVisible ? "text" : "password"}
                          placeholder="Old Password"
                          className="w-full pr-10 md:pr-10"
                          {...field}
                        />
                        {passwordVisible ? (
                          <EyeIcon
                            onClick={() => setPasswordVisible(!passwordVisible)}
                            className="absolute right-4 top-1/2 -translate-y-1/2"
                          ></EyeIcon>
                        ) : (
                          <EyeOffIcon
                            onClick={() => setPasswordVisible(!passwordVisible)}
                            className="absolute right-4 top-1/2 -translate-y-1/2"
                          ></EyeOffIcon>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={newPasswordVisible ? "text" : "password"}
                          placeholder="New Password"
                          className="w-full pr-10 md:pr-10"
                          {...field}
                        />
                        {newPasswordVisible ? (
                          <EyeIcon
                            onClick={() =>
                              setNewPasswordVisible(!newPasswordVisible)
                            }
                            className="absolute right-4 top-1/2 -translate-y-1/2"
                          ></EyeIcon>
                        ) : (
                          <EyeOffIcon
                            onClick={() =>
                              setNewPasswordVisible(!newPasswordVisible)
                            }
                            className="absolute right-4 top-1/2 -translate-y-1/2"
                          ></EyeOffIcon>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit">Save changes</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default UpdatePasswordDialog;
