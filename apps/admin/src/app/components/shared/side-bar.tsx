"use client";

import React from "react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@repo/ui/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@repo/ui/components/ui/collapsible";
import Link from "next/link";
import {
  CircleUser,
  LandPlot,
  LayoutPanelLeft,
  Menu,
  MessageCircleQuestion,
  Package,
  ShipWheel,
  Users,
  ListCollapseIcon,
  NewspaperIcon,
  HandshakeIcon,
  MapPinned,
  BarChartHorizontalBig,
  StickyNote,
  MessageSquareWarning,
  Ruler,
  FileBadge,
  WorkflowIcon,
  ChevronDown,
  UserX,
} from "lucide-react";

import { Button } from "@repo/ui/components/ui/button";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@repo/ui/components/ui/sheet";
import { useState } from "react";
import { signOut } from "next-auth/react";

import Logo from "~/../public/logos/new-logo.svg";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import ToggleDarkMode from "../toggle-dark-mode";
import UpdatePasswordDialog from "./update-password-dialog";

interface SideBarProps {
  role: string;
}

const Links = [
  {
    name: "Dashboard",
    link: "/",
    icon: Package,
  },
  {
    name: "Admins",
    link: "/admins",
    icon: Users,
  },
  {
    name: "Partners",
    link: "/partners",
    icon: HandshakeIcon,
  },
  {
    name: "Property Categories",
    link: "/property-categories",
    icon: BarChartHorizontalBig,
  },
  {
    name: "Property Types",
    link: "/property-types",
    icon: LayoutPanelLeft,
  },
  {
    name: "Area Units",
    link: "/area-units",
    icon: Ruler,
  },

  {
    name: "Amenities",
    link: "/amenities",
    icon: ShipWheel,
  },
  {
    name: "Properties",
    link: "/properties",
    icon: LandPlot,
  },
  {
    name: "Enquiries",
    link: "/enquiries",
    icon: MessageCircleQuestion,
  },
  {
    name: "FAQ",
    link: "/faq",
    icon: ListCollapseIcon,
  },
  {
    name: "News",
    link: "/news",
    icon: NewspaperIcon,
  },
  {
    name: "Cities",
    link: "/cities",
    icon: MapPinned,
  },
  { name: "Posts", link: "/posts", icon: StickyNote },
  {
    name: "Reported Posts",
    link: "/reported-posts",
    icon: MessageSquareWarning,
  },
  {
    name: "Resumes",
    link: "/resumes",
    icon: FileBadge,
  },
  {
    name: "Job Roles",
    link: "/job-roles",
    icon: WorkflowIcon,
  },
  {
    name: "Delete Accounts",
    link: "/delete-account",
    icon: UserX,
  },
];

const managerLinks = [
  {
    name: "Dashboard",
    link: "/",
    icon: Package,
  },
  {
    name: "Partners",
    link: "/partners",
    icon: HandshakeIcon,
  },
];

const SideBar: React.FC<SideBarProps> = ({ role }) => {
  const pathName = usePathname();
  const router = useRouter();
  const [open, setOpen] = useState<boolean>(false);

  const logout = async () => {
    await signOut();
    router.push("/auth/login");
  };

  const hasAccess = () => {
    if (role === "ADMIN") {
      for (const item of Links) {
        if (item.link === pathName) {
          return true;
        }
      }
      return false;
    } else {
      for (const item of managerLinks) {
        if (item.link === pathName) {
          return true;
        }
      }

      return false;
    }
  };

  if (!hasAccess()) {
    router.push("/");

    return null;
  }

  return (
    // <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[200px_1fr]">
    //   <div className="hidden border-r bg-muted/40 md:block">
    //     <div className="flex h-full max-h-screen flex-col gap-2">
    //       <div className="flex h-14 items-center justify-center border-b px-4 lg:h-[60px] lg:px-6">
    //         <Link href="/" className="flex items-center justify-center">
    //           <Image
    //             src={Logo}
    //             alt="MyDeer"
    //             className="h-14 w-auto dark:invert"
    //           />
    //         </Link>
    //       </div>
    //       {/* Desktop Navigation */}
    //       <div className="flex-1">
    //         <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
    //           {role === "ADMIN"
    //             ? Links.map((item, idx) => (
    //                 <Link
    //                   key={idx}
    //                   href={item.link}
    //                   className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
    //                 >
    //                   <item.icon className="h-5 w-5" />
    //                   {item.name}
    //                 </Link>
    //               ))
    //             : managerLinks.map((item, idx) => {
    //                 return (
    //                   <Link
    //                     key={idx}
    //                     href={item.link}
    //                     className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
    //                   >
    //                     <item.icon className="h-5 w-5" />
    //                     {item.name}
    //                   </Link>
    //                 );
    //               })}
    //         </nav>
    //       </div>
    //     </div>
    //   </div>
    //   <div className="flex flex-col">
    //     <header className="flex h-14 items-center justify-between gap-4 border-b bg-muted/40 px-4 md:justify-end lg:h-[60px] lg:px-6">
    //       <Sheet open={open} onOpenChange={setOpen}>
    //         <SheetTrigger asChild>
    //           <Button
    //             variant="outline"
    //             size="icon"
    //             className="shrink-0 md:hidden"
    //           >
    //             <Menu className="h-5 w-5" />
    //             <span className="sr-only">Toggle navigation menu</span>
    //           </Button>
    //         </SheetTrigger>
    //         <SheetContent side="left" className="flex flex-col">
    //           <nav className="grid gap-2 text-lg font-medium">
    //             {role === "ADMIN"
    //               ? Links.map((item, idx) => (
    //                   <Link
    //                     key={idx}
    //                     href={item.link}
    //                     className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
    //                   >
    //                     <item.icon className="h-5 w-5" />
    //                     {item.name}
    //                   </Link>
    //                 ))
    //               : managerLinks.map((item, idx) => {
    //                   return (
    //                     <Link
    //                       key={idx}
    //                       href={item.link}
    //                       className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
    //                     >
    //                       <item.icon className="h-5 w-5" />
    //                       {item.name}
    //                     </Link>
    //                   );
    //                 })}
    //           </nav>
    //         </SheetContent>
    //       </Sheet>

    //   <div className="flex items-center gap-2">
    //     <ToggleDarkMode />
    //     <DropdownMenu>
    //       <DropdownMenuTrigger asChild>
    //         <Button
    //           variant="secondary"
    //           size="icon"
    //           className="rounded-full"
    //         >
    //           <CircleUser className="h-5 w-5" />
    //           <span className="sr-only">Toggle user menu</span>
    //         </Button>
    //       </DropdownMenuTrigger>
    //       <DropdownMenuContent align="end">
    //         <DropdownMenuLabel>My Account</DropdownMenuLabel>
    //         <DropdownMenuSeparator />
    //         <DropdownMenuItem asChild>
    //           <UpdatePasswordDialog />
    //         </DropdownMenuItem>
    //         <DropdownMenuItem
    //           onClick={logout}
    //           className="cursor-pointer"
    //           asChild
    //         >
    //           <Button variant="ghost" className="w-full text-sm">
    //             Logout
    //           </Button>
    //         </DropdownMenuItem>
    //       </DropdownMenuContent>
    //     </DropdownMenu>
    //   </div>
    //     </header>
    //     {/* old classes on main tag => flex flex-1 flex-col gap-4 p-4 lg:w-[824.8px] lg:max-w-[1165px] lg:gap-6 lg:p-6 xl:w-[1166px] */}
    //     <main className="p-4">{children}</main>
    //   </div>
    // </div>

    <Sidebar>
      <SidebarContent>
        <Collapsible defaultOpen className="group/collapsible">
          <SidebarGroup>
            <SidebarGroupLabel asChild>
              <CollapsibleTrigger>
                Help
                <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
              </CollapsibleTrigger>
            </SidebarGroupLabel>
            <CollapsibleContent>
              <SidebarGroupContent />
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>
        <SidebarGroup>
          <SidebarGroupLabel>Application</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {role === "ADMIN"
                ? Links.map((item, idx) => (
                    <Link
                      key={idx}
                      href={item.link}
                      className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
                    >
                      <item.icon className="h-5 w-5" />
                      {item.name}
                    </Link>
                  ))
                : managerLinks.map((item, idx) => {
                    return (
                      <Link
                        key={idx}
                        href={item.link}
                        className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${pathName === item.link ? "text-primary bg-muted" : "text-muted-foreground"}`}
                      >
                        <item.icon className="h-5 w-5" />
                        {item.name}
                      </Link>
                    );
                  })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
};

export default SideBar;
