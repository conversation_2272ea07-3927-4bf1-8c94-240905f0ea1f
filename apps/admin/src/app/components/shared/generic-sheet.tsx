"use client";

import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON><PERSON><PERSON>,
} from "@repo/ui/components/ui/sheet";
import { X } from "lucide-react";
import type { ReactNode } from "react";

/**
 * A reusable sheet component that can be used for modals, forms, or other content.
 * This component is designed to work with the `useSheet` hook for state management.
 *
 * @param {Object} props - The props for the GenericSheet component.
 * @param {boolean} props.isOpen - Controls whether the sheet is open or closed.
 * @param {Function} props.onClose - Callback function to close the sheet.
 * @param {string} props.title - The title displayed at the top of the sheet.
 * @param {ReactNode} props.children - The content to be displayed inside the sheet.
 */
export const GenericSheet = ({
  isOpen,
  onClose,
  title,
  children,
}: {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
}) => {
  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent className="min-w-full space-y-4 overflow-y-auto rounded-l-xl md:min-w-[500px] lg:min-w-[600px] 2xl:min-w-[800px]">
        <SheetHeader className="flex flex-row items-center justify-between">
          <SheetTitle className="font-medium lg:text-xl">{title}</SheetTitle>
          <SheetClose>
            <X className="size-5" />
          </SheetClose>
        </SheetHeader>
        {children}
      </SheetContent>
    </Sheet>
  );
};
