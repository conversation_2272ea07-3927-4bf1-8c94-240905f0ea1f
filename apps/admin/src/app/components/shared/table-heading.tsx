import { cn } from "@repo/ui/lib/utils";
import React from "react";
import type { DivProps } from "~/app/types";

const TableHeading = ({
  children,
  leftIcon,
  rightIcon,
  className,
  ...props
}: DivProps & {
  rightIcon?: React.ReactNode;
  leftIcon?: React.ReactNode;
}) => {
  return (
    <div
      className={cn("flex cursor-pointer items-center gap-2", className)}
      {...props}
    >
      {leftIcon}
      {children}
      {rightIcon}
    </div>
  );
};

export default TableHeading;
