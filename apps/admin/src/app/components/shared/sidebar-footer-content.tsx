import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@repo/ui/components/ui/sidebar";
import { LogOut, Settings, User2 } from "lucide-react";
import type { Session } from "next-auth";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import React from "react";
import UpdatePasswordDialog from "./update-password-dialog";

const SidebarFooterContent = ({ session }: { session: Session | null }) => {
  const router = useRouter();
  const { open } = useSidebar();

  const logout = async () => {
    await signOut();
    router.push("/auth/login");
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger className="w-full">
            <SidebarMenuButton className="w-full rounded-md bg-background/50 px-3 py-2 shadow-sm hover:bg-accent/50">
              <User2 className="text-primary mr-2 h-5 w-5 shrink-0" />
              {open ? (
                <div className="flex flex-1 flex-col items-start text-sm">
                  <p className="font-medium">{session?.user.name}</p>
                  <p className="max-w-[160px] truncate text-xs text-muted-foreground">
                    {session?.user.email}
                  </p>
                </div>
              ) : (
                <span className="sr-only">{session?.user.name}</span>
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="top" align="start" className="w-60">
            <DropdownMenuItem
              className="flex items-center gap-2 py-2"
              onClick={(e) => e.stopPropagation()}
            >
              <Settings className="h-4 w-4" />
              <UpdatePasswordDialog />
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={logout}
              className="flex cursor-pointer items-center gap-2 py-2 text-destructive hover:text-destructive"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
};

export default SidebarFooterContent;
