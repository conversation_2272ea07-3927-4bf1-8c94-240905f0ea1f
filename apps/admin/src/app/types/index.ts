import type { Prisma } from "@repo/database";

// HTML element types
export type DivProps = React.ComponentPropsWithoutRef<"div">;

// Prisma types
export type TAdminUser = Prisma.AdminUserGetPayload<{
  select: {
    id: true;
    name: true;
    email: true;
    active: true;
    userType: true;
    createdAt: true;
    updatedAt: true;
  };
}>;

export type ExtendedAreaUnits = Prisma.AreaUnitGetPayload<{
  include: { category: true };
}>;

export type TPartner = Prisma.UserGetPayload<{
  select: {
    id: true;
    name: true;
    email: true;
    emailVerified: true;
    phoneNumber: true;
    statusUpdatedAt: true;
    statusUpdatedBy: true;
    statusUpdateRemarks: true;
    reraNumber: true;
    gstNumber: true;
    createdAt: true;
    updatedAt: true;
    active: true;
    verifiedAgent: true;
    experience: true;
    propertiesSold: true;
    rating: true;
    filePublicUrl: true;
    cloudinaryProfileImageUrl: true;
  };
}>;

export type TCustomer = Prisma.CustomerGetPayload<{
  select: {
    id: true;
    name: true;
    email: true;
    phoneNumber: true;
    createdAt: true;
    updatedAt: true;
    profileImagePublicUrl: true;
    locationAddress: true;
    connections: {
      select: {
        id: true;
        agent: {
          select: {
            id: true;
            name: true;
            email: true;
            phoneNumber: true;
            filePublicUrl: true;
            rating: true;
            experience: true;
          };
        };
      };
    };
  };
}>;

export type ExtendedAmenity = Prisma.AmenitiesGetPayload<{
  select: {
    id: true;
    name: true;
    fileKey: true;
    filePublicUrl: true;
    createdAt: true;
    updatedAt: true;
  };
}>;

export type ExtendedCity = Prisma.CustomerTestimonialsGetPayload<{
  include: {
    city: {
      select: {
        name: true;
      };
    };
  };
}>;

// Miscellaneous types
export type TStatus = "success" | "error" | "loading" | "idle";

export type NavLink = {
  name: string;
  link: string;
  icon: React.ElementType;
  subLinks?: SubLink[];
};

export type SubLink = {
  name: string;
  link: string;
  icon: React.ElementType;
};

export type UserRole = "ADMIN" | "MANAGER" | undefined;

// below are the all possible error codes from MeiliSearch according to there docs.
type MeiliSearchErrorCode =
  | "api_key_already_exists"
  | "api_key_not_found"
  | "bad_request"
  | "batch_not_found"
  | "database_size_limit_reached"
  | "document_fields_limit_reached"
  | "document_not_found"
  | "dump_process_failed"
  | "facet_search_disabled"
  | "feature_not_enabled"
  | "immutable_api_key_actions"
  | "immutable_api_key_created_at"
  | "immutable_api_key_expires_at"
  | "immutable_api_key_indexes"
  | "immutable_api_key_key"
  | "immutable_api_key_uid"
  | "immutable_api_key_updated_at"
  | "immutable_index_uid"
  | "immutable_index_updated_at"
  | "index_already_exists"
  | "index_creation_failed"
  | "index_not_found"
  | "index_primary_key_already_exists"
  | "index_primary_key_multiple_candidates_found"
  | "internal"
  | "invalid_api_key"
  | "invalid_api_key_actions"
  | "invalid_api_key_description"
  | "invalid_api_key_expires_at"
  | "invalid_api_key_indexes"
  | "invalid_api_key_limit"
  | "invalid_api_key_name"
  | "invalid_api_key_offset"
  | "invalid_api_key_uid"
  | "invalid_search_attributes_to_search_on"
  | "invalid_content_type"
  | "invalid_document_csv_delimiter"
  | "invalid_document_id"
  | "invalid_document_fields"
  | "invalid_document_filter"
  | "invalid_document_limit"
  | "invalid_document_offset"
  | "invalid_document_geo_field"
  | "invalid_facet_search_facet_name"
  | "invalid_facet_search_facet_query"
  | "invalid_index_limit"
  | "invalid_index_offset"
  | "invalid_index_uid"
  | "invalid_index_primary_key"
  | "invalid_multi_search_query_federated"
  | "invalid_multi_search_query_pagination"
  | "invalid_multi_search_query_position"
  | "invalid_multi_search_weight"
  | "invalid_multi_search_queries_ranking_rules"
  | "invalid_multi_search_facets"
  | "invalid_multi_search_sort_facet_values_by"
  | "invalid_multi_search_query_facets"
  | "invalid_multi_search_merge_facets"
  | "invalid_multi_search_max_values_per_facet"
  | "invalid_multi_search_facet_order"
  | "invalid_multi_search_facets_by_index"
  | "invalid_multi_search_remote"
  | "invalid_network_self"
  | "invalid_network_remotes"
  | "invalid_network_url"
  | "invalid_network_search_api_key"
  | "invalid_search_attributes_to_crop"
  | "invalid_search_attributes_to_highlight"
  | "invalid_search_attributes_to_retrieve"
  | "invalid_search_crop_length"
  | "invalid_search_crop_marker"
  | "invalid_search_embedder"
  | "invalid_search_facets"
  | "invalid_search_filter"
  | "invalid_search_highlight_post_tag"
  | "invalid_search_highlight_pre_tag"
  | "invalid_search_hits_per_page"
  | "invalid_search_hybrid_query"
  | "invalid_search_limit"
  | "invalid_search_locales"
  | "invalid_settings_facet_search"
  | "invalid_settings_localized_attributes"
  | "invalid_search_matching_strategy"
  | "invalid_search_offset"
  | "invalid_settings_prefix_search"
  | "invalid_search_page"
  | "invalid_search_q"
  | "invalid_search_ranking_score_threshold"
  | "invalid_search_show_matches_position"
  | "invalid_search_sort"
  | "invalid_settings_displayed_attributes"
  | "invalid_settings_distinct_attribute"
  | "invalid_settings_faceting_sort_facet_values_by"
  | "invalid_settings_faceting_max_values_per_facet"
  | "invalid_settings_filterable_attributes"
  | "invalid_settings_pagination"
  | "invalid_settings_ranking_rules"
  | "invalid_settings_searchable_attributes"
  | "invalid_settings_search_cutoff_ms"
  | "invalid_settings_sortable_attributes"
  | "invalid_settings_stop_words"
  | "invalid_settings_synonyms"
  | "invalid_settings_typo_tolerance"
  | "invalid_similar_id"
  | "not_found_similar_id"
  | "invalid_similar_attributes_to_retrieve"
  | "invalid_similar_embedder"
  | "invalid_similar_filter"
  | "invalid_similar_limit"
  | "invalid_similar_offset"
  | "invalid_similar_show_ranking_score"
  | "invalid_similar_show_ranking_score_details"
  | "invalid_similar_ranking_score_threshold"
  | "invalid_state"
  | "invalid_store_file"
  | "invalid_swap_duplicate_index_found"
  | "invalid_swap_indexes"
  | "invalid_task_after_enqueued_at"
  | "invalid_task_after_finished_at"
  | "invalid_task_after_started_at"
  | "invalid_task_before_enqueued_at"
  | "invalid_task_before_finished_at"
  | "invalid_task_before_started_at"
  | "invalid_task_canceled_by"
  | "invalid_task_index_uids"
  | "invalid_task_limit"
  | "invalid_task_statuses"
  | "invalid_task_types"
  | "invalid_task_uids"
  | "io_error"
  | "index_primary_key_no_candidate_found"
  | "malformed_payload"
  | "missing_api_key_actions"
  | "missing_api_key_expires_at"
  | "missing_api_key_indexes"
  | "missing_authorization_header"
  | "missing_content_type"
  | "missing_document_filter"
  | "missing_document_id"
  | "missing_index_uid"
  | "missing_facet_search_facet_name"
  | "missing_master_key"
  | "missing_network_url"
  | "missing_payload"
  | "missing_swap_indexes"
  | "missing_task_filters"
  | "no_space_left_on_device"
  | "not_found"
  | "payload_too_large"
  | "task_not_found"
  | "too_many_open_files"
  | "too_many_search_requests"
  | "unretrievable_document"
  | "vector_embedding_error"
  | "remote_bad_response"
  | "remote_bad_request"
  | "remote_could_not_send_request"
  | "remote_invalid_api_key"
  | "remote_remote_error"
  | "remote_timeout";

export type MeiliSearchError = {
  message: string;
  code: MeiliSearchErrorCode;
  type: "invalid_request" | "internal" | "auth" | "system";
  link: string;
};
