"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { PropertyTypeParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import PropertyTypeAddUpdateForm from "./property-type-add-update-form";

const PropertyTypeFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } = useSheet(
    PropertyTypeParamName,
  );

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Property Type
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Property Type" : "Add Property Type"}
      >
        {isOpen && <PropertyTypeAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default PropertyTypeFormSheet;
