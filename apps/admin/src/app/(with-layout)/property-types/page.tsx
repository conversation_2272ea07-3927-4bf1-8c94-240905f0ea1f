import { api, HydrateClient } from "~/trpc/server";
import PropertyTypesDataTable from "./property-types-data-table";
import PropertyTypeFormSheet from "./property-type-form-sheet";

type SearchParams = Promise<{ property_type_id?: string }>;

const PropertyTypesPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const propertyTypes = await api.property.getAllPropertyType();

  if (searchParams.property_type_id) {
    await api.property.getPropertyTypeById.prefetch({
      propertyTypeId: searchParams.property_type_id,
    });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Property Types
      </h1>

      <PropertyTypeFormSheet />
      <PropertyTypesDataTable data={propertyTypes} />
    </HydrateClient>
  );
};

export default PropertyTypesPage;
