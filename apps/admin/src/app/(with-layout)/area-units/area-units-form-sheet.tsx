"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { AreaUnitParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import AreaUnitsAddUpdateForm from "./area-units-add-update-form";

const AreaUnitsFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(AreaUnitParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Area Unit
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Area Unit" : "Add Area Unit"}
      >
        {isOpen && <AreaUnitsAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default AreaUnitsFormSheet;
