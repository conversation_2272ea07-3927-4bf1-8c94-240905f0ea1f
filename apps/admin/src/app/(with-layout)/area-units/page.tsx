import { api, HydrateClient } from "~/trpc/server";
import {
  AreaUnitsDataTableColumns,
  AreaUnitsDataTable,
} from "./area-units-data-table";
import AreaUnitsFormSheet from "./area-units-form-sheet";

type SearchParams = Promise<{ area_unit_id?: string }>;

const AreaUnitsPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const areaUnits = await api.areaUnit.getAllAreaUnits();

  if (searchParams.area_unit_id) {
    await api.areaUnit.getAreaUnitById.prefetch({
      areaUnitId: searchParams.area_unit_id,
    });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Area Units
      </h1>

      <AreaUnitsFormSheet />

      <AreaUnitsDataTable
        data={areaUnits}
        columns={AreaUnitsDataTableColumns}
      />
    </HydrateClient>
  );
};

export default AreaUnitsPage;
