import React from "react";
import type { Prisma } from "@repo/database";
import DataTableActions from "./data-table-actions";
import PropertyCard from "@repo/ui/components/shared/property-card";

type PropertyWithUserDetails = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    propertyType: true;
    PropertyCategory: true;
    utilities: true;
    amenities: true;
    user: {
      include: {
        company: true;
      };
    };
    mediaSections: {
      include: {
        media: true;
      };
    };
  };
}>;

interface PropertyCardViewProps {
  properties: PropertyWithUserDetails[];
}

const PropertyCardView = ({ properties }: PropertyCardViewProps) => {
  return (
    <div className="grid gap-5 py-4 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {properties.map((property) => {
        return (
          <div key={property.id} className="relative">
            <PropertyCard
              //@ts-ignore
              property={property}
              id={property.id}
              propertyOwnerId={property.userId}
              locationIcon="/location.svg"
              className="min-w-min max-w-full"
              contactOrCheckResponsesButton={<div />}
            />

            <div className="absolute right-4 top-4 flex w-fit items-center gap-4 rounded-md bg-white px-1">
              <div className="flex items-center gap-2">
                <span className="flex aspect-square h-8 min-w-8 items-center justify-center rounded-full bg-slate-100 p-2">
                  {property.user.name.at(0)}
                </span>
                <span className="text-sm">{property.user.name}</span>
              </div>

              {property.propertyStatus !== "SOLD" && (
                <DataTableActions
                  id={property.id}
                  agentId={property.user.id}
                  propertyCategoryId={property.propertyCategoryId ?? ""}
                />
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PropertyCardView;
