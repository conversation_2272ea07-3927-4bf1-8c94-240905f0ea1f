"use client";

import { TableProperties, LayoutGrid } from "lucide-react";
import { Button } from "@repo/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import Link from "next/link";

interface ViewSelectionDropdownProps {
  currentView: "table" | "cards";
  currentStatus: string;
}

const ViewSelectionDropdown = ({
  currentView,
  currentStatus,
}: ViewSelectionDropdownProps) => {
  const views = [
    {
      value: "table",
      label: "Table View",
      icon: <TableProperties className="mr-2 h-4 w-4" />,
    },
    {
      value: "cards",
      label: "Card View",
      icon: <LayoutGrid className="mr-2 h-4 w-4" />,
    },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          {currentView === "table" ? (
            <TableProperties className="h-4 w-4" />
          ) : (
            <LayoutGrid className="h-4 w-4" />
          )}
          {currentView === "table" ? "Table View" : "Card View"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {views.map((view) => (
          <Link
            key={view.value}
            href={`/properties?status=${currentStatus}&view=${view.value}`}
            className="no-underline"
          >
            <DropdownMenuItem className="cursor-pointer">
              <div className="flex items-center">
                {view.icon}
                {view.label}
              </div>
            </DropdownMenuItem>
          </Link>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ViewSelectionDropdown;
