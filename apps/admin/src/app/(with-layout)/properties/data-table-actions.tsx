"use client";

import React, { useState } from "react";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  CheckCheck,
  LoaderCircle,
  MoreHorizontal,
  X,
  Edit,
} from "lucide-react";
import { useRouter } from "next/navigation";
import TableHeading from "~/app/components/shared/table-heading";
import WrappedAlert from "~/app/components/shared/wrapped-alert";
import RejectionForm from "./rejection-form";

const DataTableActions = ({
  id,
  agentId,
  propertyCategoryId,
}: {
  id: string;
  agentId: string;
  propertyCategoryId: string;
}) => {
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const router = useRouter();

  const { mutate: updateStatus } =
    api.property.updatePropertyStatus.useMutation();
  const trpcUtils = api.useUtils();

  const update = ({
    id,
    status,
  }: {
    id: string | undefined;
    status: "ACTIVE" | "REJECTED" | "InREVIEW" | undefined;
  }) => {
    if (id && status) {
      updateStatus(
        { propertyId: id, status: status },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            trpcUtils.invalidate().catch((e) => console.log("refetched", e));
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    }
  };

  const handleEditProperty = () => {
    router.push(
      `/properties?propertyId=${id}&agentId=${agentId}&post-property-form=open&propertyCategoryId=${propertyCategoryId}`,
    );
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <TableHeading rightIcon={<MoreHorizontal />}>
            <span className="sr-only">Open menu</span>
          </TableHeading>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem>
            <WrappedAlert
              title="Approve Property"
              description="Are you sure you want to approve this property?"
              confirmText="Approve"
              onConfirm={() => {
                update({
                  id: id,
                  status: "ACTIVE",
                });
              }}
              trigger={
                <div
                  onClick={(e) => e.stopPropagation()}
                  className="flex w-full items-center gap-2"
                >
                  <CheckCheck className="size-4" />
                  Approve Property
                </div>
              }
            />
          </DropdownMenuItem>

          <DropdownMenuItem onClick={() => setRejectDialogOpen(true)}>
            <div className="flex w-full items-center gap-2">
              <X className="size-4" />
              Reject Property
            </div>
          </DropdownMenuItem>

          <DropdownMenuItem>
            <WrappedAlert
              title="Mark as Under Review"
              description="Are you sure you want to mark this property as under review?"
              confirmText="Confirm"
              onConfirm={() => {
                update({
                  id: id,
                  status: "InREVIEW",
                });
              }}
              trigger={
                <div
                  onClick={(e) => e.stopPropagation()}
                  className="flex w-full items-center gap-2"
                >
                  <LoaderCircle className="size-4" />
                  Mark as Under Review
                </div>
              }
            />
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleEditProperty}>
            <div className="flex items-center gap-2">
              <Edit className="size-4" />
              Edit Property
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <RejectionForm
        propertyId={id}
        open={rejectDialogOpen}
        onOpenChange={setRejectDialogOpen}
      />
    </>
  );
};
export default DataTableActions;
