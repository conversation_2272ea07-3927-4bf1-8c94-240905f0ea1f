"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import React from "react";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";

const formSchema = z.object({
  remarks: z.string().min(2).max(50),
});

const RejectionForm = ({
  propertyId,
  open,
  onOpenChange,
}: {
  propertyId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  const { mutate: updateStatus } =
    api.property.updatePropertyStatus.useMutation();
  const trpcUtils = api.useUtils();

  const update = ({
    id,
    status,
    remarks,
  }: {
    id: string | undefined;
    status: "ACTIVE" | "REJECTED" | "InREVIEW" | undefined;
    remarks: string;
  }) => {
    if (id && status) {
      updateStatus(
        { propertyId: id, status: status, remarks },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            trpcUtils.invalidate().catch((e) => console.log("refetched", e));
            onOpenChange(false);
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    }
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      remarks: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    const { remarks } = values;
    update({ id: propertyId, status: "REJECTED", remarks });
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-medium">
            Reject Property
          </DialogTitle>
          <DialogDescription>
            Please provide a reason for rejecting this property.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="remarks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason for rejecting</FormLabel>
                  <FormControl>
                    <Input placeholder="type here..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex flex-row items-center justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" variant="destructive">
                Reject
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default RejectionForm;
