"use client";

import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/ui/popover";
import { Filter, ChevronDown, Info } from "lucide-react";
import { Button } from "@repo/ui/components/ui/button";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import type { Prisma } from "@repo/database";
import { cn } from "@repo/ui/lib/utils";
import DataTableActions from "./data-table-actions";
import SortableColumnHeader from "~/app/components/shared/sortable-column-header";

type PropertyWithUserDetails = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    propertyType: true;
    PropertyCategory: true;
    utilities: true;
    amenities: true;
    user: {
      include: {
        company: true;
      };
    };
    comments: {
      include: {
        user: true;
      };
    };
    mediaSections: {
      include: {
        media: true;
      };
    };
  };
}>;

export const PropertiesDataTableColumns: ColumnDef<PropertyWithUserDetails>[] =
  [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "id",
      header: () => <button className="flex justify-start">Property Id</button>,
      cell: ({ row }) => <div>{row.getValue("id")}</div>,
    },
    {
      accessorKey: "propertyTitle",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Title" />
      ),
      cell: ({ row }) => {
        return <div className="font-medium">{row.original.propertyTitle}</div>;
      },
    },
    {
      accessorKey: "propertyFor",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="For" />
      ),
      cell: ({ row }) => <div>{row.original.propertyFor}</div>,
    },
    {
      accessorKey: "PropertyCategory",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Category" />
      ),
      cell: ({ row }) => <div>{row.original.PropertyCategory?.name}</div>,
    },
    {
      accessorKey: "propertyType",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Type" />
      ),
      cell: ({ row }) => <div>{row.original.propertyType.name}</div>,
    },
    {
      accessorKey: "propertyStatus",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => (
        <div
          className={cn("flex items-center gap-2", {
            "font-semibold text-green-500":
              row.original.propertyStatus === "ACTIVE",
            "font-semibold text-red-500":
              row.original.propertyStatus === "REJECTED",
            "font-semibold text-yellow-500":
              row.original.propertyStatus === "InREVIEW",
            "font-semibold text-blue-500":
              row.original.propertyStatus === "PENDING",
            "font-semibold text-secondary-2-750":
              row.original.propertyStatus === "SOLD",
          })}
        >
          {row.original.propertyStatus}
          {row.original.propertyStatus === "REJECTED" ? (
            <Popover>
              <PopoverTrigger>
                <Info
                  size={14}
                  className="transition-all duration-300 hover:scale-125 hover:cursor-pointer hover:text-secondary-2-750"
                />
              </PopoverTrigger>
              <PopoverContent className="w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Updated by</TableHead>
                      <TableHead>Remarks</TableHead>
                      <TableHead>Updated at</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">
                        {row.original.statusUpdatedBy}
                      </TableCell>
                      <TableCell>{row.original.statusUpdateRemarks}</TableCell>
                      <TableCell>
                        {row.original.statusUpdatedAt?.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </PopoverContent>
            </Popover>
          ) : null}
        </div>
      ),
    },
    {
      accessorKey: "user",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Agent" />
      ),
      cell: ({ row }) => <div>{row.original.user.name}</div>,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-right font-medium">
            {row.original.createdAt.toLocaleDateString()}
          </div>
        );
      },
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Updated At" />
      ),
      cell: ({ row }) => {
        return (
          <div className="text-right font-medium">
            {row.original.updatedAt.toLocaleDateString()}
          </div>
        );
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        return (
          <>
            {row.original.propertyStatus !== "SOLD" && (
              <DataTableActions
                id={row.original.id}
                agentId={row.original.user.id}
                propertyCategoryId={row.original.propertyCategoryId ?? ""}
              />
            )}
          </>
        );
      },
    },
  ];

interface PropertiesDataTableProps {
  data: PropertyWithUserDetails[];
  currentStatus: string;
}

const PropertiesDataTable = ({ data }: PropertiesDataTableProps) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns: PropertiesDataTableColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div>
      <div className="flex items-center py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Filter className="h-4 w-4" />
          <input
            placeholder="Filter property title..."
            value={
              (table.getColumn("propertyTitle")?.getFilterValue() as string) ||
              ""
            }
            onChange={(event) =>
              table
                .getColumn("propertyTitle")
                ?.setFilterValue(event.target.value)
            }
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={PropertiesDataTableColumns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PropertiesDataTable;
