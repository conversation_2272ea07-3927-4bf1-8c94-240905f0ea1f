import { Suspense } from "react";
import { api, HydrateClient } from "~/trpc/server";
import PropertiesDataTable from "./properties-data-table";
import PostPropertyFormSheet from "../partners/post-property/post-property-form-sheet";
import type { PropertyStatusEnum } from "@repo/database";
import PropertyStatusTabs from "./property-status-tabs";
import PropertyCardView from "./property-card-view";
import CardsSkeleton from "~/app/components/shared/card-skeleton";
import TableSkeleton from "~/app/components/shared/table-skeleton";
import PaginationButtons from "../partners/pagination-buttons";

type SearchParams = Promise<{
  status?: string;
  view?: "table" | "cards";
  page?: number;
  take?: number;
}>;

const PropertiesPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;

  const status = (searchParams.status ?? "ACTIVE") as PropertyStatusEnum;
  const view = searchParams.view ?? "table";
  const currentPage = Number(searchParams.page ?? 1);

  await api.property.getStatus.prefetch();

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Properties
      </h1>

      <PropertyStatusTabs activeStatus={status} view={view} />

      <Suspense
        fallback={view === "cards" ? <CardsSkeleton /> : <TableSkeleton />}
      >
        <PropertyDataView
          status={status}
          view={view}
          currentPage={currentPage}
        />
      </Suspense>

      <PostPropertyFormSheet />
    </HydrateClient>
  );
};

export default PropertiesPage;

const PropertyDataView = async ({
  status,
  view,
  currentPage,
}: {
  status: PropertyStatusEnum;
  view: "table" | "cards";
  currentPage?: number;
}) => {
  const properties = await api.property.getAllProperties({
    propertyStatus: status,
    page: view === "cards" ? currentPage : undefined,
    take: view === "cards" ? 9 : undefined,
  });

  return (
    <>
      {view === "cards" && (
        <div className="mb-4 flex justify-end">
          <PaginationButtons
            currentPage={currentPage ?? 1}
            totalPages={properties.totalPages}
            status={status}
            view={view}
          />
        </div>
      )}

      {view === "cards" ? (
        <PropertyCardView properties={properties.properties} />
      ) : (
        <PropertiesDataTable
          data={properties.properties}
          currentStatus={status}
        />
      )}
    </>
  );
};
