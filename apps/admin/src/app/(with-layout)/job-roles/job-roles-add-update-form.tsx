"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import addUpdateJobRoleSchema from "~/server/api/validations/add-update-job-role.validation";
import { api } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { skipToken } from "@tanstack/react-query";
import { useSheet } from "~/app/hooks/use-sheet";
import { JobRoleParamName } from "~/app/helpers/constants";

const JobRolesAddUpdateForm = () => {
  const { paramValue: jobRoleId, closeSheet } = useSheet(JobRoleParamName);

  const {
    data: jobRoleDetail,
    isLoading,
    error,
  } = api.jobRole.getJobRoleById.useQuery(
    jobRoleId ? { id: jobRoleId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  );

  const { mutate: updateJobRole, isPending: isPendingUpdateMutation } =
    api.jobRole.updateJobRole.useMutation();
  const { mutate: addJobRole, isPending: isPendingAddMutation } =
    api.jobRole.addJobRole.useMutation();
  const trpcUtils = api.useUtils();

  const form = useForm<z.infer<typeof addUpdateJobRoleSchema>>({
    resolver: zodResolver(addUpdateJobRoleSchema),
    defaultValues: jobRoleDetail
      ? {
          name: jobRoleDetail.name,
        }
      : {
          name: "",
        },
  });

  const onSubmit = (values: z.infer<typeof addUpdateJobRoleSchema>) => {
    if (jobRoleId) {
      updateJobRole(
        { ...values, id: jobRoleId },
        {
          onSuccess: (opts) => {
            if (opts.warning) {
              toast.error(opts.message);
              return;
            }
            toast.success(opts.message);
            void trpcUtils.jobRole.getJobRoleById.invalidate({ id: jobRoleId });
            void trpcUtils.jobRole.getAllJobRoles.invalidate();
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addJobRole(values, {
        onSuccess: (opts) => {
          if (opts.warning) {
            toast.error(opts.message);
            return;
          }
          toast.success(opts.message);
          void trpcUtils.jobRole.getAllJobRoles.invalidate();
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && jobRoleId) {
    return <div className="p-4 text-center">Loading Job Role details...</div>;
  }

  if (error && jobRoleId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading Job Role: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Job Role title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {jobRoleId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          jobRoleId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!jobRoleId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !jobRoleId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default JobRolesAddUpdateForm;
