"use client";

import React from "react";
import { Button } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { JobRoleParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import JobRolesAddUpdateForm from "./job-roles-add-update-form";
import { JobRolesStatusDropdown } from "./job-roles-status-dropdown";

const JobRolesFormSheet = ({ showDeleted }: { showDeleted?: string }) => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(JobRoleParamName);

  return (
    <div>
      <div className="flex flex-row flex-wrap items-center">
        <Button onClick={() => openSheet()}>
          <Plus className="mr-2 size-4" /> Add Job Role
        </Button>

        <JobRolesStatusDropdown showDeleted={showDeleted} />
      </div>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Job Role" : "Add Job Role"}
      >
        {isOpen && <JobRolesAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default JobRolesFormSheet;
