"use client";

import { CheckCircle2, Trash } from "lucide-react";
import { Button } from "@repo/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import { useRouter, useSearchParams } from "next/navigation";

interface JobRolesStatusDropdownProps {
  showDeleted?: string;
}

export const JobRolesStatusDropdown = ({
  showDeleted,
}: JobRolesStatusDropdownProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const statuses = [
    {
      value: "false",
      label: "Active Job Roles",
      icon: <CheckCircle2 className="mr-2 h-4 w-4" />,
    },
    {
      value: "true",
      label: "Deleted Job Roles",
      icon: <Trash className="mr-2 h-4 w-4" />,
    },
  ];

  const handleStatusChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("showDeleted", value);
    router.replace(`/job-roles?${params.toString()}`);
  };

  return (
    <div className="ml-4">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            {showDeleted === "true" ? (
              <Trash className="h-4 w-4" />
            ) : (
              <CheckCircle2 className="h-4 w-4" />
            )}
            {showDeleted === "true" ? "Deleted Job Roles" : "Active Job Roles"}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {statuses.map((status) => (
            <DropdownMenuItem
              key={status.value}
              onClick={() => handleStatusChange(status.value)}
              className="flex cursor-pointer items-center gap-2"
            >
              {status.icon}
              {status.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
