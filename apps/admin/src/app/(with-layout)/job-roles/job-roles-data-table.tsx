"use client";

import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import {
  ArrowUpDown,
  ChevronDown,
  Copy,
  Edit,
  Filter,
  MoreHorizontal,
  Trash,
} from "lucide-react";

import { Button } from "@repo/ui/components/ui/button";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import type { JobRole } from "@repo/database";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import { useRouter, useSearchParams } from "next/navigation";
import { useSheet } from "~/app/hooks/use-sheet";
import { JobRoleParamName } from "~/app/helpers/constants";
import TableHeading from "~/app/components/shared/table-heading";
import WrappedAlert from "~/app/components/shared/wrapped-alert";
import { cn } from "@repo/ui/lib/utils";

export const jobRolesDataTableColumns: ColumnDef<JobRole>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: () => <button className="flex justify-start">Job Role Id</button>,
    cell: ({ row }) => <div>{row.getValue("id")}</div>,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <div
          className="flex cursor-pointer items-center gap-1"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="font-medium">{row.original.name}</div>;
    },
  },

  {
    accessorKey: "createdAt",
    header: () => <div className="text-right">CreatedAt</div>,
    cell: ({ row }) => {
      return (
        <div className="text-right font-medium">
          {row.original.createdAt.toLocaleDateString()}
        </div>
      );
    },
  },
  {
    accessorKey: "updatedAt",
    header: () => <div className="text-right">UpdatedAt</div>,
    cell: ({ row }) => {
      return (
        <div className="text-right font-medium">
          {row.original.updatedAt.toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => <JobActionCell row={row} />,
  },
];

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export const JobRolesDataTable = <TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const searchParams = useSearchParams();
  const showDeleted = searchParams.get("showDeleted");

  return (
    <div className="w-full">
      <div className="flex items-center py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Filter className="h-4 w-4" />
          <input
            placeholder="Filter name..."
            value={(table.getColumn("name")?.getFilterValue() as string) || ""}
            onChange={(event) =>
              table.getColumn("name")?.setFilterValue(event.target.value)
            }
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {showDeleted === "true"
                    ? "No deleted job roles found."
                    : "No active job roles found."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default JobRolesDataTable;

const JobActionCell: React.FC<{ row: { original: JobRole } }> = ({ row }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const showDeleted = searchParams.get("showDeleted");

  const { openSheet } = useSheet(JobRoleParamName);
  const { mutate: deleteJobRole, isPending } =
    api.jobRole.removeJobRole.useMutation();

  const deleteJobRoleFunc = (id: string) => {
    deleteJobRole(
      { id: id },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          router.refresh();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  // this check is to dont show any action when user is on deleted job roles
  if (showDeleted === "true") return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <TableHeading rightIcon={<MoreHorizontal />}>
          <span className="sr-only">Open menu</span>
        </TableHeading>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={async () => {
            await navigator.clipboard.writeText(row.original.id);
            toast.success("Job ID copied to your clipboard");
          }}
          className="cursor-pointer"
        >
          <Copy className="mr-2 size-4" />
          Copy job Id
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="flex items-center gap-2"
          onClick={() => openSheet(row.original.id)}
        >
          <Edit className="size-4" />
          Update Job Role
        </DropdownMenuItem>
        <DropdownMenuItem>
          <WrappedAlert
            trigger={
              <button
                className="flex items-center"
                onClick={(e) => e.stopPropagation()}
              >
                <Trash className="mr-2 size-4" />
                {isPending ? "Deleting..." : "Delete Job Role"}
              </button>
            }
            title="Confirm deletion"
            description="Are you sure you want to delete this job role? This action cannot be undone."
            onConfirm={() => deleteJobRoleFunc(row.original.id)}
            confirmText="Confirm Deletion"
            cancelText="Cancel"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
