import { api, HydrateClient } from "~/trpc/server";
import JobRolesFormSheet from "./job-roles-form-sheet";
import JobRolesDataTable, {
  jobRolesDataTableColumns,
} from "./job-roles-data-table";

type SearchParams = Promise<{ job_role_id?: string; showDeleted?: string }>;

const JobRolesPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const jobRoles = await api.jobRole.getAllJobRoles({
    showDeleted: searchParams.showDeleted === "true",
  });

  if (searchParams.job_role_id) {
    await api.jobRole.getJobRoleById.prefetch({ id: searchParams.job_role_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Job Roles
      </h1>

      <JobRolesFormSheet showDeleted={searchParams.showDeleted} />
      <JobRolesDataTable data={jobRoles} columns={jobRolesDataTableColumns} />
    </HydrateClient>
  );
};

export default JobRolesPage;
