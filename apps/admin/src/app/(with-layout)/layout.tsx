import "@repo/ui/globals.css";

import localFont from "next/font/local";
import type { Metadata } from "next";

import { TRPCReactProvider } from "~/trpc/react";
import Providers from "../providers";

import { Toaster } from "@repo/ui/components/ui/sonner";
import { auth } from "~/server/auth";
import PropertyDialogProvider from "./property-detail-dialog-provider";
import {
  SidebarProvider,
  SidebarTrigger,
} from "@repo/ui/components/ui/sidebar";
import { AppSidebar } from "../components/shared/app-sidebar";
import AgentDetailDialogprovider from "./agent-detail-dialog-provider";

export const metadata: Metadata = {
  title: {
    default: "DeerConnect-Admin",
    template: "%s - DeerConnect-Admin",
  },
  description: "Generated by create-t3-app",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const airbnb_w_md = localFont({
  src: "../fonts/AirbnbCereal_W_Md.otf",
  variable: "--font-airbnb-w-md",
});

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await auth();

  return (
    <html lang="en" className={airbnb_w_md.variable}>
      <body>
        <TRPCReactProvider>
          <Providers>
            <SidebarProvider>
              <AppSidebar session={session} />
              <main className="w-full overflow-x-auto px-4">
                <div className="mb-4 mt-2 flex items-center gap-1 px-2 text-xs">
                  <SidebarTrigger />
                  <span className="hidden font-medium md:block">
                    <span className="text-xs">ctrl</span>+
                    <span className="text-xs">b</span>
                  </span>
                </div>
                {children}
              </main>
            </SidebarProvider>
            <Toaster richColors />
            <PropertyDialogProvider />
            <AgentDetailDialogprovider />
          </Providers>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
