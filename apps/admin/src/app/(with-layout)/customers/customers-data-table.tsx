"use client";

import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import React, { useState } from "react";
import { Button } from "@repo/ui/components/ui/button";
import { ChevronLeft, ChevronRight, Columns2, Search } from "lucide-react";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { format } from "date-fns";

import SortableColumnHeader from "~/app/components/shared/sortable-column-header";
import type { TCustomer } from "~/app/types";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@repo/ui/lib/utils";
import { usePathname, useSearchParams } from "next/navigation";

interface DataTableProps<TData> {
  data: TData[];
}

export function CustomerDataTable({ data }: DataTableProps<TCustomer>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState("");
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const handleViewAgent = (customerId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("viewAgentId", customerId);
    history.pushState(null, "", `${pathname}?${params.toString()}`);
  };

  const columns: ColumnDef<TCustomer>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "id",
      header: () => <button className="flex justify-start">Customer Id</button>,
    },
    {
      accessorKey: "profileImagePublicUrl",
      header: "Profile Image",
      cell: ({ row }) => (
        <div className="relative aspect-square w-16">
          <Image
            src={
              row.getValue("profileImagePublicUrl") ??
              "/images/placeholder-user-image.jpg"
            }
            alt={row.getValue("id")}
            fill
            className="relative object-cover"
          />
        </div>
      ),
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Name" />
      ),
    },

    {
      accessorKey: "email",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => (
        <div className="lowercase">{row.getValue("email")}</div>
      ),
    },

    {
      accessorKey: "phoneNumber",
      header: "Phone",
    },
    {
      accessorKey: "locationAddress",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Location Address" />
      ),
      cell: ({ row }) => <div>{row.original.locationAddress ?? "N/A"}</div>,
    },
    {
      accessorKey: "connections",
      header: "Connected Agent",
      cell: ({ row }) => {
        {
          /* customer may only have one connection at a time , so we can just access the first element as we are fetching just the active connection */
        }
        return row.original.connections.length > 0 ? (
          <div
            className="hover:cursor-pointer hover:underline"
            onClick={() =>
              handleViewAgent(row.original.connections[0]?.agent.id ?? "")
            }
          >
            {row.original.connections[0]?.agent.name}
          </div>
        ) : (
          <div>No active connection</div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created On" />
      ),
      cell: ({ row }) => (
        <div className="capitalize">
          {format(new Date(row.getValue("createdAt")), "PPp")}
        </div>
      ),
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Updated On" />
      ),
      cell: ({ row }) => (
        <div className="capitalize">
          {format(new Date(row.getValue("updatedAt")), "PPp")}
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      rowSelection,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    onRowSelectionChange: setRowSelection,
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <>
      <div className="mt-4 flex flex-wrap items-center justify-between gap-4 py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Search className="h-4 w-4" />
          <input
            placeholder="Search by name, email, phone..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="ml-auto flex items-center gap-2"
            >
              <Columns2 className="h-4 w-4" />
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-center">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <div className="mx-4 my-3 flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="mr-2 size-4" />
              Previous
            </Button>
            <Button
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
              <ChevronRight className="ml-2 size-4" />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
