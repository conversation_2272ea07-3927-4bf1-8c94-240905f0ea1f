import React from "react";
import { api, HydrateClient } from "~/trpc/server";
import { CustomerDataTable } from "./customers-data-table";

const CustomersPage = async () => {
  const customer = await api.customer.getCustomers();
  console.log(customer);
  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Customers
      </h1>

      <CustomerDataTable data={customer} />
    </HydrateClient>
  );
};

export default CustomersPage;
