import { api, HydrateClient } from "~/trpc/server";
import CitiesDataTable, { CitiesDataTableColumns } from "./citites-data-table";
import CityFormSheet from "./city-form-sheet";

type SearchParams = Promise<{ city_id?: string }>;

const Cities = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const cities = await api.city.getAllCities();

  if (searchParams.city_id) {
    await api.city.getCityById.prefetch({ cityId: searchParams.city_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">Cities</h1>

      <CityFormSheet />
      <CitiesDataTable data={cities} columns={CitiesDataTableColumns} />
    </HydrateClient>
  );
};

export default Cities;
