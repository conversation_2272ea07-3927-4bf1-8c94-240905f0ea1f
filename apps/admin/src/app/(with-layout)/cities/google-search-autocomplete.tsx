"use client";

import * as React from "react";
import { useEffect, useState, useCallback, useRef } from "react";
import { Input } from "@repo/ui/components/ui/input";
import { LocateFixedIcon, Search } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { useGoogleMaps } from "./google-maps-provider";

type SelectedLocation = {
  lat: string;
  lng: string;
  place_id: string;
  address_components: any;
  address: string;
};

const SearchBarWithGoogleAutocomplete = ({
  className,
  placeholder,
  onChange,
  value,
  googleApiKey,
  disable,
  setUserLocation,
  setLocationSearch,
  setSearchSelectedLocation,
}: {
  className?: string;
  placeholder?: string;
  onChange: (data: SelectedLocation) => void;
  value: string | undefined;
  googleApiKey: string;
  disable: boolean;
  setLocationSearch: (v: string | undefined) => void;
  setUserLocation?: (v: { lat: number; lng: number }) => void;
  setSearchSelectedLocation: (v: SelectedLocation | undefined) => void;
}) => {
  const [query, setQuery] = useState(value || "");
  const [lastQuery, setLastQuery] = useState(value || "");
  const [suggestions, setSuggestions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [hasSelectedLocation, setHasSelectedLocation] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1);
  const { isLoaded } = useGoogleMaps();
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLUListElement>(null);

  const fetchPlacePredictions = useCallback(() => {
    if (!isLoaded || !query) {
      setSuggestions([]);
      return;
    }

    const service = new google.maps.places.AutocompleteService();
    service.getPlacePredictions(
      {
        input: query,
        componentRestrictions: { country: ["in"] },
      },
      (predictions, status) => {
        if (
          status === google.maps.places.PlacesServiceStatus.OK &&
          predictions
        ) {
          setSuggestions(predictions);
          setShowSuggestions(true);
          setActiveIndex(-1);
        } else {
          setSuggestions([]);
          setShowSuggestions(false);
          setActiveIndex(-1);
        }
      },
    );
  }, [query, isLoaded]);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (query && query !== lastQuery) {
        // Only fetch predictions if no location has been selected
        if (!hasSelectedLocation) {
          setLastQuery(query);
          fetchPlacePredictions();
        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [query, fetchPlacePredictions, hasSelectedLocation]);

  const selectPlace = (
    suggestion: google.maps.places.AutocompletePrediction,
  ) => {
    if (!isLoaded) return;

    const placesService = new google.maps.places.PlacesService(
      document.createElement("div"),
    );

    placesService.getDetails(
      { placeId: suggestion.place_id },
      (place, status) => {
        if (
          status === google.maps.places.PlacesServiceStatus.OK &&
          place?.geometry?.location
        ) {
          const location = place.geometry.location;
          const selectedLocation: SelectedLocation = {
            lat: location.lat().toString(),
            lng: location.lng().toString(),
            place_id: suggestion.place_id,
            address_components: place.address_components,
            address: place.formatted_address || "",
          };

          onChange(selectedLocation);
          setQuery(place.formatted_address || "");

          setSuggestions([]);
          setShowSuggestions(false);

          setHasSelectedLocation(true);
          inputRef.current?.focus();
        }
      },
    );
  };

  const getUserLocation = () => {
    if (!setUserLocation) return;

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;

        setUserLocation({ lat: latitude, lng: longitude });
        setQuery("");
        setLocationSearch(undefined);
        setSearchSelectedLocation(undefined);

        setHasSelectedLocation(false);
      },
      (error) => {
        console.error("Error get user location: ", error);
      },
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);

    if (hasSelectedLocation) {
      setHasSelectedLocation(false);
    }
  };

  const handleInputFocus = () => {
    if (!hasSelectedLocation && suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  // Keyboard navigation handler
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setActiveIndex((prevIndex) =>
          prevIndex < suggestions.length - 1 ? prevIndex + 1 : prevIndex,
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setActiveIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : -1));
        break;
      case "Enter":
        if (activeIndex >= 0 && activeIndex < suggestions.length) {
          e.preventDefault();
          if (suggestions[activeIndex]) {
            selectPlace(suggestions[activeIndex]);
          }
        }
        break;
      case "Escape":
        e.preventDefault();
        setShowSuggestions(false);
        setActiveIndex(-1);
        break;
    }
  };

  // Effect to scroll active suggestion into view
  useEffect(() => {
    if (activeIndex >= 0 && suggestionsRef.current) {
      const activeElement = suggestionsRef.current.children[
        activeIndex
      ] as HTMLLIElement;
      activeElement?.scrollIntoView({ block: "nearest" });
    }
  }, [activeIndex]);

  if (!isLoaded) {
    return (
      <div className="mb-3 h-14 animate-pulse rounded-md border bg-gray-200 p-3 pl-10 pr-10 text-sm md:pl-10 md:pr-10 xl:p-3.5 xl:pl-10 xl:pr-10"></div>
    );
  }

  return (
    <div className="relative mb-3">
      <Input
        ref={inputRef}
        placeholder={placeholder}
        className={cn(
          "p-3 text-sm xs:pl-9 xs:pr-9 sm:pl-10 sm:pr-10 md:pl-10 md:pr-10 xl:p-3.5 xl:pl-10 xl:pr-10",
          className,
        )}
        value={query}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onKeyDown={handleKeyDown}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
        aria-label="Enter an address for autocomplete suggestions"
        disabled={disable}
      />
      <Search className="absolute left-3 top-1/2 size-5 -translate-y-1/2" />
      <LocateFixedIcon
        className="absolute right-3 top-1/2 size-5 -translate-y-1/2 cursor-pointer"
        onClick={getUserLocation}
      />

      {showSuggestions && suggestions.length > 0 && (
        <ul
          ref={suggestionsRef}
          className="absolute z-40 mt-1 max-h-60 w-full overflow-y-auto rounded-md border border-gray-300 bg-white shadow-lg"
        >
          {suggestions.map((suggestion, index) => (
            <li
              key={suggestion.place_id}
              className={cn(
                "cursor-pointer px-4 py-2 hover:bg-gray-100",
                index === activeIndex ? "bg-gray-200" : "",
              )}
              onMouseDown={() => selectPlace(suggestion)}
              onMouseEnter={() => setActiveIndex(index)}
            >
              {suggestion.structured_formatting.main_text},{" "}
              {suggestion.structured_formatting.secondary_text}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SearchBarWithGoogleAutocomplete;
