"use client";

import React, { useCallback, useState, useRef } from "react";
import {
  GoogleMap,
  MarkerF,
  <PERSON><PERSON><PERSON>,
  Rectangle,
  useJsApiLoader,
} from "@react-google-maps/api";
import { env } from "~/env";
import { Button } from "@repo/ui/components/ui/button";
import { useGoogleMaps } from "./google-maps-provider";

type UserCurrentPosition = {
  lat: number;
  lng: number;
};

const calculateDistance = (
  point1: google.maps.LatLngLiteral | undefined,
  point2: google.maps.LatLngLiteral | undefined,
) => {
  if (!point1 || !point2) return;

  const lat1 = point1.lat;
  const lng1 = point1.lng;
  const lat2 = point2.lat;
  const lng2 = point2.lng;

  return Math.sqrt(Math.pow(lat2 - lat1, 2) + Math.pow(lng2 - lng1, 2));
};

type MarkLocationsMapProps = {
  zoomLevel: number;
  searchLat: number | undefined;
  searchLng: number | undefined;
  drawingPoints: google.maps.LatLngLiteral[];
  userLocation: UserCurrentPosition | undefined;
  setUserLocation: (v: UserCurrentPosition | undefined) => void;
  setDrawingPoints: (
    v:
      | google.maps.LatLngLiteral[]
      | ((prev: google.maps.LatLngLiteral[]) => google.maps.LatLngLiteral[]),
  ) => void;
  setDisable: (v: boolean) => void;
};

const MAX_MAP_MARKER_CLICKS = 4;

const MapComponent = ({
  zoomLevel,
  searchLat,
  searchLng,
  userLocation,
  drawingPoints,
  setDrawingPoints,
  setDisable,
}: MarkLocationsMapProps) => {
  const mapRef = useRef<google.maps.Map | null>(null);
  const { isLoaded } = useGoogleMaps();
  const [isDrawing, setIsDrawing] = useState(false);
  const [isClosed, setIsClosed] = useState(false);
  const [showLocationMarker, setShowLocationMarker] = useState(true);
  const [activeMarkerIndex, setActiveMarkerIndex] = useState<number | null>(
    null,
  );
  const [key, setKey] = useState(0);

  const lastValidCenter = useRef<google.maps.LatLngLiteral | null>(null);
  const lastValidZoom = useRef<number>(14);
  const originalPositions = useRef<google.maps.LatLngLiteral[]>([]);

  const getInitialCenter = useCallback(() => {
    if (lastValidCenter.current) {
      return lastValidCenter.current;
    }
    if (searchLat && searchLng) {
      return { lat: searchLat, lng: searchLng };
    }
    if (userLocation) {
      return { lat: userLocation.lat, lng: userLocation.lng };
    }
    return { lat: 28.7041, lng: 77.1025 }; // default center
  }, [searchLat, searchLng, userLocation]);

  const onLoad = useCallback(
    function callback(gMap: google.maps.Map) {
      mapRef.current = gMap;

      if (drawingPoints.length >= 2) {
        const bounds = new window.google.maps.LatLngBounds();
        drawingPoints.forEach((marker) => {
          bounds.extend(marker);
        });
        gMap.fitBounds(bounds);
      } else {
        const center = getInitialCenter();
        gMap.setCenter(center);
        gMap.setZoom(lastValidZoom.current);
      }

      const initialCenter = gMap.getCenter();
      if (initialCenter) {
        lastValidCenter.current = {
          lat: initialCenter.lat(),
          lng: initialCenter.lng(),
        };
      }
      lastValidZoom.current = gMap.getZoom() ?? 5;

      mapRef.current.setClickableIcons(false);
      gMap.addListener("center_changed", () => {
        const newCenter = gMap.getCenter();
        if (newCenter) {
          lastValidCenter.current = {
            lat: newCenter.lat(),
            lng: newCenter.lng(),
          };
        }
      });

      gMap.addListener("zoom_changed", () => {
        lastValidZoom.current = gMap.getZoom() ?? 5;
      });
    },
    [getInitialCenter, drawingPoints],
  );

  const onUnmount = useCallback(function callback() {
    mapRef.current = null;
  }, []);

  const closePolygon = useCallback(() => {
    if (drawingPoints.length >= 3) {
      setIsClosed(true);
      setIsDrawing(false);
      originalPositions.current = [...drawingPoints];
    }
  }, [drawingPoints]);

  const handleMapClick = useCallback(
    (e: google.maps.MapMouseEvent) => {
      if (!isDrawing || !e.latLng || isClosed) return;

      if (drawingPoints.length >= MAX_MAP_MARKER_CLICKS) return;

      const newPoint = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };

      setDrawingPoints((prev) => {
        const updated = [...prev, newPoint];

        // Automatically close and stop drawing when 4 points are placed
        if (updated.length === MAX_MAP_MARKER_CLICKS) {
          setIsClosed(true);
          setIsDrawing(false);
          originalPositions.current = [...updated];
        }

        return updated;
      });
    },
    [isDrawing, isClosed, drawingPoints, setDrawingPoints],
  );

  const getRectangleBounds = useCallback(() => {
    if (drawingPoints.length !== 4) return null;

    const lngs = drawingPoints.map((point) => point.lng);
    const lats = drawingPoints.map((point) => point.lat);

    console.log({
      north: Math.max(...lats),
      south: Math.min(...lats),
      east: Math.max(...lngs),
      west: Math.min(...lngs),
    });

    return {
      north: Math.max(...lats),
      south: Math.min(...lats),
      east: Math.max(...lngs),
      west: Math.min(...lngs),
    };
  }, [drawingPoints]);

  const handleMarkerDragStart = useCallback((index: number) => {
    setActiveMarkerIndex(index);
  }, []);

  const handleMarkerDrag = useCallback(
    (index: number, e: google.maps.MapMouseEvent) => {
      if (!e.latLng || isClosed) return;

      const newLat = e.latLng.lat();
      const newLng = e.latLng.lng();

      setDrawingPoints((prev) => {
        const updated = [...prev];
        updated[index] = { lat: newLat, lng: newLng };
        return updated;
      });
    },
    [setDrawingPoints, isClosed],
  );

  const handleMarkerDragEnd = useCallback(
    (index: number, e: google.maps.MapMouseEvent) => {
      if (!e.latLng) return;

      const newLat = e.latLng.lat();
      const newLng = e.latLng.lng();

      setDrawingPoints((prev) => {
        const updated = [...prev];
        updated[index] = { lat: newLat, lng: newLng };
        originalPositions.current = [...updated];
        return updated;
      });
      setActiveMarkerIndex(null);
      setKey((prev) => prev + 1);
    },
    [setDrawingPoints],
  );

  const getPolylinePath = useCallback(() => {
    if (drawingPoints.length < 2) return drawingPoints;
    return isClosed || drawingPoints.length >= 3
      ? [...drawingPoints, drawingPoints[0]]
      : drawingPoints;
  }, [drawingPoints, isClosed]);

  const handleStartDrawing = useCallback(() => {
    setDisable(true);
    setIsDrawing(true);
    setDrawingPoints([]);
    setIsClosed(false);
    originalPositions.current = [];
    setShowLocationMarker(false);
    setKey((prev) => prev + 1);
  }, [setDrawingPoints]);

  const handleFinishDrawing = useCallback(() => {
    closePolygon();
    setDisable(false);
  }, [closePolygon]);

  const handleReset = useCallback(() => {
    setDrawingPoints([]);
    setIsDrawing(false);
    setIsClosed(false);
    originalPositions.current = [];
    setShowLocationMarker(true);
    setActiveMarkerIndex(null);
    setKey((prev) => prev + 1);
  }, [setDrawingPoints]);

  const handleVisibilityChange = useCallback(() => {
    if (document.hidden && originalPositions.current.length > 0) {
      setDrawingPoints([...originalPositions.current]);
    }
  }, [setDrawingPoints]);

  React.useEffect(() => {
    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [handleVisibilityChange]);

  React.useEffect(() => {
    if (!mapRef.current || !searchLat || !searchLng) return;

    mapRef.current.moveCamera({
      center: {
        lat: searchLat,
        lng: searchLng,
      },
    });
  }, [searchLat, searchLng]);

  React.useEffect(() => {
    if (!mapRef.current || !userLocation) return;

    mapRef.current.moveCamera({
      center: {
        lat: userLocation.lat,
        lng: userLocation.lng,
      },
    });
  }, [userLocation]);

  return (
    isLoaded && (
      <>
        <div className="relative">
          <GoogleMap
            key={key}
            center={getInitialCenter()}
            zoom={lastValidZoom.current ?? zoomLevel}
            onLoad={onLoad}
            onUnmount={onUnmount}
            onClick={handleMapClick}
            mapContainerClassName="flex h-full min-h-[50vh] rounded-2xl"
            options={{
              disableDefaultUI: false,
              zoomControl: true,
              scrollwheel: true,
              fullscreenControl: true,
            }}
          >
            {showLocationMarker &&
              (searchLat && searchLng ? (
                <MarkerF
                  position={{
                    lat: searchLat,
                    lng: searchLng,
                  }}
                />
              ) : userLocation ? (
                <MarkerF
                  position={{
                    lat: userLocation.lat,
                    lng: userLocation.lng,
                  }}
                />
              ) : null)}

            {drawingPoints.map((point, index) => (
              <MarkerF
                key={`drawing-${index}-${key}`}
                position={point}
                label={`${index + 1}`}
                draggable={!isClosed}
                onDragStart={() => handleMarkerDragStart(index)}
                onDrag={(e) => handleMarkerDrag(index, e)}
                onDragEnd={(e) => handleMarkerDragEnd(index, e)}
                icon={{
                  url:
                    index === 0
                      ? "http://maps.google.com/mapfiles/ms/icons/green-dot.png"
                      : "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
                  scaledSize: new google.maps.Size(40, 40),
                  anchor: new google.maps.Point(19, 40),
                }}
                options={{
                  zIndex: activeMarkerIndex === index ? 1000 : 100,
                }}
              />
            ))}

            {/* Add Rectangle when 4 points are drawn */}
            {drawingPoints.length === 4 && (
              <Rectangle
                // @ts-ignore
                bounds={getRectangleBounds()}
                options={{
                  strokeColor: "#31a31d",
                  strokeOpacity: 0.8,
                  strokeWeight: 2,
                  fillColor: "#31a31d",
                  fillOpacity: 0.35,
                }}
              />
            )}

            {drawingPoints.length > 0 && (
              <Polyline
                // @ts-ignore
                path={getPolylinePath()}
                options={{
                  strokeColor: "#FF0000",
                  strokeOpacity: 1.0,
                  strokeWeight: 2,
                  clickable: false,
                  editable: false,
                  geodesic: true,
                  zIndex: 1,
                }}
              />
            )}
          </GoogleMap>

          <div className="absolute bottom-4 left-4 space-x-2">
            {!isDrawing && !isClosed && (
              <Button onClick={handleStartDrawing}>Start Drawing</Button>
            )}
            {isDrawing && drawingPoints.length >= 3 && (
              <Button onClick={handleFinishDrawing}>Complete Shape</Button>
            )}
            {(drawingPoints.length > 0 || isClosed) && (
              <Button onClick={handleReset} variant="destructive">
                Reset
              </Button>
            )}
          </div>
        </div>
      </>
    )
  );
};

export default MapComponent;
