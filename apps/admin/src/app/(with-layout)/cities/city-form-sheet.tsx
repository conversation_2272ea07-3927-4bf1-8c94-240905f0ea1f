"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { CityParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import AddEditCityForm from "./add-edit-city-form";

const CityFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } = useSheet(CityParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add City
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit City" : "Add City"}
      >
        {isOpen && <AddEditCityForm />}
      </GenericSheet>
    </div>
  );
};

export default CityFormSheet;
