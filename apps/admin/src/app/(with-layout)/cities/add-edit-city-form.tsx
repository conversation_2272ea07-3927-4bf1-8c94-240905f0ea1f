"use client";

import React, { useEffect, useState } from "react";
import MapComponent from "./map";
import { env } from "~/env";
import SearchBarWithGoogleAutocomplete from "./google-search-autocomplete";
import { GoogleMapsProvider } from "./google-maps-provider";

type SelectedLocation = {
  lat: string;
  lng: string;
  place_id: string;
  address_components: any;
  address: string;
};

type UserCurrentPosition = {
  lat: number;
  lng: number;
};

import { zodResolver } from "@hookform/resolvers/zod";
import type { FieldErrors } from "react-hook-form";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { AddUpdateCitiesSchema } from "~/server/api/validations/add-update-cities.validation";
import { toast } from "@repo/ui/components/ui/sonner";
import { api } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { skipToken } from "@tanstack/react-query";
import { CityParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { Button } from "@repo/ui/components/ui/button";

const AddEditCityForm = () => {
  const { paramValue: cityId, closeSheet } = useSheet(CityParamName);

  const { data: city, isLoading } = api.city.getCityById.useQuery(
    cityId ? { cityId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  );

  const initialMarkers =
    city?.cityMarkersLatLng &&
    Array.isArray(city.cityMarkersLatLng) &&
    city.cityMarkersLatLng.length > 0
      ? (city.cityMarkersLatLng as { lat: number; lng: number }[])
      : [];

  const [drawingPoints, setDrawingPoints] =
    useState<google.maps.LatLngLiteral[]>(initialMarkers);
  const [locationSearch, setLocationSearch] = useState<string | undefined>(
    undefined,
  );
  const [searchSelectedLocation, setSearchSelectedLocation] = useState<
    SelectedLocation | undefined
  >(undefined);
  const [userLocation, setUserLocation] = useState<
    UserCurrentPosition | undefined
  >(undefined);
  const [disableSearchBar, setDisableSearchBar] = useState<boolean>(false);

  const { mutate: addNewCity } = api.city.addNewCity.useMutation();
  const { mutate: updateCity } = api.city.updateCity.useMutation();
  const trpcUtils = api.useUtils();

  const getFormDefaults = () => {
    if (
      city &&
      city.cityMarkersLatLng &&
      Array.isArray(city.cityMarkersLatLng) &&
      city.cityMarkersLatLng.length > 0
    ) {
      const markers = city.cityMarkersLatLng as { lat: number; lng: number }[];
      const lngs = markers.map((point) => point.lng);
      const lats = markers.map((point) => point.lat);

      return {
        cityName: city.name,
        cityMarkersLatLng: markers,
        northMaxLat: Math.max(...lats),
        southMaxLat: Math.min(...lats),
        eastMaxLng: Math.max(...lngs),
        westMaxLng: Math.min(...lngs),
      };
    }

    return {
      cityName: "",
      cityMarkersLatLng: [],
      northMaxLat: 0,
      southMaxLat: 0,
      eastMaxLng: 0,
      westMaxLng: 0,
    };
  };

  const form = useForm<z.infer<typeof AddUpdateCitiesSchema>>({
    resolver: zodResolver(AddUpdateCitiesSchema),
    defaultValues: getFormDefaults(),
  });

  useEffect(() => {
    if (drawingPoints.length) {
      form.setValue("cityMarkersLatLng", drawingPoints);

      const lngs = drawingPoints.map((point) => point.lng);
      const lats = drawingPoints.map((point) => point.lat);

      if (lats.length > 0 && lngs.length > 0) {
        form.setValue("northMaxLat", Math.max(...lats));
        form.setValue("southMaxLat", Math.min(...lats));
        form.setValue("eastMaxLng", Math.max(...lngs));
        form.setValue("westMaxLng", Math.min(...lngs));
      }
    }
  }, [drawingPoints, form]);

  const errorHandler = (
    err: FieldErrors<z.infer<typeof AddUpdateCitiesSchema>>,
  ) => {
    if (err.cityMarkersLatLng) {
      toast.error("Please select markers on the map.");
    }
  };

  const onSubmit = (values: z.infer<typeof AddUpdateCitiesSchema>) => {
    if (cityId) {
      updateCity(
        { ...values, cityId: cityId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void trpcUtils.city.invalidate();
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addNewCity(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void trpcUtils.city.invalidate();
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && cityId) {
    return <div className="p-4 text-center">Loading city details...</div>;
  }

  return (
    <div className="px-5">
      <GoogleMapsProvider>
        <div className="">
          <div className="flex items-center justify-center gap-4 py-2">
            <div className="flex w-full items-center gap-4">
              <div className="w-full">
                <SearchBarWithGoogleAutocomplete
                  className="w-full rounded-md p-2 outline-none focus:outline-none"
                  placeholder="Search Location..."
                  onChange={(resp) => {
                    setSearchSelectedLocation(resp);
                  }}
                  value={locationSearch}
                  googleApiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}
                  disable={disableSearchBar}
                  setUserLocation={setUserLocation}
                  setLocationSearch={setLocationSearch}
                  setSearchSelectedLocation={setSearchSelectedLocation}
                />
              </div>
            </div>
          </div>

          <MapComponent
            searchLat={Number(searchSelectedLocation?.lat)}
            searchLng={Number(searchSelectedLocation?.lng)}
            userLocation={userLocation}
            drawingPoints={drawingPoints}
            setDrawingPoints={setDrawingPoints}
            setUserLocation={setUserLocation}
            zoomLevel={14}
            setDisable={setDisableSearchBar}
          />

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit, errorHandler)}
              className="mt-6 flex flex-col gap-4"
            >
              <FormField
                control={form.control}
                name="cityName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg font-medium">
                      City Name
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="eg: Mumbai" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" size="lg">
                {cityId ? "Update" : "Create"}
              </Button>
            </form>
          </Form>
        </div>
      </GoogleMapsProvider>
    </div>
  );
};

export default AddEditCityForm;
