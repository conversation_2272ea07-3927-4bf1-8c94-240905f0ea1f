import { api, HydrateClient } from "~/trpc/server";
import {
  amenitiesDataTableColumns,
  AmenitiesDataTable,
} from "./amenities-data-table";
import AmenitiesFormSheet from "./amenities-form-sheet";

type SearchParams = Promise<{ amenity_id?: string }>;

const AmenitiesPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const amenities = await api.amenities.getAllAmenities();

  if (searchParams.amenity_id) {
    await api.amenities.getAmenityById.prefetch({
      id: searchParams.amenity_id,
    });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Amenities
      </h1>

      <AmenitiesFormSheet />
      <AmenitiesDataTable
        data={amenities}
        columns={amenitiesDataTableColumns}
      />
    </HydrateClient>
  );
};

export default AmenitiesPage;
