"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";

import { Button } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import AddUpdateAmenitySchema from "~/server/api/validations/add-update-amenities.validation";
import { useEffect, useState } from "react";
import { api } from "~/trpc/react";
import Image from "next/image";
import Loading from "~/app/components/loading";
import { skipToken } from "@tanstack/react-query";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { AmenityParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";

const AmenitiesAddUpdateForm = () => {
  const { paramValue: amenityId, closeSheet } = useSheet(AmenityParamName);
  const [imageUrl, setImageUrl] = useState<string | undefined>(undefined);
  const [isUploadingImage, setIsUploadingImage] = useState<boolean>(false);

  const {
    data: amenityDetail,
    isLoading,
    error,
  } = api.amenities.getAmenityById.useQuery(
    amenityId ? { id: amenityId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  );

  const { mutate: updateAmenity, isPending: isPendingUpdateMutation } =
    api.amenities.updateAmenity.useMutation();
  const { mutate: addAmenity, isPending: isPendingAddMutation } =
    api.amenities.addAmenity.useMutation();
  const awsPresignedUrlMutation = api.aws.getPresignedUrl.useMutation();
  const awsGetPublicUrlMutation = api.aws.getPublicFileUrl.useMutation();
  const trpcUtils = api.useUtils();

  const form = useForm<z.infer<typeof AddUpdateAmenitySchema>>({
    resolver: zodResolver(AddUpdateAmenitySchema),
    defaultValues: amenityDetail
      ? {
          name: amenityDetail.name,
          fileKey: amenityDetail.fileKey ?? "",
          filePublicUrl: amenityDetail.filePublicUrl ?? "",
        }
      : {
          name: "",
          fileKey: "",
          filePublicUrl: "",
        },
  });

  useEffect(() => {
    if (amenityDetail) {
      form.setValue("name", amenityDetail.name);
      form.setValue("fileKey", amenityDetail.fileKey ?? "");
      form.setValue("filePublicUrl", amenityDetail.filePublicUrl ?? "");

      setImageUrl(
        amenityDetail.filePublicUrl ? amenityDetail.filePublicUrl : undefined,
      );
    }
  }, [amenityDetail, form]);

  const onSubmit = (values: z.infer<typeof AddUpdateAmenitySchema>) => {
    if (amenityId) {
      updateAmenity(
        { ...values, id: amenityId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void trpcUtils.amenities.getAmenityById.invalidate({
              id: amenityId,
            });
            void trpcUtils.amenities.getAllAmenities.invalidate();
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addAmenity(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void trpcUtils.amenities.getAllAmenities.invalidate();
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  const uploadImage = async (file: File | undefined) => {
    if (!file) return;
    try {
      setIsUploadingImage(true);
      const key = `/amenities/` + Date.now() + "-" + file.name;
      const { url, key: presignedUrlKey } =
        await awsPresignedUrlMutation.mutateAsync({
          fileName: key,
          contentType: file.type,
          publicAvailable: true,
        });
      if (!url) {
        setIsUploadingImage(false);
        toast.error("Error uploading media");
        return;
      }
      const requestOptions = {
        method: "PUT",
        body: file,
      };
      await fetch(url, requestOptions);
      const fileUrl = await awsGetPublicUrlMutation.mutateAsync({
        fileKey: presignedUrlKey,
      });

      setImageUrl(fileUrl);
      form.setValue("fileKey", presignedUrlKey);
      form.setValue("filePublicUrl", fileUrl);

      toast.success("Image uploaded successfully");
    } catch (e) {
      setIsUploadingImage(false);
      toast.error("Error uploading image");
    } finally {
      setIsUploadingImage(false);
    }
  };

  if (isLoading && amenityId) {
    return <div className="p-4 text-center">Loading amenity details...</div>;
  }

  if (error && amenityId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading amenity: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Amenity name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Input
          type="file"
          onChange={async (e) => {
            const file = e.target.files;
            if (!file || file.length === 0) return;

            await uploadImage(file[0]);
          }}
        />

        <div className="my-6 flex items-center justify-center">
          {imageUrl && (
            <Image
              src={imageUrl}
              alt="image"
              height={1000}
              width={1000}
              className="size-36"
            />
          )}

          {isUploadingImage && (
            <div className="flex items-center gap-2 font-medium">
              <Loading />
              Uploading Image
            </div>
          )}
        </div>

        {amenityId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          amenityId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!amenityId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !amenityId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default AmenitiesAddUpdateForm;
