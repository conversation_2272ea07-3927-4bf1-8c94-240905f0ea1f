"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { AmenityParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import AmenitiesAddUpdateForm from "./amenities-add-update-form";

const AmenitiesFormSheet = () => {
  const {
    isOpen,
    paramValue: amenityId,
    closeSheet,
    openSheet,
  } = useSheet(AmenityParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Amenities
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={amenityId ? "Edit Amenity" : "Add Amenity"}
      >
        {isOpen && <AmenitiesAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default AmenitiesFormSheet;
