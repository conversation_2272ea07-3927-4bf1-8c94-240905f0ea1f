"use client";

import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import {
  ChevronLeft,
  ChevronRight,
  Columns2,
  Filter,
  MoreHorizontal,
  Trash,
  SquarePen,
  Copy,
} from "lucide-react";

import { Button } from "@repo/ui/components/ui/button";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { AmenityParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import TableHeading from "~/app/components/shared/table-heading";
import SortableColumnHeader from "~/app/components/shared/sortable-column-header";
import WrappedAlert from "~/app/components/shared/wrapped-alert";
import type { ExtendedAmenity } from "~/app/types";
import { cn } from "@repo/ui/lib/utils";

export const amenitiesDataTableColumns: ColumnDef<ExtendedAmenity>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: "id",
    header: () => <button className="flex justify-start">Amenity Id</button>,
    cell: ({ row }) => <div>{row.getValue("id")}</div>,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return <div className="font-medium">{row.original.name}</div>;
    },
  },
  {
    accessorKey: "filePublicUrl",
    header: "Image",
    cell: ({ row }) => {
      return row.original.filePublicUrl ? (
        <div className="flex justify-center">
          <Image
            src={row.original.filePublicUrl}
            alt="image"
            height={500}
            width={500}
            className="size-16"
          />
        </div>
      ) : (
        <div className="text-center">No Image</div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Created On" />
    ),
    cell: ({ row }) => {
      return (
        <div className="capitalize">
          {new Date(row.getValue("createdAt")).toDateString()}
        </div>
      );
    },
  },
  {
    accessorKey: "updatedAt",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Updated On" />
    ),
    cell: ({ row }) => {
      return (
        <div className="capitalize">
          {new Date(row.getValue("updatedAt")).toDateString()}
        </div>
      );
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      return <AmenityActionCell amenity={row.original} />;
    },
  },
];

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function AmenitiesDataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      rowSelection,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    onRowSelectionChange: setRowSelection,
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <div>
      <div className="flex flex-wrap items-center justify-between gap-4 py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Filter className="h-4 w-4" />
          <input
            placeholder="Filter amenities..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="ml-auto flex items-center gap-2"
            >
              <Columns2 className="h-4 w-4" />
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-center">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <div className="mx-4 my-3 flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="size-5" />
              Previous
            </Button>
            <Button
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
              <ChevronRight className="size-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

const AmenityActionCell = ({ amenity }: { amenity: ExtendedAmenity }) => {
  const router = useRouter();
  const { openSheet } = useSheet(AmenityParamName);

  const { mutate: deleteAmenity, isPending } =
    api.amenities.removeAmenity.useMutation();
  const trpcUtils = api.useUtils();

  const handleAmenityDelete = () => {
    deleteAmenity(
      { id: amenity.id },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void trpcUtils.amenities.getAllAmenities.invalidate();
          router.refresh();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <TableHeading rightIcon={<MoreHorizontal />}>
            <span className="sr-only">Open menu</span>
          </TableHeading>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              await navigator.clipboard.writeText(amenity.id);
              toast.success("Amenity ID copied to your clipboard");
            }}
            className="cursor-pointer"
          >
            <Copy className="mr-2 size-4" />
            Copy Amenity Id
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => openSheet(amenity.id)}
            className="cursor-pointer"
          >
            <SquarePen className="mr-2 size-4" />
            Update Amenity
          </DropdownMenuItem>
          <DropdownMenuItem disabled={isPending} className="cursor-pointer">
            <WrappedAlert
              trigger={
                <button
                  className="flex items-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Trash className="mr-2 size-4" />
                  {isPending ? "Deleting..." : "Delete Amenity"}
                </button>
              }
              title="Confirm deletion"
              description="Are you sure you want to delete this amenity? This action cannot be undone."
              onConfirm={handleAmenityDelete}
              confirmText="Confirm Deletion"
              cancelText="Cancel"
            />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
