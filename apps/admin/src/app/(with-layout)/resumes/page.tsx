import { api } from "~/trpc/server";
import { resumeDataTableColumns, ResumeDataTable } from "./resume-data-table";

const ResumesPage = async () => {
  const applications = await api.career.getAllApplications();

  return (
    <>
      <h1 className="font-airbnb_w_md text-3xl font-semibold">Resumes</h1>

      <ResumeDataTable data={applications} columns={resumeDataTableColumns} />
    </>
  );
};

export default ResumesPage;
