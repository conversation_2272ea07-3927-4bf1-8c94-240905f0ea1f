"use client";
import type { Prisma } from "@repo/database";
import PropertyCard from "@repo/ui/components/shared/property-card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";

export type IProperty = Prisma.PropertyGetPayload<{
  include: {
    areaUnit: true;
    utilities: true;
    amenities: true;
    user: {
      select: {
        id: true;
        company: true;
        companyDetails: true;
      };
    };
    // comments: {
    //   include: {
    //     user: true,
    //   },
    // },
    mediaSections: {
      include: {
        media: {
          select: {
            id: true;
            fileKey: true;
            filePublicUrl: true;
          };
        };
      };
    };
  };
}>;
const AgentPropertyCardsSwiper = ({
  properties,
}: {
  properties: IProperty[];
}) => {
  return (
    <>
      {properties.length == 0 && (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-sm text-primary-2-800 md:text-base xl:text-lg 2xl:text-xl">
          No property listed.
        </div>
      )}
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {properties.length > 0 &&(
            properties.map((item, index) => (
              <CarouselItem key={index} className="basis-[340px]">
                <PropertyCard
                  locationIcon="/icons/location.svg"
                  id={item.id}
                  propertyOwnerId={item.user.id}
                  key={item.id}
                  property={item}
                />
              </CarouselItem>
            ))
          )}
        </CarouselContent>
      </Carousel>
    </>
  );
};
export default AgentPropertyCardsSwiper;
