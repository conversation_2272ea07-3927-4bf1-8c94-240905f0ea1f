import { api, HydrateClient } from "~/trpc/server";
import NewsFormSheet from "./news-form-sheet";
import NewsDataTable, { newsDataTableColumns } from "./news-data-table";

type SearchParams = Promise<{ news_id?: string }>;

const NewsPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const news = await api.news.getAllNews();

  if (searchParams.news_id) {
    await api.news.getNewsById.prefetch({ id: searchParams.news_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">News</h1>

      <NewsFormSheet />
      <NewsDataTable data={news} columns={newsDataTableColumns} />
    </HydrateClient>
  );
};

export default NewsPage;
