"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { NewsParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import NewsAddUpdateForm from "./news-add-update-form";

const NewsFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } = useSheet(NewsParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add News
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit News" : "Add News"}
      >
        {isOpen && <NewsAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default NewsFormSheet;
