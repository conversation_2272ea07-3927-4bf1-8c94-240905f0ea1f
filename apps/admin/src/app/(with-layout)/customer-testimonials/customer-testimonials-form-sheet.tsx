"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { TestimonialParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import TestimonialAddUpdateForm from "./customer-testimonials-add-update-form";

const TestimonialFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(TestimonialParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Testimonial
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Testimonial" : "Add Testimonial"}
      >
        {isOpen && <TestimonialAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default TestimonialFormSheet;
