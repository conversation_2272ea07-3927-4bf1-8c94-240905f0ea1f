import { api, HydrateClient } from "~/trpc/server";
import {
  customerTestimonialDataTableColumns,
  default as TestimonialsDataTable,
} from "./customer-testimonials-data-table";
import TestimonialFormSheet from "./customer-testimonials-form-sheet";
import { TestimonialParamName } from "~/app/helpers/constants";

type SearchParams = Promise<{ [TestimonialParamName]?: string }>;

const CustomerTestimonialsPage = async (props: {
  searchParams: SearchParams;
}) => {
  const searchParams = await props.searchParams;
  const testimonials = await api.customerTestimonails.getTestimonials();

  if (searchParams[TestimonialParamName]) {
    await api.customerTestimonails.getCustomerTestimonialById.prefetch({
      id: searchParams[TestimonialParamName],
    });
  }
  await api.city.getAllCities.prefetch();

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Customer Testimonials
      </h1>

      <TestimonialFormSheet />
      <TestimonialsDataTable
        data={testimonials}
        columns={customerTestimonialDataTableColumns}
      />
    </HydrateClient>
  );
};

export default CustomerTestimonialsPage;
