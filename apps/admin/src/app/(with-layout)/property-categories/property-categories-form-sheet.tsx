"use client";

import React from "react";
import { Button } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import CategoriesAddUpdateForm from "./categories-add-update-form";

import { useSheet } from "~/app/hooks/use-sheet";
import { CategoryParamName } from "~/app/helpers/constants";

const PropertyCategoriesFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(CategoryParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Category
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Category" : "Add Category"}
      >
        {isOpen && <CategoriesAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default PropertyCategoriesFormSheet;
