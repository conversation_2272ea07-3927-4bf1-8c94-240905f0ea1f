import { api, HydrateClient } from "~/trpc/server";
import {
  CategoriesDataTable,
  CategoriesDataTableColumns,
} from "./categories-data-table";
import PropertyCategoriesFormSheet from "./property-categories-form-sheet";

type SearchParams = Promise<{ category_id?: string }>;

const PropertyCategoriesPage = async (props: {
  searchParams: SearchParams;
}) => {
  const searchParams = await props.searchParams;
  const categories = await api.propertyCategories.getAllCategories();

  if (searchParams.category_id) {
    await api.propertyCategories.getCategoryById.prefetch({
      id: searchParams.category_id,
    });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Property Categories
      </h1>

      <PropertyCategoriesFormSheet />

      <CategoriesDataTable
        data={categories}
        columns={CategoriesDataTableColumns}
      />
    </HydrateClient>
  );
};

export default PropertyCategoriesPage;
