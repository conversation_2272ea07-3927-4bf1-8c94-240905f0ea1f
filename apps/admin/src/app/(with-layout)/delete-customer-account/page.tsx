import { api } from "~/trpc/server";
import {
  DeleteCustomerAccountDataTable,
  deleteCustomerAccountRequestsColumns,
} from "./delete-customer-account-data-table";
// import {
//   DeleteAccountDataTable,
//   deleteAccountRequestsColumns,
// } from "./delete-account-data-table";

const DeleteAccount = async () => {
  const deleteCustomerAccountRequests =
    await api.deleteAccounts.getDeleteCustomerAccountReq();

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Delete Accounts
      </h1>

      <DeleteCustomerAccountDataTable
        columns={deleteCustomerAccountRequestsColumns}
        data={deleteCustomerAccountRequests}
      />
    </>
  );
};

export default DeleteAccount;
