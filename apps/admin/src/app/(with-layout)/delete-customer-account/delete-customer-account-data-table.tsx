"use client";

import type { DeleteAccount } from "@repo/database";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { toast } from "@repo/ui/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";

import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import { format } from "date-fns";
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Filter,
  Trash,
} from "lucide-react";
import React, { useState } from "react";
import { api } from "~/trpc/react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import WrappedAlert from "~/app/components/shared/wrapped-alert";
import SortableColumnHeader from "~/app/components/shared/sortable-column-header";
import { useRouter } from "next/navigation";
import { cn } from "@repo/ui/lib/utils";

export const deleteCustomerAccountRequestsColumns: ColumnDef<DeleteAccount>[] =
  [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "id",
      header: () => <button className="flex justify-start">Account Id</button>,
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => (
        <div className="lowercase">{row.getValue("email")}</div>
      ),
    },
    {
      accessorKey: "phoneNumber",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone Number" />
      ),
    },
    {
      accessorKey: "reason",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Reason" />
      ),
    },
    {
      accessorKey: "deletedAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Deleted At" />
      ),
      cell: ({ row }) => {
        return (
          <div className="flex items-center justify-center">
            {row.original.deletedAt == null
              ? "-"
              : format(row.original.deletedAt, "dd/MM/yyyy")}
          </div>
        );
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const router = useRouter();
        const { mutate: deletingAccount, isPending } =
          api.deleteAccounts.deleteAccount.useMutation();

        const handleDeleteAccount = () => {
          deletingAccount(
            { id: row.original.id },
            {
              onSuccess: (opts) => {
                if (opts.message) {
                  toast.success(opts.message);
                  router.refresh();
                }
                if (opts.error) {
                  toast.error(opts.error);
                }
              },
              onError: (opts) => {
                toast.error(opts.message);
              },
            },
          );
        };

        return (
          <>
            {row.original.deletedAt == null && (
              <WrappedAlert
                trigger={
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center"
                    disabled={isPending}
                  >
                    <Trash className="mr-2 size-4" />
                    {isPending ? "Deleting..." : "Delete Account"}
                  </Button>
                }
                title="Delete Account Confirmation"
                description="This action cannot be undone. This will permanently delete the account and remove all data from our servers."
                onConfirm={handleDeleteAccount}
                confirmText="Delete Account"
                cancelText="Cancel"
              />
            )}
          </>
        );
      },
    },
  ];

interface DeleteAccountDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function DeleteCustomerAccountDataTable<TData, TValue>({
  columns,
  data,
}: DeleteAccountDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  return (
    <>
      <div className="flex flex-wrap items-center justify-between gap-4 py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Filter className="h-4 w-4" />
          <input
            placeholder="Filter accounts..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="ml-auto flex items-center gap-2"
            >
              <ChevronDown className="ml-2 h-4 w-4" />
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-center">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <div className="mx-4 my-3 flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="size-5" />
              Previous
            </Button>
            <Button
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
              <ChevronRight className="size-5" />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
