"use client";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@repo/ui/components/ui/carousel";
import PostCard from "@repo/ui/components/shared/post-card";
import { useRouter, useSearchParams } from "next/navigation";
import type { Prisma } from "@repo/database";

export type IPost = Prisma.PostGetPayload<{
  include: {
    user: {
      select: {
        id: true;
        filePublicUrl: true;
        cloudinaryProfileImageUrl: true;
        name: true;
        company: {
          select: {
            companyName: true;
          };
        };
      };
    };
    media: {
      select: {
        filePublicUrl: true;
        cloudinaryUrl: true;
        cloudinaryId: true;
        mediaType: true;
      };
    };
    comments: {
      select: {
        comment: true;
        isPinned: true;
        createdAt: true;
        customerId: true;
        userId: true;
        user: {
          select: {
            id: true;
            name: true;
            filePublicUrl: true;
            companyDetails: {
              select: {
                companyName: true;
              };
            };
          };
        };
        customer: {
          select: {
            id: true;
            name: true;
            profileImagePublicUrl: true;
          };
        };
      };
    };
    likes: {
      select: {
        id: true;
        postId: true;
      };
    };
  };
}>;
const AgentPostCardsSwiper = ({ posts }: { posts: IPost[] }) => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const handlePostClick = (postId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("postId", postId);
    router.replace(`?${params}`, { scroll: false });
  };

  return (
    <>
      {posts.length == 0 && (
        <div className="col-span-full flex min-h-[20vh] w-full items-center justify-center text-sm text-primary-2-800 md:text-base xl:text-lg 2xl:text-xl">
          No posts.
        </div>
      )}
      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full"
      >
        <CarouselContent className="w-full">
          {posts.length > 0 &&
            posts.map((item, index) => {
              return (
                <CarouselItem
                  key={index}
                  className="basis-[360px] md:basis-[390px] 2xl:basis-[500px]"
                >
                  <PostCard
                    hideDeleteButton={false}
                    hideReportButton={true}
                    onPostClick={handlePostClick}
                    formatTime={(date) => date.toLocaleDateString()}
                    formatCount={(count) => count.toString()}
                    {...item}
                  />
                </CarouselItem>
              );
            })}
        </CarouselContent>
      </Carousel>
    </>
  );
};

export default AgentPostCardsSwiper;
