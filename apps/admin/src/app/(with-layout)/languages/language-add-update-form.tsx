"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import { api } from "~/trpc/react";
import { skipToken } from "@tanstack/react-query";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { useSheet } from "~/app/hooks/use-sheet";
import { LanguageParamName } from "~/app/helpers/constants";

const languageFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

const LanguageAddUpdateForm = () => {
  const { paramValue: languageId, closeSheet } = useSheet(LanguageParamName);

  const {
    data: languageDetail,
    isLoading,
    error,
  } = api.language.getById.useQuery(
    languageId ? { id: languageId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  );

  const { mutate: updateLanguage, isPending: isPendingUpdateMutation } =
    api.language.update.useMutation();
  const { mutate: createLanguage, isPending: isPendingCreateMutation } =
    api.language.create.useMutation();
  const trpcUtils = api.useUtils();

  const form = useForm<z.infer<typeof languageFormSchema>>({
    resolver: zodResolver(languageFormSchema),
    defaultValues: languageDetail
      ? {
          name: languageDetail.name,
        }
      : {
          name: "",
        },
  });

  const onSubmit = (values: z.infer<typeof languageFormSchema>) => {
    if (languageId) {
      updateLanguage(
        { ...values, id: languageId },
        {
          onSuccess: () => {
            toast.success("Language updated successfully");
            void trpcUtils.language.getById.invalidate({ id: languageId });
            void trpcUtils.language.getAll.invalidate();
            closeSheet();
          },
          onError: (error) => {
            toast.error(error.message);
          },
        },
      );
    } else {
      createLanguage(values, {
        onSuccess: () => {
          toast.success("Language created successfully");
          void trpcUtils.language.getAll.invalidate();
          closeSheet();
        },
        onError: (error) => {
          toast.error(error.message);
        },
      });
    }
  };

  if (isLoading && languageId) {
    return <div className="p-4 text-center">Loading language details...</div>;
  }

  if (error && languageId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading language: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel className="text-sm font-medium">Name</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter language name"
                  className="w-full"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />

        {languageId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          languageId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!languageId && isPendingCreateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !languageId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default LanguageAddUpdateForm;
