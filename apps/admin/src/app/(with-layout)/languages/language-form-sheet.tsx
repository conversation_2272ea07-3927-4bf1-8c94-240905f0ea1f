"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { LanguageParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import LanguageAddUpdateForm from "./language-add-update-form";
import { Plus } from "lucide-react";

const LanguageFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(LanguageParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Language
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Language" : "Add Language"}
      >
        {isOpen && <LanguageAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default LanguageFormSheet;
