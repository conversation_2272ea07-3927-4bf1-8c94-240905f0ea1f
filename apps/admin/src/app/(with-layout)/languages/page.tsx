import { api, HydrateClient } from "~/trpc/server";
import { LanguageDataTable } from "./language-data-table";
import LanguageFormSheet from "./language-form-sheet";

type SearchParams = Promise<{ language_id?: string }>;

const LanguagesPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const languages = await api.language.getAll();

  if (searchParams.language_id) {
    await api.language.getById.prefetch({ id: searchParams.language_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Languages
      </h1>

      <LanguageFormSheet />
      <LanguageDataTable data={languages} />
    </HydrateClient>
  );
};

export default LanguagesPage;
