import type { TPartner } from "~/app/types";
import AgentCard from "@repo/ui/components/shared/agent-card";

const CardViewPartners = ({ partners }: { partners: TPartner[] }) => {
  return (
    <div className="mt-4 grid grid-cols-1 gap-4 py-4 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {partners.length === 0 ? (
        <p>No partners available.</p>
      ) : (
        partners.map((item) => (
          <AgentCard
            {...item}
            key={item.id}
            link="#"
            fallBackImage="/agent-fallback-image.png"
          />
        ))
      )}
    </div>
  );
};
export default CardViewPartners;
