"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@repo/ui/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

type PaginationButtonsProps = {
  currentPage: number;
  totalPages: number;
  status: string;
  view: string;
};

const PaginationButtons = ({
  currentPage,
  totalPages,
  status,
  view,
}: PaginationButtonsProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;

    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    params.set("status", status);
    params.set("view", view);

    router.push(`?${params.toString()}`);
  };

  return (
    <div className="flex items-center gap-x-4">
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage <= 1}
      >
        <ChevronLeft className="h-4 w-4" />
        Previous
      </Button>
      <span className="text-sm font-medium">
        Page {currentPage} of {totalPages}
      </span>
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
      >
        Next
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default PaginationButtons;
