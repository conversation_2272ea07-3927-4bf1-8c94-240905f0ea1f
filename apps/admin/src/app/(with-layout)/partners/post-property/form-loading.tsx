/**
 * FormLoading Component
 *
 * A simple loading indicator component used during form operations.
 * Displays a spinning loader icon centered in the container.
 * Used when form data is being fetched or processed.
 */
import { Loader2 } from "lucide-react";

const FormLoading = () => {
  return (
    <div className="flex min-h-[50vh] flex-col items-center justify-center space-y-4">
      <Loader2 className="size-6 animate-spin md:size-10" />
    </div>
  );
};

export default FormLoading;
