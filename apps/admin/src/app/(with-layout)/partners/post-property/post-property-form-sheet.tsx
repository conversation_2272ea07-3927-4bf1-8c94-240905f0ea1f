/**
 * PostPropertyFormSheet Component
 *
 * A sheet/drawer component that contains the property posting form.
 * This component is responsible for:
 * 1. Managing the visibility of the form sheet based on URL parameters
 * 2. Providing a responsive container for the property posting form
 * 3. Rendering the form header and step decider components
 *
 * The sheet opens when the URL contains a "post-property-form" parameter
 * and provides different max-widths based on screen sizes for optimal viewing.
 */
"use client";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@repo/ui/components/ui/sheet";
import { useSearchParams } from "next/navigation";
import PostPropertyFormStepDecider from "./post-property-form-step-decider";
import PostPropertyHeader from "./post-property-header";
import usePostPropertyForm from "./use-post-property-form";

const PostPropertyFormSheet = () => {
  const searchParams = useSearchParams();
  const isOpen = !!searchParams.get("post-property-form");

  const { isLastStep } = usePostPropertyForm();

  return (
    <Sheet open={isOpen} modal={isLastStep ? false : true}>
      <SheetContent className="w-full overflow-hidden rounded-bl-2xl rounded-tl-2xl p-0 sm:max-w-[430px] md:max-w-[614px] lg:max-w-[819px] xl:max-w-[1065px] 2xl:max-w-[1536px]">
        <SheetTitle aria-describedby="Post Property" className="hidden" />
        <SheetDescription className="hidden" />
        <PostPropertyHeader />
        <PostPropertyFormStepDecider />
      </SheetContent>
    </Sheet>
  );
};

export default PostPropertyFormSheet;
