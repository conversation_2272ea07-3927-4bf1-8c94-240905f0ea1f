"use client";
import { motion } from "motion/react";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";
import React from "react";
import usePostPropertyForm from "./use-post-property-form";

/**
 * Configuration for the property posting steps
 *
 * Each step object contains:
 * @property {number} id - Unique identifier for the step (1-4)
 * @property {string} alt - Alt text for the step icon
 * @property {string} currentSrc - Image source when step is active
 * @property {string} completeSrc - Image source when step is completed
 * @property {string} pendingSrc - Image source when step is pending/not yet reached
 */
const steps = [
  {
    id: 1,
    alt: "step-1",
    currentSrc: "/icons/post-property-form/step-1-current.svg",
    completeSrc: "/icons/post-property-form/step-1-complete.svg",
    pendingSrc: "/icons/post-property-form/step-1-current.svg",
  },
  {
    id: 2,
    alt: "step-2",
    pendingSrc: "/icons/post-property-form/step-2-pending.svg",
    currentSrc: "/icons/post-property-form/step-2-current.svg",
    completeSrc: "/icons/post-property-form/step-2-complete.svg",
  },
  {
    id: 3,
    alt: "step-3",
    pendingSrc: "/icons/post-property-form/step-3-pending.svg",
    currentSrc: "/icons/post-property-form/step-3-current.svg",
    completeSrc: "/icons/post-property-form/step-3-complete.svg",
  },
  {
    id: 4,
    alt: "step-4",
    pendingSrc: "/icons/post-property-form/step-4-pending.svg",
    currentSrc: "/icons/post-property-form/step-4-current.svg",
    completeSrc: "/icons/post-property-form/step-4-current.svg",
  },
];

/**
 * PostPropertyStepper Component
 *
 * A visual stepper component that shows the current progress in the property posting workflow.
 *
 * Features:
 * - Visual indication of current step
 * - Completed steps are highlighted with different styling
 * - Responsive design with different sizes based on viewport
 * - Animated transitions between states
 * - Connecting lines between step indicators show pathway/progress
 *
 * @returns {JSX.Element} A stepper UI showing the current step in the property posting process
 */
const PostPropertyStepper = () => {
  const { currStep } = usePostPropertyForm();

  return (
    <div className="container mx-auto max-w-full py-10">
      <div className="flex items-center justify-center gap-11 sm:gap-[66px] md:gap-[88px]">
        {steps.map((step) => {
          const status =
            currStep === step.id
              ? "active"
              : currStep < step.id
                ? "pending"
                : "complete";
          return (
            <React.Fragment key={step.id}>
              {currStep > step.id ? (
                <motion.div
                  animate={{
                    background:
                      status === "complete"
                        ? "#c58e00"
                        : status === "active"
                          ? "#784100"
                          : "transparent",
                  }}
                  key={step.id}
                  className={cn(
                    "relative aspect-square w-9 rounded-full transition-all duration-500 sm:w-[42px] md:w-11 lg:w-[52px] lg:after:left-[52px] xl:w-[66px] 2xl:w-[84px]",
                    {
                      "after:absolute after:left-9 after:top-1/2 after:h-0 after:w-9 after:border-t-2 after:border-solid after:border-primary-2-700 sm:after:left-[42px] sm:after:w-[58px] md:after:left-11 md:after:w-20 xl:after:left-[66px] 2xl:after:left-[84px]":
                        step.id < 4,
                    },
                  )}
                >
                  <Image
                    src={step.completeSrc}
                    alt="step-1"
                    fill
                    className="relative object-cover"
                  ></Image>
                </motion.div>
              ) : currStep === step.id ? (
                <motion.div
                  animate={{
                    background:
                      status === "complete"
                        ? "#c58e00"
                        : status === "active"
                          ? "#784100"
                          : "transparent",
                  }}
                  key={step.id}
                  className={cn(
                    "relative aspect-square w-9 rounded-full transition-all duration-500 sm:w-[42px] md:w-11 lg:w-[52px] lg:after:left-[52px] xl:w-[66px] 2xl:w-[84px]",
                    {
                      "after:absolute after:left-9 after:top-1/2 after:h-0 after:w-9 after:border-t-2 after:border-dashed after:border-primary-2-750 sm:after:left-[42px] sm:after:w-[58px] md:after:left-11 md:after:w-20 xl:after:left-[66px] 2xl:after:left-[84px]":
                        step.id < 4,
                    },
                  )}
                >
                  <Image
                    src={step.currentSrc}
                    alt="step-1"
                    fill
                    className="relative object-cover"
                  ></Image>
                </motion.div>
              ) : (
                <motion.div
                  animate={{
                    background:
                      status === "complete"
                        ? "#c58e00"
                        : status === "active"
                          ? "#784100"
                          : "transparent",
                  }}
                  key={step.id}
                  className={cn(
                    "relative aspect-square w-9 rounded-full transition-all duration-500 sm:w-[42px] md:w-11 lg:w-[52px] lg:after:left-[52px] xl:w-[66px] 2xl:w-[84px]",
                    {
                      "after:absolute after:left-9 after:top-1/2 after:h-0 after:w-9 after:border-t-2 after:border-dashed after:border-primary-2-700 sm:after:left-[42px] sm:after:w-[58px] md:after:left-11 md:after:w-20 xl:after:left-[66px] 2xl:after:left-[84px]":
                        step.id < 4,
                    },
                  )}
                >
                  <Image
                    src={step.pendingSrc}
                    alt="step-1"
                    fill
                    className="relative object-cover"
                  ></Image>
                </motion.div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default PostPropertyStepper;
