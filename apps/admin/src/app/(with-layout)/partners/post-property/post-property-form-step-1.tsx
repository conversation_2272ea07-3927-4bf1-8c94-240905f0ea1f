/**
 * PostPropertyFormStep1 Component
 *
 * The first step of the property posting form that collects basic property information.
 * This component handles:
 * 1. Property registry file upload to AWS S3
 * 2. Basic property details like title, type, and category
 * 3. Property specifications (bedrooms, bathrooms, area)
 * 4. Property pricing information
 *
 * Features:
 * - File upload with validation for PDF, PNG, and JPEG formats
 * - Form validation using Zod schema with TanStack Form
 * - Auto-population of form fields when editing existing property
 * - Real-time error handling and field validation
 * - Dynamic field visibility based on selected property category
 * - Loading state during data fetching
 *
 * @component
 */

import usePostPropertyForm from "./use-post-property-form";
import PostPropertyFooter from "./post-property-footer";

import { useForm, useStore } from "@tanstack/react-form";
import { Input } from "@repo/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { Textarea } from "@repo/ui/components/ui/textarea";
import { PostPropertyStep1BaseSchema } from "~/server/api/validations/post-property.validation";
import type { PostPropertyStep1Base } from "~/server/api/validations/post-property.validation";
import { PropertyForEnum } from "@repo/database";
import FormLabel from "~/app/components/shared/form-label";
import FieldError from "~/app/components/shared/field-error";
import { api } from "~/trpc/react";
import { z } from "zod";
import { toast } from "@repo/ui/components/ui/sonner";
import { useEffect, useState } from "react";
import { Button } from "@repo/ui/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { skipToken } from "@tanstack/react-query";
import FormLoading from "./form-loading";

/**
 * Allowed file types for property registry document uploads
 */
const ACCEPTED_FILE_TYPES = ["application/pdf", "image/png", "image/jpeg"];
const FILE_TYPE_ERROR = "Only PDF, PNG, and JPEG files are accepted";

const PostPropertyFormStep1 = () => {
  const trpcUtils = api.useUtils();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { goToNextStep, agentId, propertyId, setPropertyId } =
    usePostPropertyForm();
  const [isUploading, setIsUploading] = useState(false);
  const [isSettingValues, setIsSettingValues] = useState(true);

  const { mutate: submitStep1 } = api.property.submitStep1.useMutation();
  const awsMutation = api.aws.getPresignedUrl.useMutation();

  const { data: propertyStep1Data, isPending } = api.property.getStep1.useQuery(
    propertyId && agentId ? { id: propertyId, agentId: agentId } : skipToken,
    {
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      staleTime: 0,
    },
  );

  const form = useForm({
    defaultValues: {
      registeryFileKey: "",
      propertyTitle: "",
      propertyFor: PropertyForEnum.RENT,
      bedrooms: undefined,
      bathrooms: undefined,
      propertyPrice: 0,
      securityDeposit: undefined,
      areaUnitId: "",
      areaUnit: "",
      area: 0,
      propertyCategoryId: "",
      propertyTypeId: "",
      propertyType: "",
      aboutProperty: undefined,
    } as PostPropertyStep1Base,
    onSubmit: ({ value }) => {
      if (!agentId) {
        return toast.error("Agent id is missing");
      }

      submitStep1(
        { ...value, agentId: agentId, propertyId: propertyId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void trpcUtils.property.getStep1.invalidate();
            void setPropertyId(opts.propertyId);
            void goToNextStep();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    },
  });

  const propertyCategoryId = useStore(
    form.store,
    (state) => state.values.propertyCategoryId,
  );

  const { data: categories } =
    api.propertyCategories.getAllCategories.useQuery();
  const { data: propertyTypes } =
    api.property.getPropertyTypeByCategoryId.useQuery(
      {
        categoryId: propertyCategoryId,
      },
      {
        enabled: !!propertyCategoryId,
      },
    );
  const { data: selectedCategory } =
    api.propertyCategories.getCategoryById.useQuery(
      { id: propertyCategoryId },
      { enabled: !!propertyCategoryId },
    );
  const { data: areaUnits } = api.areaUnit.getAllAreaUnits.useQuery();

  /**
   * Removes the uploaded registry file from the form
   */
  const handleDeleteFile = () => {
    form.resetField("registeryFileKey");
  };

  /**
   * Uploads a file to AWS S3 and updates the form with the file key
   *
   * @param {File} file - The file to upload
   * @returns {Promise<void>}
   */
  const uploadFile = async (file: File) => {
    // Validate file type
    if (
      !ACCEPTED_FILE_TYPES.includes(
        file.type as "application/pdf" | "image/png" | "image/jpeg",
      )
    ) {
      toast.error(FILE_TYPE_ERROR);
      return;
    }

    setIsUploading(true);
    const fileType = file.type;
    const fileName = file.name;

    try {
      // Get presigned URL from AWS
      const date = new Date();
      const { url, key } = await awsMutation.mutateAsync({
        fileName: `/property/${date.getTime()}-${fileName}`,
        contentType: fileType,
      });

      if (!url) {
        toast.error("Failed to get upload URL");
        return;
      }

      // Upload file to presigned URL
      const requestOptions = {
        method: "PUT",
        body: file,
      };

      const res = await fetch(url, requestOptions);

      if (res.ok) {
        console.log("key", key);
        form.setFieldValue("registeryFileKey", key);
        await form.validateField("registeryFileKey", "change");
        toast.success("File uploaded successfully");
        return;
      }

      toast.error("Error uploading file");
    } catch (e) {
      toast.error("Error uploading file");
      console.error(e);
    } finally {
      setIsUploading(false);
    }
  };

  const registeryFileKey = useStore(
    form.store,
    (state) => state.values.registeryFileKey,
  );

  /**
   * Populate form with existing property data when editing
   */
  useEffect(() => {
    if (propertyStep1Data) {
      setIsSettingValues(true);
      // Populate form fields with existing property data
      form.setFieldValue(
        "registeryFileKey",
        propertyStep1Data.registeryFileKey ?? "",
      );
      form.setFieldValue(
        "propertyTitle",
        propertyStep1Data.propertyTitle ?? "",
      );
      form.setFieldValue("propertyFor", propertyStep1Data.propertyFor);
      form.setFieldValue("bedrooms", propertyStep1Data.bedrooms ?? undefined);
      form.setFieldValue("bathrooms", propertyStep1Data.bathrooms ?? undefined);
      form.setFieldValue(
        "propertyPrice",
        Number(propertyStep1Data.propertyPrice ?? 0),
      );
      form.setFieldValue(
        "securityDeposit",
        propertyStep1Data.securityDeposit
          ? Number(propertyStep1Data.securityDeposit)
          : undefined,
      );
      form.setFieldValue("areaUnitId", propertyStep1Data.areaUnitId ?? "");
      form.setFieldValue("area", propertyStep1Data.area ?? 0);
      form.setFieldValue(
        "propertyCategoryId",
        propertyStep1Data.propertyCategoryId ?? "",
      );
      form.setFieldValue("propertyTypeId", propertyStep1Data.propertyTypeId);
      form.setFieldValue(
        "aboutProperty",
        propertyStep1Data.aboutProperty ?? undefined,
      );

      // Validate all fields after setting values
      void form.validateAllFields("mount");
      setIsSettingValues(false);
    }
  }, [propertyStep1Data, form]);

  // Show loading state while fetching data
  if ((isPending || isSettingValues) && propertyId) {
    return <FormLoading />;
  }

  return (
    <>
      <form
        onSubmit={(e) => {
          e.preventDefault();
        }}
        className="container mx-auto max-w-full overflow-auto"
      >
        <div className="flex flex-col gap-2.5 md:gap-3 lg:gap-4 xl:gap-5 2xl:gap-6">
          {/* registery file key */}
          <div>
            <form.Field
              name="registeryFileKey"
              validators={{
                onChange: PostPropertyStep1BaseSchema.pick({
                  registeryFileKey: true,
                }).shape.registeryFileKey,
              }}
            >
              {(field) => (
                <>
                  <FormLabel
                    className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                    htmlFor={field.name}
                  >
                    Registry File
                  </FormLabel>
                  {!registeryFileKey ? (
                    <div className="flex flex-col gap-2">
                      <Input
                        id="fileUpload"
                        name="fileUpload"
                        type="file"
                        accept={ACCEPTED_FILE_TYPES.join(",")}
                        onChange={async (e) => {
                          if (e.target.files?.[0]) {
                            await uploadFile(e.target.files[0]);
                          }
                        }}
                        disabled={isUploading}
                      />
                      {isUploading && <div>Uploading...</div>}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <div className="flex-1 truncate rounded border p-2">
                        {registeryFileKey.split("/").pop()}
                      </div>
                      <Button
                        variant="destructive"
                        onClick={handleDeleteFile}
                        type="button"
                      >
                        Remove
                      </Button>
                    </div>
                  )}

                  <FieldError>
                    {field.state.meta.errors.length > 0
                      ? field.state.meta.errorMap.onChange?.map(
                          (item) => item.message,
                        )
                      : null}
                  </FieldError>
                </>
              )}
            </form.Field>
          </div>

          {/* property title */}
          <div>
            <form.Field
              name="propertyTitle"
              validators={{
                onChange: PostPropertyStep1BaseSchema.pick({
                  propertyTitle: true,
                }).shape.propertyTitle,
              }}
            >
              {(field) => (
                <>
                  <FormLabel
                    className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                    htmlFor={field.name}
                  >
                    Property Title
                  </FormLabel>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    type="text"
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  <FieldError>
                    {field.state.meta.errors.length > 0
                      ? field.state.meta.errorMap.onChange?.map(
                          (item) => item.message,
                        )
                      : null}
                  </FieldError>
                </>
              )}
            </form.Field>
          </div>

          {/* property for */}
          <div>
            <form.Field
              name="propertyFor"
              validators={{
                onChange: PostPropertyStep1BaseSchema.pick({
                  propertyFor: true,
                }).shape.propertyFor,
              }}
            >
              {(field) => (
                <>
                  <FormLabel
                    className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                    htmlFor={field.name}
                  >
                    Property For
                  </FormLabel>
                  <Select
                    value={field.state.value}
                    onValueChange={(value) =>
                      field.handleChange(value as PropertyForEnum)
                    }
                  >
                    <SelectTrigger id={field.name}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(PropertyForEnum).map((item) => (
                        <SelectItem key={item} value={item}>
                          {item}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FieldError>
                    {field.state.meta.errors.length > 0
                      ? field.state.meta.errorMap.onChange?.map(
                          (item) => item.message,
                        )
                      : null}
                  </FieldError>
                </>
              )}
            </form.Field>
          </div>

          <div className="flex items-center gap-3">
            {/* property category */}
            <div className="flex-1">
              <form.Field
                name="propertyCategoryId"
                listeners={{
                  onChange: ({ value, fieldApi }) => {
                    if (selectedCategory?.showBedrooms === "HIDE") {
                      fieldApi.form.setFieldValue("bedrooms", undefined);
                    } else if (selectedCategory?.showBedrooms === "OPTIONAL") {
                      fieldApi.form.setFieldValue("bedrooms", undefined);
                    } else {
                      fieldApi.form.resetField("bedrooms");
                    }

                    if (selectedCategory?.showBathrooms === "HIDE") {
                      fieldApi.form.setFieldValue("bathrooms", undefined);
                    } else if (selectedCategory?.showBathrooms === "OPTIONAL") {
                      fieldApi.form.setFieldValue("bathrooms", undefined);
                    } else {
                      fieldApi.form.resetField("bathrooms");
                    }

                    if (selectedCategory?.showSecurityDeposit === "HIDE") {
                      fieldApi.form.setFieldValue("securityDeposit", undefined);
                    } else if (
                      selectedCategory?.showSecurityDeposit === "OPTIONAL"
                    ) {
                      fieldApi.form.setFieldValue("securityDeposit", undefined);
                    } else {
                      fieldApi.form.resetField("securityDeposit");
                    }

                    if (selectedCategory?.showAboutProperty === "HIDE") {
                      fieldApi.form.setFieldValue("aboutProperty", undefined);
                    } else if (
                      selectedCategory?.showAboutProperty === "OPTIONAL"
                    ) {
                      fieldApi.form.setFieldValue("aboutProperty", undefined);
                    } else {
                      fieldApi.form.resetField("aboutProperty");
                    }

                    const params = new URLSearchParams(searchParams);
                    params.set("propertyCategoryId", value);
                    router.replace(`?${params.toString()}`);
                  },
                }}
                validators={{
                  onChange: PostPropertyStep1BaseSchema.pick({
                    propertyCategoryId: true,
                  }).shape.propertyCategoryId,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      Property Category
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      disabled={!!propertyId}
                      onValueChange={(value) => field.handleChange(value)}
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories?.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>

            {/* property type */}
            <div className="flex-1">
              <form.Field
                name="propertyTypeId"
                validators={{
                  onChange: PostPropertyStep1BaseSchema.pick({
                    propertyTypeId: true,
                  }).shape.propertyTypeId,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      Property Type
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) => field.handleChange(value)}
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {propertyTypes?.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* bedroom */}
            {selectedCategory?.showBedrooms !== "HIDE" && (
              <div className="flex-1">
                <form.Field
                  name="bedrooms"
                  validators={{
                    onChange:
                      selectedCategory?.showBedrooms === "SHOW"
                        ? PostPropertyStep1BaseSchema.pick({
                            bedrooms: true,
                          }).shape.bedrooms.refine((val) =>
                            !val ? false : true,
                          )
                        : selectedCategory?.showBedrooms === "OPTIONAL"
                          ? z.coerce
                              .number()
                              .optional()
                              .refine((val) => val === undefined || val >= 1, {
                                message:
                                  "Number of bedrooms must be at least 1",
                              })
                          : undefined,
                  }}
                >
                  {(field) => (
                    <>
                      <FormLabel
                        className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                        htmlFor={field.name}
                      >
                        Bedrooms{" "}
                        {selectedCategory?.showBedrooms === "OPTIONAL"
                          ? "(Optional)"
                          : ""}
                      </FormLabel>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value ?? ""}
                        type="number"
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : e.target.valueAsNumber;
                          field.handleChange(value);
                        }}
                      />
                      <FieldError>
                        {field.state.meta.errors.length > 0
                          ? field.state.meta.errorMap.onChange?.map(
                              (item) => item.message,
                            )
                          : null}
                      </FieldError>
                    </>
                  )}
                </form.Field>
              </div>
            )}

            {/* bathrooms */}
            {selectedCategory?.showBathrooms !== "HIDE" && (
              <div className="flex-1">
                <form.Field
                  name="bathrooms"
                  validators={{
                    onChange:
                      selectedCategory?.showBathrooms === "SHOW"
                        ? PostPropertyStep1BaseSchema.pick({
                            bathrooms: true,
                          }).shape.bathrooms.refine((val) =>
                            !val ? false : true,
                          )
                        : selectedCategory?.showBathrooms === "OPTIONAL"
                          ? z.coerce
                              .number()
                              .optional()
                              .refine((val) => val === undefined || val >= 1, {
                                message:
                                  "Number of bathrooms must be at least 1",
                              })
                          : undefined,
                  }}
                >
                  {(field) => (
                    <>
                      <FormLabel
                        className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                        htmlFor={field.name}
                      >
                        Bathrooms{" "}
                        {selectedCategory?.showBathrooms === "OPTIONAL"
                          ? "(Optional)"
                          : ""}
                      </FormLabel>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value ?? ""}
                        type="number"
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : e.target.valueAsNumber;
                          field.handleChange(value);
                        }}
                      />
                      <FieldError>
                        {field.state.meta.errors.length > 0
                          ? field.state.meta.errorMap.onChange?.map(
                              (item) => item.message,
                            )
                          : null}
                      </FieldError>
                    </>
                  )}
                </form.Field>
              </div>
            )}
          </div>

          <div className="flex items-center gap-3">
            {/* security deposit */}
            {selectedCategory?.showSecurityDeposit !== "HIDE" && (
              <div className="flex-1">
                <form.Field
                  name="securityDeposit"
                  validators={{
                    onChange:
                      selectedCategory?.showSecurityDeposit === "SHOW"
                        ? PostPropertyStep1BaseSchema.pick({
                            securityDeposit: true,
                          }).shape.securityDeposit.refine((val) =>
                            !val ? false : true,
                          )
                        : selectedCategory?.showSecurityDeposit === "OPTIONAL"
                          ? PostPropertyStep1BaseSchema.pick({
                              securityDeposit: true,
                            }).shape.securityDeposit
                          : undefined,
                  }}
                >
                  {(field) => (
                    <>
                      <FormLabel
                        className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                        htmlFor={field.name}
                      >
                        Security Deposit{" "}
                        {selectedCategory?.showSecurityDeposit === "OPTIONAL"
                          ? "(Optional)"
                          : ""}
                      </FormLabel>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value ?? ""}
                        type="number"
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? undefined
                              : e.target.valueAsNumber;
                          field.handleChange(value);
                        }}
                      />
                      <FieldError>
                        {field.state.meta.errors.length > 0
                          ? field.state.meta.errorMap.onChange?.map(
                              (item) => item.message,
                            )
                          : null}
                      </FieldError>
                    </>
                  )}
                </form.Field>
              </div>
            )}

            {/* property price */}
            <div className="flex-1">
              <form.Field
                name="propertyPrice"
                validators={{
                  onChange: PostPropertyStep1BaseSchema.pick({
                    propertyPrice: true,
                  }).shape.propertyPrice,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      Property Price
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      value={field.state.value}
                      type="number"
                      onChange={(e) =>
                        field.handleChange(e.target.valueAsNumber)
                      }
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* area in */}
            <div className="flex-1">
              <form.Field
                name="areaUnitId"
                validators={{
                  onChange: PostPropertyStep1BaseSchema.pick({
                    areaUnitId: true,
                  }).shape.areaUnitId,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      Area Unit
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) => field.handleChange(value)}
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {areaUnits?.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>

            {/* area */}
            <div className="flex-1">
              <form.Field
                name="area"
                validators={{
                  onChange: PostPropertyStep1BaseSchema.pick({
                    area: true,
                  }).shape.area,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      Area
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      value={field.state.value}
                      type="number"
                      onChange={(e) =>
                        field.handleChange(e.target.valueAsNumber)
                      }
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          </div>

          {/* about property */}
          <div>
            {selectedCategory?.showAboutProperty === "HIDE" ? null : (
              <form.Field
                name="aboutProperty"
                validators={{
                  onChange:
                    selectedCategory?.showAboutProperty === "SHOW"
                      ? PostPropertyStep1BaseSchema.pick({
                          aboutProperty: true,
                        }).shape.aboutProperty.refine((val) =>
                          !val ? false : true,
                        )
                      : selectedCategory?.showAboutProperty === "OPTIONAL"
                        ? PostPropertyStep1BaseSchema.pick({
                            aboutProperty: true,
                          }).shape.aboutProperty
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      About Property{" "}
                      {selectedCategory?.showAboutProperty === "OPTIONAL"
                        ? "(Optional)"
                        : ""}
                    </FormLabel>
                    <Textarea
                      id={field.name}
                      name={field.name}
                      value={field.state.value}
                      onChange={(e) => {
                        const value =
                          e.target.value === "" ? undefined : e.target.value;
                        field.handleChange(value);
                      }}
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            )}
          </div>
        </div>
      </form>

      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting]}
        children={([canSubmit, isSubmitting]) => (
          <PostPropertyFooter
            canSubmit={canSubmit}
            isSubmitting={isSubmitting}
            handleNextStep={async () => await form.handleSubmit()}
          />
        )}
      />
    </>
  );
};

export default PostPropertyFormStep1;
