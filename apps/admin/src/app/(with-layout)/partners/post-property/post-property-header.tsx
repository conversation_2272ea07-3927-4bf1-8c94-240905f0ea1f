import { ChevronLeftIcon, CircleXIcon } from "lucide-react";
import React from "react";
import usePostPropertyForm from "./use-post-property-form";

/**
 * PostPropertyHeader Component
 *
 * A header component for the property posting form that provides navigation controls.
 *
 * Features:
 * - Title "Post Property" displayed prominently in the center
 * - Back button that appears on all steps except the first
 * - Close button (X) to exit the property posting flow completely
 * - Responsive design with different sizes based on viewport
 * - Backdrop blur effect for visual distinction
 * - Accessible buttons with proper aria-labels
 *
 * @returns {JSX.Element} Header with title and navigation controls for the property posting form
 */
const PostPropertyHeader = () => {
  const { goToPreviousStep, handleCloseForm, isFirstStep } =
    usePostPropertyForm();

  return (
    <div className="bg-text-30 py-3 backdrop-blur-[10px] md:py-3.5 lg:py-4 2xl:py-5">
      <div className="container mx-auto max-w-full">
        <div className="flex items-center justify-between">
          {!isFirstStep ? (
            <button
              onClick={async () => await goToPreviousStep()}
              className="cursor-pointer p-2 transition-opacity hover:opacity-80"
              aria-label="Go back"
            >
              <ChevronLeftIcon className="size-5 text-primary-2-800 lg:size-6 xl:size-8 2xl:size-10" />
            </button>
          ) : (
            <div />
          )}

          <h2 className="text-center font-airbnb_w_xbd text-xl font-extrabold text-secondary-2-700 md:text-3xl lg:text-4xl xl:text-[40px] xl:leading-[48px] 2xl:text-5xl 2xl:leading-[56px]">
            Post Property
          </h2>

          <button
            onClick={handleCloseForm}
            className="p-2 transition-opacity hover:opacity-80"
            aria-label="Close"
          >
            <CircleXIcon className="size-5 text-primary-2-800 lg:size-6 xl:size-8 2xl:size-10" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostPropertyHeader;
