import { But<PERSON> } from "@repo/ui/components/ui/button";
import usePostPropertyForm from "./use-post-property-form";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";

/**
 * Props for the PostPropertyFooter component
 *
 * @typedef {Object} PostPropertyFooterProps
 * @property {boolean | undefined} canSubmit - Flag indicating if the current form step can be submitted
 * @property {boolean | undefined} isSubmitting - Flag indicating if form submission is in progress
 * @property {() => Promise<void>} handleNextStep - Function to handle proceeding to the next step
 */
type PostPropertyFooterProps = {
  canSubmit: boolean | undefined;
  isSubmitting: boolean | undefined;
  handleNextStep: () => Promise<void>;
};

/**
 * PostPropertyFooter Component
 *
 * A fixed footer component for the property posting form that contains navigation buttons.
 *
 * Features:
 * - Previous step button (hidden on first step)
 * - Next step/Submit button with dynamic text based on current step
 * - Loading indicator during form submission
 * - Responsive design with different sizes based on viewport
 * - Disabled state for Next/Submit button based on form validity
 * - Backdrop blur effect for visual distinction
 *
 * @param {PostPropertyFooterProps} props - Component props
 * @returns {JSX.Element} Footer with navigation buttons for the property posting form
 */
const PostPropertyFooter = ({
  canSubmit,
  isSubmitting,
  handleNextStep,
}: PostPropertyFooterProps) => {
  const { goToPreviousStep, isFirstStep, isLastStep } = usePostPropertyForm();

  return (
    <div className="absolute bottom-0 w-full bg-primary-0/30 py-3 backdrop-blur-[15px] sm:max-w-[430px] md:max-w-[614px] md:py-9 lg:max-w-[819px] xl:max-w-[1065px] 2xl:max-w-[1536px]">
      <div className="container mx-auto max-w-full">
        <div className="flex items-center justify-between gap-10">
          {!isFirstStep ? (
            <Button
              type="button"
              onClick={goToPreviousStep}
              className="flex w-[173px] gap-2 rounded-xl bg-secondary-2-100 px-6 py-3.5 font-airbnb_w_md text-sm font-medium text-primary-2-800 hover:text-white md:w-[216px] lg:w-[359px] lg:text-lg 2xl:text-xl"
            >
              <ChevronLeft className="size-4" />
              Previous Step
            </Button>
          ) : (
            <div />
          )}

          <Button
            onClick={handleNextStep}
            disabled={!canSubmit || isSubmitting}
            className="flex w-[173px] items-center gap-2 rounded-xl px-6 py-3.5 font-airbnb_w_md text-sm font-medium md:w-[216px] lg:w-[359px] lg:text-lg 2xl:text-xl"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                {isLastStep ? "Submitting..." : "Processing..."}
              </>
            ) : (
              <>
                {isLastStep ? "Submit Property" : "Next Step"}
                {!isLastStep && <ChevronRight className="size-6" />}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PostPropertyFooter;
