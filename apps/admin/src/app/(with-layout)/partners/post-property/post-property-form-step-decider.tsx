/**
 * PostPropertyFormStepDecider Component
 *
 * A controller component that manages the multi-step property posting form flow.
 * This component:
 * 1. Determines which step of the form to display based on current step state
 * 2. Maps step numbers to their corresponding form components
 * 3. Provides consistent layout structure for all form steps
 * 4. Renders the property form stepper navigation
 *
 * The form consists of 4 main steps:
 * - Step 1: Basic property information (title, category, pricing)
 * - Step 2: Location and address details (maps integration, address fields)
 * - Step 3: Property attributes and amenities (features, facilities)
 * - Step 4: Property media uploads (images, videos, documents)
 *
 * @component
 * @returns {JSX.Element} The current form step wrapped in a container with stepper
 */

import PostPropertyFormStep1 from "./post-property-form-step-1";
import PostPropertyFormStep2 from "./post-property-form-step-2";
import PostPropertyFormStep3 from "./post-property-form-step-3";
import PostPropertyFormStep4 from "./post-property-form-step-4";
import PostPropertyStepper from "./post-property-stepper";
import usePostPropertyForm from "./use-post-property-form";
import type { Step } from "./use-post-property-form";

/**
 * Map of step numbers to their corresponding form components
 * Used to dynamically render the correct step component
 */
const FormStepsMap: Record<Step, React.FC> = {
  1: PostPropertyFormStep1,
  2: PostPropertyFormStep2,
  3: PostPropertyFormStep3,
  4: PostPropertyFormStep4,
};

const PostPropertyFormStepDecider = () => {
  const { currStep } = usePostPropertyForm();
  const FormStep = FormStepsMap[currStep];

  return (
    <div className="flex h-full flex-col overflow-y-auto pb-20 md:pb-36 lg:pb-40">
      {/* Stepper component that shows current progress */}
      <PostPropertyStepper />

      {/* Current form step component */}
      <div className="pb-20">{<FormStep />}</div>
    </div>
  );
};

export default PostPropertyFormStepDecider;
