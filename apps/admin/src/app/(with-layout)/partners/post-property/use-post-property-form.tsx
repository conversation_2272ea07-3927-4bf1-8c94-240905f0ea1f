"use client";
import { useCallback, useEffect } from "react";
import { parseAsString, useQueryState } from "nuqs";

/**
 * Constants defining the boundaries of the property posting form flow
 */
const POST_PROPERTY_FORM_MIN_STEPS = 1;
const POST_PROPERTY_FORM_MAX_STEPS = 4;

/**
 * Valid step numbers for the property posting form
 * @typedef {1 | 2 | 3 | 4} Step
 */
export type Step = 1 | 2 | 3 | 4;

/**
 * Return type for the usePostPropertyForm hook
 *
 * @typedef {Object} PostPropertyFormResult
 * @property {string | null} agentId - ID of the agent posting the property
 * @property {Step | null} currStep - Current step in the form flow
 * @property {string | null} propertyId - ID of the property being created or edited
 * @property {string | null} categoryId - ID of the selected property category
 * @property {() => Promise<void>} goToNextStep - Function to navigate to the next form step
 * @property {() => Promise<void>} goToPreviousStep - Function to navigate to the previous form step
 * @property {(id: string | null) => Promise<void>} setPropertyId - Function to update the property ID
 * @property {() => Promise<void>} handleCloseForm - Function to close the form and reset state
 * @property {boolean} isFormOpen - Whether the property form is currently open
 * @property {boolean} isFirstStep - Whether the current step is the first step
 * @property {boolean} isLastStep - Whether the current step is the last step
 */

/**
 * usePostPropertyForm Hook
 *
 * A custom hook that manages the state and logic for the property posting form.
 * This hook provides a comprehensive solution for multi-step form management
 * by using URL query parameters to persist state.
 *
 * Features:
 * - Multi-step form state management with URL-based persistence
 * - Navigation between form steps with validation
 * - Form data persistence between page refreshes
 * - Property ID and agent ID management
 * - Form open/close state handling
 * - Step boundary enforcement
 *
 * @returns {PostPropertyFormResult} Object containing form state, navigation methods, and utilities
 */
const usePostPropertyForm = () => {
  // URL query parameters for form state persistence
  const [agentId, setAgentId] = useQueryState("agentId", parseAsString);
  const [categoryId, setCategoryId] = useQueryState(
    "propertyCategoryId",
    parseAsString,
  );
  const [propertyId, setPropertyId] = useQueryState(
    "propertyId",
    parseAsString,
  );
  const [postPropertyForm, setPostPropertyForm] = useQueryState(
    "post-property-form",
    parseAsString,
  );

  /**
   * State for the current step in the form flow
   * Uses URL query parameter to persist state across page refreshes
   */
  const [currStep, setCurrStep] = useQueryState<Step>("step", {
    defaultValue: POST_PROPERTY_FORM_MIN_STEPS,
    shallow: false,
    parse(value: string | null) {
      if (!value || value === "") return null;
      else {
        const number = Number(value);
        if (Number.isNaN(number)) {
          return null;
        }
        return number as Step;
      }
    },
  });

  /**
   * Navigate to the next step in the form flow
   * Will not exceed the maximum step limit defined by POST_PROPERTY_FORM_MAX_STEPS
   *
   * @returns {Promise<void>} Promise that resolves when the step is changed
   */
  const goToNextStep = useCallback(async () => {
    if (currStep < POST_PROPERTY_FORM_MAX_STEPS) {
      await setCurrStep((currStep + 1) as Step);
    }
  }, [currStep, setCurrStep]);

  /**
   * Navigate to the previous step in the form flow
   * Will not go below the minimum step limit defined by POST_PROPERTY_FORM_MIN_STEPS
   *
   * @returns {Promise<void>} Promise that resolves when the step is changed
   */
  const goToPreviousStep = useCallback(async () => {
    if (currStep > POST_PROPERTY_FORM_MIN_STEPS) {
      await setCurrStep((currStep - 1) as Step);
    }
  }, [currStep, setCurrStep]);

  /**
   * Close the property form and reset all related state
   * Removes all query parameters related to the form
   *
   * @returns {Promise<void>} Promise that resolves when all state is reset
   */
  const closeForm = useCallback(async () => {
    // Using nuqs to remove all form-related query parameters
    await setPostPropertyForm(null);
    await setPropertyId(null);
    await setCategoryId(null);
    await setAgentId(null);
  }, [setPostPropertyForm, setPropertyId, setCategoryId, setAgentId]);

  /**
   * Effect to enforce step boundaries and validate required parameters
   * - Ensures current step is within valid range
   * - Redirects to step 1 if required IDs are missing
   */
  useEffect(() => {
    // Verify step is within valid bounds
    const isInvalidStep =
      currStep < POST_PROPERTY_FORM_MIN_STEPS ||
      currStep > POST_PROPERTY_FORM_MAX_STEPS;
    if (isInvalidStep) {
      void setCurrStep(POST_PROPERTY_FORM_MIN_STEPS);
    }

    // Enforce that steps beyond 1 require all IDs to be present
    if ((!propertyId || !categoryId || !agentId) && currStep !== 1) {
      void setCurrStep(POST_PROPERTY_FORM_MIN_STEPS);
    }
  }, [currStep, propertyId, categoryId, agentId, setCurrStep]);

  return {
    agentId,
    currStep,
    propertyId,
    categoryId,
    goToNextStep,
    goToPreviousStep,
    setPropertyId,
    handleCloseForm: closeForm,
    isFormOpen: !!postPropertyForm,
    isFirstStep: currStep === POST_PROPERTY_FORM_MIN_STEPS,
    isLastStep: currStep === POST_PROPERTY_FORM_MAX_STEPS,
  };
};

export default usePostPropertyForm;
