"use client";

import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import React, { useState } from "react";
import { Button } from "@repo/ui/components/ui/button";
import {
  ChevronLeft,
  ChevronRight,
  Columns2,
  Copy,
  Edit,
  Info,
  LandPlot,
  MoreHorizontal,
  Trash,
  CheckChe<PERSON>,
  Search,
} from "lucide-react";
import { toast } from "@repo/ui/components/ui/sonner";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { format } from "date-fns";
import { api } from "~/trpc/react";
import { cn } from "@repo/ui/lib/utils";
import {
  PartnerParamName,
  PostPropertyParamName,
  AgentIdParamName,
} from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import WrappedAlert from "~/app/components/shared/wrapped-alert";
import TableHeading from "~/app/components/shared/table-heading";
import SortableColumnHeader from "~/app/components/shared/sortable-column-header";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/ui/popover";
import StatusRejectionDialog from "./status-rejection-dialog";
import type { TPartner } from "~/app/types";

interface DataTableProps<TData> {
  data: TData[];
}

export function PartnerDataTable({ data }: DataTableProps<TPartner>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = useState("");

  const { openSheet } = useSheet(PartnerParamName);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const trpcUtils = api.useUtils();

  const { mutate: deletePartner } = api.partner.deletePartner.useMutation({
    onSuccess: (opts) => {
      toast.success(opts.message);
      void trpcUtils.partner.getPartners.invalidate();
    },
    onError: (opts) => {
      toast.error(opts.message);
    },
  });

  const { mutate: approvePartner } = api.partner.approvePartner.useMutation({
    onSuccess: (opts) => {
      toast.success(opts.message);
      void trpcUtils.partner.getPartners.invalidate();
    },
    onError: (opts) => {
      toast.error(opts.message);
    },
  });

  const handlePostProperty = (partnerId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(PostPropertyParamName, "open");
    params.set(AgentIdParamName, partnerId);
    history.pushState(null, "", `${pathname}?${params.toString()}`);
  };

  const columns: ColumnDef<TPartner>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "id",
      header: () => <button className="flex justify-start">Partner Id</button>,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Name" />
      ),
    },
    {
      accessorKey: "active",
      header: () => <div>Status</div>,
      cell: ({ row }) => (
        <div
          className={cn(
            "flex items-center gap-2",
            row.original.active ? "text-green-500" : "text-red-500",
          )}
        >
          {row.original.active ? "ACTIVE" : "INACTIVE"}
          <Popover>
            <PopoverTrigger>
              <Info
                size={14}
                className="transition-all duration-300 hover:scale-125 hover:cursor-pointer hover:text-secondary-2-750"
              />
            </PopoverTrigger>
            <PopoverContent className="w-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Updated by</TableHead>
                    <TableHead>Remarks</TableHead>
                    <TableHead>Updated at</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">
                      {row.original.statusUpdatedBy ?? "N/A"}
                    </TableCell>
                    <TableCell>
                      {row.original.statusUpdateRemarks ?? "N/A"}
                    </TableCell>
                    <TableCell>
                      {row.original.statusUpdatedAt
                        ? format(new Date(row.original.statusUpdatedAt), "PPpp")
                        : "N/A"}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </PopoverContent>
          </Popover>
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => (
        <div className="lowercase">{row.getValue("email")}</div>
      ),
    },
    {
      accessorKey: "emailVerified",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Email Verified" />
      ),
      cell: ({ row }) => (
        <div>
          {row.getValue("emailVerified")
            ? format(new Date(row.getValue("emailVerified")), "PPp")
            : "Not Verified"}
        </div>
      ),
    },
    {
      accessorKey: "phoneNumber",
      header: "Phone",
    },
    {
      accessorKey: "reraNumber",
      header: "RERA",
      cell: ({ row }) => (
        <div>{row.getValue("reraNumber") ?? "NOT PROVIDED"}</div>
      ),
    },
    {
      accessorKey: "gstNumber",
      header: "GST",
      cell: ({ row }) => (
        <div>{row.getValue("gstNumber") ?? "NOT PROVIDED"}</div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created On" />
      ),
      cell: ({ row }) => (
        <div className="capitalize">
          {format(new Date(row.getValue("createdAt")), "PPp")}
        </div>
      ),
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Updated On" />
      ),
      cell: ({ row }) => (
        <div className="capitalize">
          {format(new Date(row.getValue("updatedAt")), "PPp")}
        </div>
      ),
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const partner = row.original;
        const isPartnerActive = partner.active;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild className="relative">
              <TableHeading className="relative" rightIcon={<MoreHorizontal />}>
                <span className="sr-only">Open menu</span>
              </TableHeading>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={async () => {
                  await navigator.clipboard.writeText(partner.id);
                  toast.success("Partner ID copied to your clipboard");
                }}
                className="cursor-pointer"
              >
                <Copy className="mr-2 size-4" />
                Copy Partner ID
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {isPartnerActive ? (
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={(e) => e.stopPropagation()}
                  asChild
                >
                  <StatusRejectionDialog partnerId={partner.id} />
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  onClick={() => {
                    if (
                      confirm("Are you sure you want to approve this partner?")
                    ) {
                      approvePartner({ partnerId: partner.id });
                    }
                  }}
                  className="cursor-pointer"
                >
                  <CheckCheck className="mr-2 size-4 text-green-500" />
                  Approve Partner
                </DropdownMenuItem>
              )}

              <DropdownMenuItem
                onClick={() => openSheet(partner.id)}
                className="cursor-pointer"
              >
                <Edit className="mr-2 size-4" />
                Update Partner
              </DropdownMenuItem>

              <DropdownMenuItem className="cursor-pointer">
                <WrappedAlert
                  trigger={
                    <button
                      className="flex items-center"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Trash className="mr-2 size-4" />
                      Delete Partner
                    </button>
                  }
                  title="Confirm deletion"
                  description="Are you sure you want to delete this partner? This action cannot be undone."
                  onConfirm={() => deletePartner({ id: partner.id })}
                  confirmText="Delete Partner"
                  cancelText="Cancel"
                />
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => handlePostProperty(partner.id)}
                className="cursor-pointer"
              >
                <LandPlot className="mr-2 size-4" />
                Post a Property
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      rowSelection,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    onRowSelectionChange: setRowSelection,
    getPaginationRowModel: getPaginationRowModel(),
  });

  return (
    <>
      <div className="mt-4 flex flex-wrap items-center justify-between gap-4 py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Search className="h-4 w-4" />
          <input
            placeholder="Search by name, email, phone..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="ml-auto flex items-center gap-2"
            >
              <Columns2 className="h-4 w-4" />
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-center">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <div className="mx-4 my-3 flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="mr-2 size-4" />
              Previous
            </Button>
            <Button
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
              <ChevronRight className="ml-2 size-4" />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
