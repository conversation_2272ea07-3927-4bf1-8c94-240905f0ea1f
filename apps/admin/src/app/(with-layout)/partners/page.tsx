import { api, HydrateClient } from "~/trpc/server";
import { PartnerDataTable } from "./partner-data-table";
import PartnerFormSheet from "./partner-form-sheet";
import PostPropertyFormSheet from "./post-property/post-property-form-sheet";
import CardViewPartners from "./card-view-partners";
import PaginationButtons from "./pagination-buttons";

type SearchParams = Promise<{
  partner_id?: string;
  status?: string;
  view?: "cards" | "table";
  page?: number;
  take?: number;
}>;

const PartnersPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;

  const status = searchParams.status ?? "active";
  const isActive = status === "active";
  const view = searchParams.view ?? "table";
  const currentPage = Number(searchParams.page ?? 1);

  const partners = await api.partner.getPartners({
    active: isActive ? "active" : "inactive",
    page: view === "cards" ? currentPage : undefined,
    take: view === "cards" ? 9 : undefined,
  });

  if (searchParams.partner_id) {
    await api.partner.getPartnerById.prefetch({
      id: searchParams.partner_id,
    });
  }

  await api.partner.getCities.prefetch();

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        {isActive ? "Active" : "Inactive"} Partners
      </h1>

      <div className="flex items-center justify-between gap-x-4">
        <PartnerFormSheet
          activeStatus={isActive ? "active" : "inactive"}
          view={view}
        />
        {view === "cards" && (
          <PaginationButtons
            currentPage={currentPage}
            totalPages={partners.totalPages}
            status={status}
            view={view}
          />
        )}
      </div>

      {view === "cards" ? (
        <CardViewPartners partners={partners.partners} />
      ) : (
        <PartnerDataTable data={partners.partners} />
      )}

      <PostPropertyFormSheet />
    </HydrateClient>
  );
};

export default PartnersPage;
