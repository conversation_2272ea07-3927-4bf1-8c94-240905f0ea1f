"use client";

import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import { Ban } from "lucide-react";
import { api } from "~/trpc/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/ui/default-dialog";

const formSchema = z.object({
  remarks: z
    .string()
    .min(2, {
      message: "Remarks must be at least 2 characters.",
    })
    .max(50, {
      message: "Remarks must not exceed 50 characters.",
    }),
});

interface StatusRejectionDialogProps {
  partnerId: string;
  onSuccess?: () => void;
}

const StatusRejectionDialog = ({
  partnerId,
  onSuccess,
}: StatusRejectionDialogProps) => {
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      remarks: "",
    },
  });

  const trpcUtils = api.useUtils();

  const { mutate: rejectPartner, isPending } =
    api.partner.rejectPartner.useMutation({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.partner.getPartners.invalidate();
        onSuccess?.();
        form.reset();
        setOpen(false);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });

  function onSubmit(values: z.infer<typeof formSchema>) {
    rejectPartner({
      partnerId: partnerId,
      remarks: values.remarks,
    });
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button
          onClick={(e) => e.stopPropagation()}
          className="flex cursor-pointer items-center justify-start gap-2 rounded-full px-2 py-1.5 text-sm"
        >
          <Ban className="size-4" />
          <p>Reject Partner</p>
        </button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reject Partner</DialogTitle>
          <DialogDescription>
            Please provide a reason for rejecting this partner.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="remarks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason for rejecting</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Type reason here..."
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormDescription>
                    This information will be visible to the partner.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  setOpen(false);
                }}
                type="button"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Rejecting..." : "Submit"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default StatusRejectionDialog;
