"use client";

import { List, LayoutGrid } from "lucide-react";
import { Button } from "@repo/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { PartnerViewParamName } from "~/app/helpers/constants";

interface ViewSelectionDropdownProps {
  view: "cards" | "table";
}

export const ViewSelectionDropdown = ({ view }: ViewSelectionDropdownProps) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const views = [
    {
      value: "table",
      label: "Table View",
      icon: <List className="mr-2 h-4 w-4" />,
    },
    {
      value: "cards",
      label: "Card View",
      icon: <LayoutGrid className="mr-2 h-4 w-4" />,
    },
  ];

  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);
    return params.toString();
  };

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            {view === "table" ? (
              <List className="h-4 w-4" />
            ) : (
              <LayoutGrid className="h-4 w-4" />
            )}
            {view === "table" ? "Table View" : "Card View"}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {views.map((viewOption) => (
            <Link
              key={viewOption.value}
              href={`${pathname}?${createQueryString(PartnerViewParamName, viewOption.value)}`}
              className="no-underline"
            >
              <DropdownMenuItem className="cursor-pointer">
                <div className="flex items-center">
                  {viewOption.icon}
                  {viewOption.label}
                </div>
              </DropdownMenuItem>
            </Link>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
