"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { PartnerParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import { Plus } from "lucide-react";
import PartnerAddUpdateForm from "./partner-add-update-form";
import { PartnerStatusTabs } from "./partner-status-tabs";
import { ViewSelectionDropdown } from "./view-selection-dropdown";

type PartnerFormSheetProps = {
  activeStatus: "active" | "inactive";
  view: "cards" | "table";
};

const PartnerFormSheet = ({ activeStatus, view }: PartnerFormSheetProps) => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(PartnerParamName);

  return (
    <>
      <div className="flex flex-row flex-wrap items-center gap-x-4">
        <Button onClick={() => openSheet()}>
          <Plus className="mr-2 size-4" /> Add Partner
        </Button>

        <PartnerStatusTabs activeStatus={activeStatus} />
        <ViewSelectionDropdown view={view} />
      </div>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Partner" : "Add Partner"}
      >
        {isOpen && <PartnerAddUpdateForm />}
      </GenericSheet>
    </>
  );
};

export default PartnerFormSheet;
