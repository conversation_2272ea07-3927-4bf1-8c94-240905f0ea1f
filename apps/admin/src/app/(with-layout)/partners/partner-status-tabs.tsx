"use client";

import { CheckCircle2, XCircle } from "lucide-react";
import { Button } from "@repo/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { PartnerStatusParamName } from "~/app/helpers/constants";

interface PartnerStatusTabsProps {
  activeStatus: "active" | "inactive";
}

export const PartnerStatusTabs = ({ activeStatus }: PartnerStatusTabsProps) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const statuses = [
    {
      value: "active",
      label: "Active Partners",
      icon: <CheckCircle2 className="mr-2 h-4 w-4" />,
    },
    {
      value: "inactive",
      label: "Inactive Partners",
      icon: <XCircle className="mr-2 h-4 w-4" />,
    },
  ];

  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);
    return params.toString();
  };

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            {activeStatus === "active" ? (
              <CheckCircle2 className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            {activeStatus === "active"
              ? "Active Partners"
              : "Inactive Partners"}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {statuses.map((status) => (
            <Link
              key={status.value}
              href={`${pathname}?${createQueryString(PartnerStatusParamName, status.value)}`}
              className="no-underline"
            >
              <DropdownMenuItem className="cursor-pointer">
                <div className="flex items-center">
                  {status.icon}
                  {status.label}
                </div>
              </DropdownMenuItem>
            </Link>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
