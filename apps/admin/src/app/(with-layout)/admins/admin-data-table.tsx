"use client";

import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import type {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import React, { useState } from "react";
import { Button } from "@repo/ui/components/ui/button";
import {
  ChevronLeft,
  ChevronRight,
  Columns2,
  Copy,
  Filter,
  MoreHorizontal,
  SquarePen,
  Trash,
  FolderUp,
} from "lucide-react";
import { Checkbox } from "@repo/ui/components/ui/checkbox";
import { AdminParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { toast } from "@repo/ui/components/ui/sonner";
import TableHeading from "~/app/components/shared/table-heading";
import type { TAdminUser } from "~/app/types";
import type { IAdminCsv } from "~/app/types/admin";
import WrappedAlert from "~/app/components/shared/wrapped-alert";
import SortableColumnHeader from "~/app/components/shared/sortable-column-header";
import exportJsonToCsv from "~/app/helpers/export-json-csv";
import { cn } from "@repo/ui/lib/utils";

export const adminColumns: ColumnDef<TAdminUser>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: "id",
    header: () => <button className="flex justify-start">Admin Id</button>,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Name" />
    ),
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => <div className="lowercase">{row.getValue("email")}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "active",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Active" />
    ),
    filterFn: (row, _columnId, filterValue: string) => {
      if (!filterValue) return true;
      const value = row.getValue("active") ? "yes" : "no";
      return value.includes(filterValue.toLowerCase());
    },
    cell: ({ row }) => (row.getValue("active") ? "Yes" : "No"),
  },
  {
    accessorKey: "userType",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="User Type" />
    ),
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Created On" />
    ),
    cell: ({ row }) => (
      <div className="capitalize">
        {new Date(row.getValue("createdAt")).toDateString()}
      </div>
    ),
  },
  {
    accessorKey: "updatedAt",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Updated On" />
    ),
    cell: ({ row }) => (
      <div className="capitalize">
        {new Date(row.getValue("updatedAt")).toDateString()}
      </div>
    ),
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      return <AdminActionCell admin={row.original} />;
    },
  },
];

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function AdminDataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,

    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      rowSelection,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    onRowSelectionChange: setRowSelection,
    getPaginationRowModel: getPaginationRowModel(),
  });

  const exportCsv = () => {
    const mainData: IAdminCsv[] = [];

    // Cast the generic data to TAdminUser[] for the export functionality
    (data as TAdminUser[]).map((details) =>
      mainData.push({
        "Admin Id": details.id,
        Name: details.name,
        Email: details.email,
        Active: details.active ? "Yes" : "No",
        "Account Created On": details.createdAt,
        "Account Updated On": details.updatedAt,
      }),
    );

    const result: boolean = exportJsonToCsv({
      data: mainData,
      fileName: "mydeer-admins-data",
    });

    if (!result) {
      toast.error("Unable to export data !");
      return;
    }

    toast.success("CSV file exported successfully !");
  };

  return (
    <div>
      <div className="flex flex-wrap items-center justify-between gap-4 py-4">
        <div className="flex h-10 w-full max-w-sm items-center gap-2 rounded-md border px-3 text-sm">
          <Filter className="h-4 w-4" />
          <input
            placeholder="Filter emails..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="w-full rounded-md border-input bg-background py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="ml-auto flex items-center gap-2"
            >
              <Columns2 className="h-4 w-4" />
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
        <Button onClick={exportCsv}>
          <FolderUp className="mr-2 size-5" />
          Export CSV
        </Button>
      </div>
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-center">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        <div className="mx-4 my-3 flex items-center justify-between text-sm text-muted-foreground">
          <div>
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="size-5" />
              Previous
            </Button>
            <Button
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
              <ChevronRight className="size-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

const AdminActionCell = ({ admin }: { admin: TAdminUser }) => {
  const router = useRouter();
  const { openSheet } = useSheet(AdminParamName);

  const { mutate: deleteAdmin, isPending } =
    api.admin.deleteAdmin.useMutation();

  const handleAdminDelete = () => {
    deleteAdmin(
      { id: admin.id },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          router.refresh();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <TableHeading rightIcon={<MoreHorizontal />}>
            <span className="sr-only">Open menu</span>
          </TableHeading>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              await navigator.clipboard.writeText(admin.id);
              toast.success("Admin ID copied to your clipboard");
            }}
            className="cursor-pointer"
          >
            <Copy className="mr-2 size-4" />
            Copy Admin Id
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => openSheet(admin.id)}
            className="cursor-pointer"
          >
            <SquarePen className="mr-2 size-4" />
            Update Admin
          </DropdownMenuItem>
          <DropdownMenuItem disabled={isPending} className="cursor-pointer">
            <WrappedAlert
              trigger={
                <button
                  className="flex items-center"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Trash className="mr-2 size-4" />
                  {isPending ? "Deleting..." : "Delete Admin"}
                </button>
              }
              title="Confirm deletion"
              description="Are you sure you want to delete this admin? This action cannot be undone."
              onConfirm={handleAdminDelete}
              confirmText="Confirm Deletion"
              cancelText="Cancel"
            />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
