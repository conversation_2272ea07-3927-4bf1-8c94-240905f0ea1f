import { api, HydrateClient } from "~/trpc/server";
import AdminFormSheet from "./admin-form-sheet";
import { adminColumns, AdminDataTable } from "./admin-data-table";

type SearchParams = Promise<{ admin_id?: string }>;

const AdminsPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;
  const admins = await api.admin.getAllAdmins();

  if (searchParams.admin_id) {
    await api.admin.getAdminById.prefetch({ id: searchParams.admin_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">Admins</h1>

      <AdminFormSheet />
      <AdminDataTable data={admins} columns={adminColumns} />
    </HydrateClient>
  );
};

export default AdminsPage;
