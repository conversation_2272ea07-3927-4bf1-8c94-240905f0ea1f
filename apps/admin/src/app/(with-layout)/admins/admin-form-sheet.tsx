"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { AdminParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import AdminAddUpdateForm from "./admin-add-update-form";
import { Plus } from "lucide-react";

const AdminFormSheet = () => {
  const { isOpen, paramValue, closeSheet, openSheet } =
    useSheet(AdminParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <Plus className="mr-2 size-4" /> Add Admin
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Admin" : "Add Admin"}
      >
        {isOpen && <AdminAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default AdminFormSheet;
