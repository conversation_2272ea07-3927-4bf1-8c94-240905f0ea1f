import React from "react";

import UserSelection from "./user-selection";
import { api, HydrateClient } from "~/trpc/server";
import PostsGrid from "./posts-grid";

type SearchParams = Promise<{ user_id?: string }>;

const Posts = async (params: { searchParams: SearchParams }) => {
  const searchParams = await params.searchParams;
  const partners = await api.partner.getAllPartners();

  if (searchParams.user_id) {
    await api.post.getPostsByUserId.prefetch({ userId: searchParams.user_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">Posts</h1>

      <UserSelection partners={partners} />

      <PostsGrid userId={searchParams.user_id} />
    </HydrateClient>
  );
};

export default Posts;
