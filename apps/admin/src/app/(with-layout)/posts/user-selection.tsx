"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { PostUserIdParamName } from "~/app/helpers/constants";

type UserSelectionProps = {
  partners: { id: string; name: string }[];
};

const UserSelection = ({ partners }: UserSelectionProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const selectedUserId = searchParams.get(PostUserIdParamName);

  const handlePatnerSelection = (userId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(PostUserIdParamName, userId);
    router.push(`?${params.toString()}`, { scroll: false });
  };

  return (
    <div className="space-y-5">
      <h2>Select user whose post you want to view</h2>
      <Select
        value={selectedUserId ?? ""}
        onValueChange={(e) => {
          handlePatnerSelection(e);
        }}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select a user" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {partners.map((partner) => (
              <SelectItem key={partner.id} value={partner.id}>
                {partner.name}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default UserSelection;
