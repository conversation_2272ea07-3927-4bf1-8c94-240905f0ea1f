import { api } from "~/trpc/server";
import {
  DeleteAccountDataTable,
  deleteAccountRequestsColumns,
} from "./delete-account-data-table";

const DeleteAccount = async () => {
  const deleteAccountRequests = await api.deleteAccounts.getDeleteAccountReq();

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Delete Accounts
      </h1>

      <DeleteAccountDataTable
        columns={deleteAccountRequestsColumns}
        data={deleteAccountRequests}
      />
    </>
  );
};

export default DeleteAccount;
