"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import AddUpdateFaqSchema from "~/server/api/validations/add-update-faq.validations";
import { api } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { skipToken } from "@tanstack/react-query";
import { useSheet } from "~/app/hooks/use-sheet";
import {
  FaqPageParamName,
  FaqParamName,
  FaqProjectParamName,
} from "~/app/helpers/constants";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { FaqPagesEnum, ProjectEnum } from "@repo/database";
import { useSearchParams } from "next/navigation";

const FaqAddUpdateForm = () => {
  const searchParams = useSearchParams();

  // => Why i am setting default values ?
  // Pre-populate form fields with URL parameters when available to maintain context
  // This eliminates the need for users to repeatedly select the same project/page values when working within a specific section, enhancing workflow efficiency
  const page = (searchParams.get(FaqPageParamName) ??
    FaqPagesEnum.HELP_CENTER_PAGE) as FaqPagesEnum;
  const project = (searchParams.get(FaqProjectParamName) ??
    ProjectEnum.B2B_DEER_CONNECT) as ProjectEnum;

  const { paramValue: faqId, closeSheet } = useSheet(FaqParamName);

  const {
    data: faqDetail,
    isLoading,
    error,
  } = api.faq.getFaqById.useQuery(faqId ? { id: faqId } : skipToken, {
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const { mutate: updateFaq, isPending: isPendingUpdateMutation } =
    api.faq.updateFaq.useMutation();
  const { mutate: addFaq, isPending: isPendingAddMutation } =
    api.faq.addFaq.useMutation();
  const trpcUtils = api.useUtils();

  const form = useForm<z.infer<typeof AddUpdateFaqSchema>>({
    resolver: zodResolver(AddUpdateFaqSchema),
    defaultValues: faqDetail
      ? {
          question: faqDetail.question,
          answer: faqDetail.answer,
          order: faqDetail.order,
          page: faqDetail.page ?? page,
          project: faqDetail.project ?? project,
        }
      : {
          question: "",
          answer: "",
          order: undefined,
          page: page,
          project: project,
        },
  });

  const onSubmit = (values: z.infer<typeof AddUpdateFaqSchema>) => {
    console.log("Form submitted with values:", values);

    if (faqId) {
      updateFaq(
        { ...values, id: faqId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void trpcUtils.faq.getFaqById.invalidate({ id: faqId });
            void trpcUtils.faq.getAllFaq.invalidate();
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addFaq(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void trpcUtils.faq.getAllFaq.invalidate();
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && faqId) {
    return <div className="p-4 text-center">Loading FAQ details...</div>;
  }

  if (error && faqId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading FAQ: {error.message}
      </div>
    );
  }

  const selectedProject = form.watch("project");

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="question"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Question</FormLabel>
              <FormControl>
                <Input placeholder="Type here..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="answer"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Answer</FormLabel>
              <FormControl>
                <Input placeholder="Type here..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="project"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your project" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(ProjectEnum).map((item) => (
                      <SelectItem key={item} value={item}>
                        {item.replaceAll("_", " ").toLowerCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="page"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Page</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select page" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(FaqPagesEnum).map((item) => {
                      // Filter out CAREER_PAGE for B2C_MY_DEER project because it is not applicable
                      if (
                        selectedProject === "B2C_MY_DEER" &&
                        (item === "CAREER_PAGE" || item === "CONTACT_US_PAGE")
                      )
                        return null;

                      return (
                        <SelectItem key={item} value={item}>
                          {item.replaceAll("_", " ").toLowerCase()}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="order"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Order</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Type here..."
                  {...field}
                  onChange={(e) => {
                    const value =
                      e.target.value === ""
                        ? undefined
                        : parseInt(e.target.value);
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {faqId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          faqId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!faqId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !faqId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default FaqAddUpdateForm;
