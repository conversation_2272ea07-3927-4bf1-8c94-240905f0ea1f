"use client";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { FaqPageParamName, FaqProjectParamName } from "~/app/helpers/constants";
import { FaqPagesEnum, ProjectEnum } from "@repo/database";
import { cn } from "@repo/ui/lib/utils";

interface ProjectSelectionDropdownProps {
  project: ProjectEnum;
}

export const ProjectSelectionDropdown = ({
  project,
}: ProjectSelectionDropdownProps) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);
    params.set(FaqPageParamName, FaqPagesEnum.HELP_CENTER_PAGE); // adding this becoz some of the pages are disabled in my-deer so we need to reset the page to default
    return params.toString();
  };

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 capitalize"
          >
            {project.replaceAll("_", " ").toLowerCase()}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {Object.values(ProjectEnum).map((item) => (
            <Link
              key={item}
              href={`${pathname}?${createQueryString(FaqProjectParamName, item)}`}
              className="no-underline"
            >
              <DropdownMenuItem
                className={cn(
                  "cursor-pointer capitalize",
                  project === item && "bg-secondary-2-100",
                )}
              >
                <div className="flex items-center">
                  {item.replaceAll("_", " ").toLowerCase()}
                </div>
              </DropdownMenuItem>
            </Link>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
