"use client";

import React from "react";
import { But<PERSON> } from "@repo/ui/components/ui/button";
import { Plus } from "lucide-react";
import { FaqParamName } from "~/app/helpers/constants";
import { useSheet } from "~/app/hooks/use-sheet";
import { GenericSheet } from "~/app/components/shared/generic-sheet";
import FaqAddUpdateForm from "./faq-add-update-form";
import { ProjectSelectionDropdown } from "./project-selection-dropdown";
import type { FaqPagesEnum, ProjectEnum } from "@repo/database";
import { PageSelectionDropdown } from "./page-selection-dropdown";

const FaqFormSheet = ({
  page,
  project,
}: {
  page: FaqPagesEnum;
  project: ProjectEnum;
}) => {
  const { isOpen, paramValue, closeSheet, openSheet } = useSheet(FaqParamName);

  return (
    <div>
      <div className="flex gap-2">
        <Button onClick={() => openSheet()}>
          <Plus className="mr-2 size-4" /> Add FAQ
        </Button>

        <ProjectSelectionDropdown project={project} />
        <PageSelectionDropdown page={page} project={project} />
      </div>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit FAQ" : "Add FAQ"}
      >
        {isOpen && <FaqAddUpdateForm />}
      </GenericSheet>
    </div>
  );
};

export default FaqFormSheet;
