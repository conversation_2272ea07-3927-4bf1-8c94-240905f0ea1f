"use client";

import { CheckCircle2, XCircle } from "lucide-react";
import { Button } from "@repo/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/ui/dropdown-menu";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { FaqPageParamName } from "~/app/helpers/constants";
import { FaqPagesEnum } from "@repo/database";
import type { ProjectEnum } from "@repo/database";
import { cn } from "@repo/ui/lib/utils";

interface PageSelectionDropdownProps {
  page: FaqPagesEnum;
  project: ProjectEnum;
}

export const PageSelectionDropdown = ({
  page,
  project,
}: PageSelectionDropdownProps) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);
    return params.toString();
  };

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 capitalize"
          >
            {page.replaceAll("_", " ").toLowerCase()}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {Object.values(FaqPagesEnum).map((item) => {
            if (
              project === "B2C_MY_DEER" &&
              (item === "CAREER_PAGE" || item === "CONTACT_US_PAGE")
            )
              return null;

            return (
              <Link
                key={item}
                href={`${pathname}?${createQueryString(FaqPageParamName, item)}`}
                className="no-underline"
              >
                <DropdownMenuItem
                  className={cn(
                    "cursor-pointer capitalize",
                    page === item && "bg-secondary-2-100",
                  )}
                >
                  <div className="flex items-center">
                    {item.replaceAll("_", " ").toLowerCase()}
                  </div>
                </DropdownMenuItem>
              </Link>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
