import { api, HydrateClient } from "~/trpc/server";
import { faqDataTableColumns, FaqDataTable } from "./faq-data-table";
import FaqFormSheet from "./faq-form-sheet";
import { FaqPagesEnum, ProjectEnum } from "@repo/database";

type SearchParams = Promise<{
  faq_id?: string;
  page?: string;
  project?: string;
}>;

const FaqPage = async (props: { searchParams: SearchParams }) => {
  const searchParams = await props.searchParams;

  const project = (searchParams.project ??
    ProjectEnum.B2B_DEER_CONNECT) as ProjectEnum;
  const page = (searchParams.page ??
    FaqPagesEnum.HELP_CENTER_PAGE) as FaqPagesEnum;

  const faqs = await api.faq.getAllFaq({
    page: page,
    project: project,
  });

  if (searchParams.faq_id) {
    await api.faq.getFaqById.prefetch({ id: searchParams.faq_id });
  }

  return (
    <HydrateClient>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">FAQs</h1>

      <FaqFormSheet project={project} page={page} />
      <FaqDataTable data={faqs} columns={faqDataTableColumns} />
    </HydrateClient>
  );
};

export default FaqPage;
