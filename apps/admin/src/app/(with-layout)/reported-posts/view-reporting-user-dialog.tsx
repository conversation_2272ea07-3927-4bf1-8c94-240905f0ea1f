import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@repo/ui/components/ui/default-dialog";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/ui/table";
import { Button } from "@repo/ui/components/ui/button";
import type { Prisma } from "@repo/database";

type Item = Prisma.PostGetPayload<{
  select: {
    id: true;
    content: true;
    totalComments: true;
    totalLikes: true;
    reportCount: true;
    createdAt: true;
    updatedAt: true;
    deletedAt: true;
    media: {
      select: {
        filePublicUrl: true;
        mediaType: true;
      };
    };
    user: {
      select: {
        id: true;
        filePublicUrl: true;
        name: true;
        company: {
          select: {
            companyName: true;
          };
        };
      };
    };
    comments: {
      select: {
        comment: true;
        isPinned: true;
        createdAt: true;
        user: {
          select: {
            id: true;
            name: true;
            fileP<PERSON>licUrl: true;
            companyDetails: {
              select: {
                companyName: true;
              };
            };
          };
        };
        customer: {
          select: {
            id: true;
            name: true;
            profileImagePublicUrl: true;
          };
        };
      };
    };
    likes: {
      select: {
        id: true;
        postId: true;
      };
      take: 1;
    };
    reportedPost: {
      select: {
        reason: true;
        createdAt: true;
        updatedAt: true;
        user: {
          select: {
            id: true;
            filePublicUrl: true;
            name: true;
            company: {
              select: {
                companyName: true;
              };
            };
          };
        };
      };
    };
  };
}>;

const ViewReportingUserDialog = ({ item }: { item: Item }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">View Reporting Users</Button>
      </DialogTrigger>
      <DialogContent className="max-h-[500px] overflow-auto sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Reasons for reporting Post</DialogTitle>
        </DialogHeader>
        <Table>
          <TableCaption>List of users who reported this post</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="">User</TableHead>
              <TableHead>Reason</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {item.reportedPost.map((item) => {
              return (
                <>
                  <TableRow>
                    <TableCell className="font-medium">
                      {item.user?.name}
                    </TableCell>
                    <TableCell>{item.reason}</TableCell>
                  </TableRow>
                </>
              );
            })}
          </TableBody>
        </Table>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="submit">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewReportingUserDialog;
