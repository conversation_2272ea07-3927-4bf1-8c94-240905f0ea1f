//  @ts-nocheck

import { PrismaAdapter } from "@auth/prisma-adapter";
import NextAuth, { DefaultSession, NextAuthConfig } from "next-auth";
import "next-auth/jwt";
import type { Adapter } from "next-auth/adapters";
import Credentials from "next-auth/providers/credentials";

import authenticate from "./api/helpers/authenticate";
import type { AdminUserTypeEnum } from "@repo/database";
import { db } from "./db";
import { env } from "~/env";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role: AdminUserTypeEnum;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: AdminUserTypeEnum;
  }
}

export const authOptions: NextAuthConfig = {
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/login",
  },
  callbacks: {
    jwt({ token, user }) {
      if (user) {
        // This will only be executed at sign in
        token.id = user.id ?? "";
        token.role = user.userType;
      }
      return token;
    },
    session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.role = token.role;
      }
      return session;
    },
  },
  adapter: PrismaAdapter(db) as Adapter,
  secret: env.NEXTAUTH_SECRET,
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        email: {
          label: "Email",
          type: "text",
          placeholder: "Email",
        },
        password: {
          label: "Password",
          type: "text",
          placeholder: "Password",
        },
      },
      async authorize(credentials) {
        if (!credentials.email || !credentials.password) {
          return null;
        }

        // const { email, password } = credentials;
        const user = await authenticate({
          email: credentials.email as string,
          password: credentials.password as string,
        });

        // Make sure the authenticate function returns the userType
        return user;
      },
    }),
  ],
};

export const { auth, handlers, signIn, signOut } = NextAuth(authOptions);
