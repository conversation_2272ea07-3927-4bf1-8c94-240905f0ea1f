import crypto from "crypto";

export default function generateAlphanumericString(): string {
  const charset =
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  const charsetLength = charset.length;
  const length = 9;

  const buffer: Buffer = crypto.randomBytes(length * 2);

  let result = "";

  for (let i = 0; i < buffer.length && result.length < length; i++) {
    const randomByte: number = buffer.readUInt8(i);

    
    if (randomByte < charsetLength * Math.floor(256 / charsetLength)) {
      result += charset[randomByte % charsetLength];
    }
  }

  // In the extremely unlikely case we didn't get enough characters
  if (result.length < length) {
    throw new Error("Failed to generate alphanumeric string");
  }

  return result;
}
