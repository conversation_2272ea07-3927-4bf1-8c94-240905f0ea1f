export type DataRow = Record<string, string>;

export const convertData = (data: string[][]): DataRow[] => {
  if (data.length < 2) {
    throw new Error("Insufficient data to convert");
  }

  const keys = data[0]!;

  const result: DataRow[] = data.slice(1).map((row) => {
    if (row.length !== keys.length) {
      throw new Error("Row length does not match keys length");
    }

    const obj: DataRow = {};
    row.forEach((value, index) => {
      obj[keys[index]!] = value;
    });

    return obj;
  });

  return result;
};
