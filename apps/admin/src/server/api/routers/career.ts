import { z } from "zod";
import { createTRPCRouter, adminProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";

const careerRouter = createTRPCRouter({
  getAllApplications: adminProcedure.query(({ ctx }) => {
    return ctx.db.partnerResume.findMany({
      include: { jobRole: true },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),
  removeResume: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        await ctx.db.partnerResume.delete({
          where: {
            id: input.id,
          },
        });
        return {
          message: "Resume deleted successfully",
        };
      } catch (error) {
        console.log(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete resume",
        });
      }
    }),
});

export default careerRouter;
