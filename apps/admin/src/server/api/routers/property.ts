import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";
import AddUpdatePropertyTypeSchema from "../validations/add-update-property-type.validation";
import { PropertyStatusEnum } from "@repo/database";
import { env } from "~/env";
import axios from "axios";
import {
  PostPropertyStep1BaseSchema,
  PostPropertyStep2BaseSchema,
  PostPropertyStep3BaseSchema,
  PostPropertyStep4Schema,
} from "../validations/post-property.validation";
import type { MeiliSearchError } from "~/app/types";

import { v2 as cloudinary } from "cloudinary";

const propertyRouter = createTRPCRouter({
  getAllProperties: protectedProcedure
    .input(
      z.object({
        propertyStatus: z.nativeEnum(PropertyStatusEnum).optional(),
        page: z.number().optional().default(1),
        take: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { take, page } = input;
      // Get total count for pagination
      const totalProperties = await ctx.db.property.count({
        where: { propertyStatus: input?.propertyStatus },
      });

      const properties = await ctx.db.property.findMany({
        ...(take && { take: take }),
        ...(page && take && { skip: (page - 1) * take }),
        where: { propertyStatus: input?.propertyStatus },
        include: {
          areaUnit: true,
          propertyType: true,
          PropertyCategory: true,
          utilities: true,
          amenities: true,
          user: {
            include: {
              company: true,
            },
          },
          comments: {
            include: {
              user: true,
            },
          },

          mediaSections: {
            include: {
              media: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return {
        properties,
        totalPages: Math.ceil(totalProperties / (input?.take ?? 9)),
        totalResults: totalProperties,
      };
    }),
  getPropertyDetails: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { id } = input;
      return await ctx.db.property.findFirst({
        include: {
          areaUnit: true,
          utilities: true,
          amenities: true,
          user: {
            include: {
              company: true,
              companyDetails: true,
            },
          },
          comments: {
            include: {
              user: true,
            },
          },
          mediaSections: {
            include: {
              media: {
                select: {
                  id: true,
                  fileKey: true,
                  filePublicUrl: true,
                  cloudinaryId: true,
                  cloudinaryUrl: true,
                  mediaType: true,
                },
              },
            },
          },
          PropertyCategory: true,
        },
        where: {
          id: id,
        },
      });
    }),
  updatePropertyStatus: protectedProcedure
    .input(
      z.object({
        propertyId: z.string().nonempty("Property ID is required"),
        status: z.enum(["ACTIVE", "REJECTED", "InREVIEW", "PENDING"], {
          required_error: "Valid status is required",
        }),
        remarks: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { propertyId, status, remarks } = input;
      const userName = ctx.session.user.name;

      // Status configuration mapping
      const statusConfig = {
        ACTIVE: {
          value: "ACTIVE" as PropertyStatusEnum,
          defaultRemarks: "Property marked as active by admin",
          successMessage: "Approved Successfully.",
        },
        REJECTED: {
          value: "REJECTED" as PropertyStatusEnum,
          defaultRemarks: remarks || "Property rejected by admin",
          successMessage: "Rejected Successfully.",
        },
        InREVIEW: {
          value: "InREVIEW" as PropertyStatusEnum,
          defaultRemarks: "Property marked as under review by admin",
          successMessage: "Marked as Under Review.",
        },
        PENDING: {
          value: "PENDING" as PropertyStatusEnum,
          defaultRemarks: "Property status is pending",
          successMessage: "Marked as Pending.",
        },
      };

      const statusVal = statusConfig[status].value;
      const remarksVal =
        status === "REJECTED" ? remarks : statusConfig[status].defaultRemarks;

      try {
        // Fetch property details before making any changes
        const property = await ctx.db.property.findUnique({
          where: { id: propertyId },
          select: {
            id: true,
            propertyTitle: true,
            propertyLocation: true,
            propertyAddress: true,
            user: { select: { name: true } },
          },
        });

        if (!property) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Property not found for status update",
          });
        }

        // Step 1: Meli search: this must succeed for further updates to proceed
        if (statusVal === "ACTIVE") {
          try {
            await axios.post(
              `${env.MEILI_SEARCH_URL}/indexes/${env.PROPERTY_INDEX}/documents`,
              property,
              {
                headers: {
                  "Content-Type": "application/json",
                  "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
                  Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
                },
              },
            );
          } catch (searchError) {
            console.error("MeiliSearch indexing failed:", searchError);
            // Critical failure - propagate the error
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message:
                "Failed to index property in search engine. Status update aborted.",
            });
          }
        } else {
          // For non-active statuses, try to remove from search index
          try {
            await axios.delete(
              `${env.MEILI_SEARCH_URL}/indexes/${env.PROPERTY_INDEX}/documents/${propertyId}`,
              {
                headers: {
                  "Content-Type": "application/json",
                  "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
                  Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
                },
              },
            );
          } catch (err) {
            if (err instanceof axios.AxiosError) {
              const res = err.response?.data as MeiliSearchError;

              if (res.code === "index_not_found") {
                throw new TRPCError({
                  code: "INTERNAL_SERVER_ERROR",
                  message:
                    "Failed to update search engine. Status update aborted.",
                });
              }
            }
            // If document not found in MeiliSearch, that's okay - continue with update
          }
        }

        // Step 2: Now i can perform database updates
        const result = await ctx.db.$transaction(async (tx) => {
          const updatedProperty = await tx.property.update({
            where: { id: propertyId },
            data: {
              propertyStatus: statusVal,
              statusUpdatedAt: new Date(),
              statusUpdateRemarks: remarksVal,
              statusUpdatedBy: userName,
            },
            select: {
              id: true,
              propertyStatus: true,
              userId: true,
              propertyTitle: true,
              statusUpdateRemarks: true,
              mediaSections: {
                select: {
                  media: {
                    select: { filePublicUrl: true },
                  },
                },
              },
            },
          });

          const coverImageUrl =
            updatedProperty.mediaSections[0]?.media[0]?.filePublicUrl;

          // Create appropriate notification based on status
          if (statusVal === "ACTIVE") {
            await tx.notification.create({
              data: {
                title: "Property Active",
                description: `Your property with the title - "${updatedProperty.propertyTitle}" is now active`,
                type: "PROPERTY",
                metaData: {
                  propertyId: updatedProperty.id,
                  active: true,
                  propertyCoverImagUrl: coverImageUrl,
                },
                receiverId: updatedProperty.userId,
              },
            });
          } else if (statusVal === "REJECTED") {
            const truncatedTitle = updatedProperty.propertyTitle
              ? updatedProperty.propertyTitle.length > 12
                ? updatedProperty.propertyTitle.slice(0, 12) + "..."
                : updatedProperty.propertyTitle
              : "";

            await tx.notification.create({
              data: {
                title: "Property Rejected",
                description: `Your property with the title - "${truncatedTitle}" has been rejected due to the reason - "${updatedProperty.statusUpdateRemarks}"`,
                type: "PROPERTY",
                metaData: {
                  propertyId: updatedProperty.id,
                  reject: true,
                  propertyCoverImagUrl: coverImageUrl,
                },
                receiverId: updatedProperty.userId,
              },
            });
          } else if (statusVal === "InREVIEW") {
            await tx.notification.create({
              data: {
                title: "Property Under Review",
                description: `Your property with the title - "${updatedProperty.propertyTitle}" is under review, our team will review the property and update the status soon`,
                type: "PROPERTY",
                metaData: {
                  propertyId: updatedProperty.id,
                  inReview: true,
                  propertyCoverImagUrl: coverImageUrl,
                },
                receiverId: updatedProperty.userId,
              },
            });
          }

          return {
            message: `Property with id: ${updatedProperty.id}, ${statusConfig[status].successMessage}`,
            status: statusVal,
            property: {
              id: updatedProperty.id,
              status: updatedProperty.propertyStatus,
            },
          };
        });

        return result;
      } catch (error) {
        console.error("Error in updatePropertyStatus:", error);

        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update the property status.",
        });
      }
    }),

  getPropertyTypeByCategoryId: protectedProcedure
    .input(z.object({ categoryId: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.propertyType.findMany({
        select: {
          id: true,
          name: true,
          category: true,
        },
        where: {
          categoryId: input.categoryId,
        },
      });
    }),

  getPropertyTypeById: protectedProcedure
    .input(z.object({ propertyTypeId: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.propertyType.findUnique({
        where: {
          id: input.propertyTypeId,
        },
        include: { category: true },
      });
    }),

  getAllPropertyType: protectedProcedure.query(({ ctx }) => {
    return ctx.db.propertyType.findMany({
      include: {
        category: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),
  deletePropertyType: protectedProcedure
    .input(z.object({ propertyId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const id = input.propertyId;

      try {
        const query = await ctx.db.propertyType.delete({
          where: {
            id: id,
          },
        });

        return {
          message: `Property type: ${query.name} deleted successfully.`,
        };
      } catch (e) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to delete the property type.",
        });
      }
    }),

  addPropertyType: protectedProcedure
    .input(AddUpdatePropertyTypeSchema)
    .mutation(async ({ ctx, input }) => {
      const { name, category } = input;

      try {
        const isExisting = await ctx.db.propertyType.findFirst({
          where: {
            categoryId: { equals: category },
            name: { contains: name, mode: "insensitive" },
          },
        });
        if (isExisting) {
          return {
            message: `${isExisting.name} property type already exists.`,
            warning: true,
          };
        }
        const newPropertyType = await ctx.db.propertyType.create({
          data: {
            name: name,
            categoryId: category,
          },
        });

        return {
          message: `${newPropertyType.name} property type created successfully.`,
        };
      } catch (e) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to create new property type.",
        });
      }
    }),

  updatePropertyType: protectedProcedure
    .input(AddUpdatePropertyTypeSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { category, id, name } = input;
      try {
        const isExisting = await ctx.db.propertyType.findFirst({
          where: {
            categoryId: { equals: category },
            name: { contains: name, mode: "insensitive" },
            id: { not: id },
          },
        });
        if (isExisting) {
          return {
            message: `${isExisting.name} property type already exists.`,
            warning: true,
          };
        }
        const query = await ctx.db.propertyType.update({
          where: {
            id: id,
          },
          data: {
            name: name,
            categoryId: category,
          },
        });

        return {
          message: `${query.name} property type updated successfully.`,
        };
      } catch (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to update the property type.",
        });
      }
    }),

  getPropertyStatus: protectedProcedure.query(({ ctx }) => {
    const propertyStatusCounts = ctx.db.property.groupBy({
      by: ["propertyStatus"],
      _count: {
        propertyStatus: true,
      },
    });
    return propertyStatusCounts;
  }),

  getStatus: protectedProcedure.query(({ ctx }) => {
    return ctx.db.property.findMany({
      select: {
        propertyStatus: true,
      },
    });
  }),

  submitStep1: protectedProcedure
    .input(
      PostPropertyStep1BaseSchema.extend({
        agentId: z.string().nonempty(),
        propertyId: z.string().nullable(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Check if user exists and is active
        const user = await ctx.db.user.findUnique({
          where: {
            id: input.agentId,
          },
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Agent not found.",
          });
        }

        const areaUnit = await ctx.db.areaUnit.findUnique({
          where: {
            id: input.areaUnitId,
          },
        });

        const areaInSqMeters = areaUnit?.conversionMultiplyer
          ? input.area * areaUnit.conversionMultiplyer
          : 0;

        const propertyData = {
          userId: input.agentId,
          registeryFileKey: input.registeryFileKey,
          propertyTitle: input.propertyTitle,
          propertyFor: input.propertyFor,
          bedrooms: input.bedrooms,
          bathrooms: input.bathrooms,
          propertyPrice: input.propertyPrice,
          securityDeposit: input.securityDeposit ?? null,
          areaUnitId: input.areaUnitId,
          area: input.area,
          areaInSqMeters: areaInSqMeters,
          propertyCategoryId: input.propertyCategoryId,
          propertyTypeId: input.propertyTypeId,
          aboutProperty: input.aboutProperty ?? null,
        };

        // If propertyId exists, first verify it exists and belongs to the agent
        if (input.propertyId) {
          const existingProperty = await ctx.db.property.findFirst({
            where: {
              id: input.propertyId,
              userId: input.agentId,
            },
          });

          if (!existingProperty) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Property not found or unauthorized access",
            });
          }

          // Update existing property
          const updatedProperty = await ctx.db.property.update({
            where: {
              id: input.propertyId,
            },
            data: propertyData,
          });

          return {
            message: "Property updated successfully",
            propertyId: updatedProperty.id,
          };
        } else {
          // Only create new property if propertyId is not provided
          const newProperty = await ctx.db.property.create({
            data: propertyData,
          });

          return {
            message: "Property created successfully",
            propertyId: newProperty.id,
          };
        }
      } catch (err) {
        if (err instanceof TRPCError) throw err;

        console.error(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to submit the form",
        });
      }
    }),

  getStep1: protectedProcedure
    .input(
      z.object({ id: z.string().nonempty(), agentId: z.string().nonempty() }),
    )
    .query(async ({ ctx, input }) => {
      const { id } = input;

      const property = await ctx.db.property.findFirst({
        select: {
          id: true,
          aboutProperty: true,
          area: true,
          areaUnitId: true,
          bathrooms: true,
          bedrooms: true,
          PropertyCategory: true,
          propertyPrice: true,
          propertyTitle: true,
          propertyTypeId: true,
          securityDeposit: true,
          propertyFor: true,
          registeryFileKey: true,
          propertyCategoryId: true,
        },
        where: {
          id: id,
          userId: input.agentId,
        },
      });

      return property;
    }),

  submitStep2: protectedProcedure
    .input(
      PostPropertyStep2BaseSchema.extend({
        agentId: z.string().nonempty(),
        propertyId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const user = await ctx.db.user.findFirst({
        select: {
          id: true,
          email: true,
        },
        where: {
          id: input.agentId,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "Unauthorized",
        });
      }

      if (!input.propertyId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Property id is required",
        });
      }

      await ctx.db.utilities.deleteMany({
        where: {
          propertyId: input.propertyId,
        },
      });

      await ctx.db.property.update({
        where: {
          id: input.propertyId,
        },
        data: {
          propertyAddress: input.propertyAddress,
          propertyLatitude: Number(input.propertyLatitude),
          propertyLongitude: Number(input.propertyLongitude),
          propertyGooglePlaceId: input.propertyGooglePlaceId,
          propertyAddressComponents: input.propertyAddressComponents,
          propertyMarkersLatLng: input.propertyMarkersLatLng,
          propertyLocation: input.propertyLocation,
          ...(input.utilities && {
            utilities: {
              createMany: {
                data: input.utilities,
              },
            },
          }),
        },
      });

      return {
        message: "Step 2 completed successfully",
        propertyId: input.propertyId,
      };
    }),

  getStep2: protectedProcedure
    .input(
      z.object({ id: z.string().nonempty(), agentId: z.string().nonempty() }),
    )
    .query(async ({ input, ctx }) => {
      const { id } = input;

      const property = await ctx.db.property.findFirst({
        select: {
          id: true,
          propertyAddress: true,
          propertyLatitude: true,
          propertyLongitude: true,
          propertyGooglePlaceId: true,
          propertyAddressComponents: true,
          propertyLocation: true,
          utilities: true,
          propertyMarkersLatLng: true,
          propertyCategoryId: true,
          PropertyCategory: {
            select: {
              id: true,
              name: true,
              showPropertyAddress: true,
              showPropertyLocation: true,
              showUtilities: true,
            },
          },
        },
        where: {
          id: id,
          user: {
            id: input.agentId,
          },
        },
      });

      if (!property) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      return {
        ...property,
        utilities: property.utilities.map((u) => ({
          ...u,
          distanceInKm: u.distanceInKm.toNumber(),
        })),
      };
    }),

  submitStep3: protectedProcedure
    .input(
      PostPropertyStep3BaseSchema.extend({
        agentId: z.string().nonempty(),
        propertyId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const property = await ctx.db.property.findUnique({
        where: {
          id: input.propertyId,
        },
      });

      if (!property) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      await ctx.db.$transaction(async (tx) => {
        await tx.property.update({
          where: {
            id: input.propertyId,
          },
          data: {
            amenities: {
              set: [],
            },
          },
        });

        await tx.property.update({
          where: {
            id: input.propertyId,
          },
          data: {
            societyOrLocalityName: input.societyOrLocalityName,
            buildYear: input.buildYear,
            possessionState: input.possessionState,
            furnishing: input.furnishing,
            totalFloors: input.totalFloors,
            floorNumber: input.floorNumber,
            carParking: input.carParking,
            facing: input.facing,
            propertyState: input.propertyState,
            amenities: {
              connect: input.amenities?.map((amenity) => ({
                id: amenity.id,
              })),
            },
          },
        });
      });

      return {
        message: "Step 3 completed successfully",
        propertyId: input.propertyId,
      };
    }),

  getStep3: protectedProcedure
    .input(
      z.object({
        propertyId: z.string().nullable(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!input.propertyId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      const property = await ctx.db.property.findUnique({
        select: {
          id: true,
          propertyCategoryId: true,
          societyOrLocalityName: true,
          buildYear: true,
          possessionState: true,
          amenities: {
            select: {
              id: true,
              name: true,
            },
            orderBy: {
              name: "asc",
            },
          },
          facing: true,
          furnishing: true,
          totalFloors: true,
          floorNumber: true,
          carParking: true,
          propertyState: true,
          PropertyCategory: {
            select: {
              id: true,
              name: true,
              showSocietyName: true,
              showBuildYear: true,
              showPossessionState: true,
              showAmenities: true,
              showFurnishing: true,
              showFacing: true,
              showTotalFloors: true,
              showFloorNumber: true,
              showCarParking: true,
              showPropertyState: true,
            },
          },
        },
        where: {
          id: input.propertyId,
        },
      });

      return property;
    }),

  submitStep4: protectedProcedure
    .input(
      PostPropertyStep4Schema.extend({
        propertyId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { propertyId, mediaSections } = input;

      try {
        await ctx.db.$transaction(async (tx) => {
          // delete all existing media sections and their media
          await tx.propertyMediaSection.deleteMany({
            where: {
              propertyId: propertyId,
            },
          });

          // Create new media sections with their media
          for (const section of mediaSections) {
            const newSection = await tx.propertyMediaSection.create({
              data: {
                title: section.title,
                propertyId: propertyId,
              },
            });

            // Create media entries for this section
            if (section.media.length > 0) {
              await tx.propertyMedia.createMany({
                data: section.media.map((media) => ({
                  fileKey: media.fileKey,
                  filePublicUrl: media.filePublicUrl,
                  cloudinaryId: media.cloudinaryId,
                  cloudinaryUrl: media.cloudinaryUrl,
                  mediaType: media.mediaType,
                  propertyMediaSectionId: newSection.id,
                })),
              });
            }
          }

          // update property status
          await tx.property.update({
            where: {
              id: propertyId,
            },
            data: {
              propertyStatus: "InREVIEW",
            },
          });
        });

        return {
          message:
            "Media sections saved successfully, Your property is under review.",
          propertyId: propertyId,
        };
      } catch (error) {
        console.error("Error saving media sections:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to save media sections",
        });
      }
    }),

  getStep4: protectedProcedure
    .input(z.object({ propertyId: z.string().nonempty() }))
    .query(async ({ ctx, input }) => {
      const mediaSections = await ctx.db.propertyMediaSection.findMany({
        where: {
          propertyId: input.propertyId,
        },
        include: {
          media: {
            select: {
              id: true,
              fileKey: true,
              filePublicUrl: true,
              cloudinaryUrl: true,
              cloudinaryId: true,
              mediaType: true,
            },
          },
        },
      });

      return mediaSections;
    }),

  deletePropertyMedia: protectedProcedure
    .input(z.object({ cloudinaryId: z.string().optional() }))
    .mutation(async ({ input }) => {
      const { cloudinaryId } = input;

      try {
        if (!cloudinaryId) {
          return null;
        }

        cloudinary.config({
          api_key: env.CLOUDINARY_API_KEY,
          api_secret: env.CLOUDINARY_API_SECRET,
          cloud_name: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        });

        await cloudinary.uploader.destroy(cloudinaryId, {
          invalidate: true,
        });

        return null;
      } catch (error) {
        console.error("Error deleting media from Cloudinary:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete media from Cloudinary",
        });
      }
    }),
});

export default propertyRouter;
