import { z } from "zod";

import { createTRPCRouter, adminProcedure } from "../trpc";

export const agentReviewsRouter = createTRPCRouter({
  getAgentReviews: adminProcedure
    .input(z.object({ agentId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { agentId } = input;
      return await ctx.db.customerRatingsToAgents.findMany({
        where: {
          ratedToUserId: agentId,
          fileKey: {
            not: null,
          },
          filePublicUrl: {
            not: null,
          },
        },
        include: {
          ratedBy: true,
          connection: {
            select: {
              customer: {
                select: {
                  city: true,
                  cityId: true,
                },
              },
            },
          },
        },
      });
    }),
});
