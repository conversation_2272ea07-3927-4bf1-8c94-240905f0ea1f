import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure } from "../trpc";
import { AddUpdateCitiesSchema } from "../validations/add-update-cities.validation";
import { z } from "zod";

const cityRouter = createTRPCRouter({
  getAllCities: adminProcedure.query(async ({ ctx }) => {
    try {
      const cities = await ctx.db.city.findMany({
        orderBy: {
          createdAt: "desc",
        },
      });

      return cities;
    } catch (err) {
      console.log(err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "failed to fetch cities",
      });
    }
  }),
  getCityById: adminProcedure
    .input(z.object({ cityId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { cityId } = input;

      try {
        const city = await ctx.db.city.findUnique({
          where: {
            id: cityId,
          },
        });

        return city;
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get city",
        });
      }
    }),
  addNewCity: adminProcedure
    .input(AddUpdateCitiesSchema)
    .mutation(async ({ ctx, input }) => {
      const {
        cityName,
        cityMarkersLatLng,
        northMaxLat,
        southMaxLat,
        eastMaxLng,
        westMaxLng,
      } = input;

      try {
        const newCity = await ctx.db.city.create({
          data: {
            name: cityName,
            cityMarkersLatLng: cityMarkersLatLng,
            northMaxLat,
            southMaxLat,
            eastMaxLng,
            westMaxLng,
          },
        });

        return {
          message: `${newCity.name} Created Successfully`,
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add new city",
        });
      }
    }),
  updateCity: adminProcedure
    .input(AddUpdateCitiesSchema.extend({ cityId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const {
        cityName,
        cityMarkersLatLng,
        cityId,
        northMaxLat,
        southMaxLat,
        eastMaxLng,
        westMaxLng,
      } = input;

      try {
        const newCity = await ctx.db.city.update({
          where: {
            id: cityId,
          },
          data: {
            name: cityName,
            cityMarkersLatLng: cityMarkersLatLng,
            northMaxLat,
            southMaxLat,
            eastMaxLng,
            westMaxLng,
          },
        });

        return {
          message: `${newCity.name} Updated Successfully`,
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update city",
        });
      }
    }),
  deleteCity: adminProcedure
    .input(z.object({ cityId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { cityId } = input;

      try {
        const users = await ctx.db.city.findUnique({
          where: {
            id: cityId,
          },
          select: {
            users: {
              select: { id: true },
            },
          },
        });

        if (users?.users && users.users.length > 0) {
          return {
            warning: true,
            message: "This city cannot be deleted.",
          };
        }

        await ctx.db.city.delete({
          where: {
            id: cityId,
          },
        });

        return {
          message: "City deleted successfully.",
        };
      } catch (err) {
        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete city",
        });
      }
    }),
});

export default cityRouter;
