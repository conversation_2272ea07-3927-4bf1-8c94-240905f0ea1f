import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure } from "../trpc";

const reportedUsersFromChatRouter = createTRPCRouter({
  getReportedUsersFromChat: adminProcedure.query(async ({ ctx }) => {
    try {
      const reportedUsers = await ctx.db.reportedUserFromChat.findMany({
        select: {
          id: true,
          reportingUserId: true,
          reportingUser: {
            select: {
              name: true,
            },
          },
          reportedUserId: true,
          reportedUser: {
            select: {
              name: true,
            },
          },
          reasonForReporting: true,
          reportingCustomerId: true,
          reportingCustomer: {
            select: {
              name: true,
            },
          },
        },
      });
      return reportedUsers;
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Error fetching reported users from chat",
      });
    }
  }),
});

export default reportedUsersFromChatRouter;
