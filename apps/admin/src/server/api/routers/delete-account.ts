import { z } from "zod";
import { createTR<PERSON><PERSON>outer, adminProcedure } from "../trpc";
import { TRPCError } from "@trpc/server";
import { env } from "~/env";
import axios from "axios";

const deleteAccountRouter = createTRPCRouter({
  // partner account deletion requests
  getDeleteAccountReq: adminProcedure.query(async ({ ctx }) => {
    //retrieve the data for all delete account requests
    return await ctx.db.deleteAccount.findMany();
  }),

  deleteAccount: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;
      // Finding the delete request entry in the deleteAccount table
      try {
        const deleteRequest = await ctx.db.deleteAccount.findFirst({
          where: {
            id: id,
          },
        });

        console.log("entry from delete account table", deleteRequest);
        if (!deleteRequest) {
          console.log("no entry is found with this id in deleteAccounts table");
          return {
            error: "Can't able to find the user you want to delete.",
          };
        }

        // Finding the user details that need to be deleted
        const userToDelete = await ctx.db.user.findFirst({
          select: {
            id: true,
            companyDetails: true,
          },
          where: {
            email: deleteRequest.email,
            phoneNumber: deleteRequest.phoneNumber,
          },
        });
        if (!userToDelete) {
          return {
            error: "The user you are trying to delete does not exist.",
          };
        }

        //we need to check whether the user we are deleting is not the owner of any company as if it is then it will contains many agents inside it
        const checkCompanyHead = await ctx.db.user.findFirst({
          where: {
            companyDetails: {
              adminUserId: userToDelete.id,
            },
          },
        });
        if (checkCompanyHead) {
          return {
            error: `The user you are trying to delete is the owner of company ${userToDelete.companyDetails?.companyName}.`,
          };
        }

        // Then delete the user
        await ctx.db.user.delete({
          where: { id: userToDelete.id },
        });
        console.log("entry from user table", userToDelete);

        await ctx.db.deleteAccount.update({
          where: {
            id: deleteRequest.id,
          },
          data: {
            deletedAt: new Date(),
          },
        });

        try {
          await axios.delete(
            `${env.MEILI_SEARCH_URL}/indexes/${env.PARTNER_INDEX}/documents/${userToDelete.id}`,
            {
              headers: {
                "Content-Type": "application/json",
                "X-MEILI-API-KEY": env.MEILI_SEARCH_KEY,
                Authorization: `Bearer ${env.MEILI_SEARCH_KEY}`,
              },
            },
          );
        } catch (err) {
          console.log("error is", err);
        }

        return {
          message: "Account deleted successfully.",
        };
      } catch (err) {
        console.log("error", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete the account.",
        });
      }
    }),

  // customer account deletion requests
  getDeleteCustomerAccountReq: adminProcedure.query(async ({ ctx }) => {
    return await ctx.db.deleteCustomerAccount.findMany();
  }),
});

export default deleteAccountRouter;
