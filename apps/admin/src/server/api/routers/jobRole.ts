import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure } from "../trpc";
import { z } from "zod";
import addUpdateJobRoleSchema from "../validations/add-update-job-role.validation";
import * as Sentry from "@sentry/nextjs";

const jobRoleRouter = createTRPCRouter({
  getAllJobRoles: adminProcedure
    .input(z.object({ showDeleted: z.boolean().optional() }))
    .query(async ({ ctx, input }) => {
      try {
        const allJobRoles = await ctx.db.jobRole.findMany({
          orderBy: {
            createdAt: "desc",
          },
          where: {
            deletedAt: input.showDeleted ? { not: null } : null,
          },
        });

        console.log(allJobRoles);
        return allJobRoles;
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch job roles.",
        });
      }
    }),

  getJobRoleById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { id } = input;
      try {
        return ctx.db.jobRole.findUnique({
          where: {
            id,
          },
        });
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch job role.",
        });
      }
    }),

  addJobRole: adminProcedure
    .input(addUpdateJobRoleSchema)
    .mutation(async ({ ctx, input }) => {
      const { name } = input;
      try {
        const isExisting = await ctx.db.jobRole.findFirst({
          where: {
            name: { contains: name, mode: "insensitive" },
          },
        });
        if (isExisting) {
          return {
            message: `Job role ${isExisting.name} already exists `,
            warning: true,
          };
        }

        const newJobRole = await ctx.db.jobRole.create({
          data: {
            name,
          },
        });
        return {
          message: `Job Role ${newJobRole.name} created successfully`,
        };
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add new job role",
        });
      }
    }),

  updateJobRole: adminProcedure
    .input(addUpdateJobRoleSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { name, id } = input;
      try {
        const isExisting = await ctx.db.jobRole.findFirst({
          where: {
            name: { contains: name, mode: "insensitive" },
          },
        });
        if (isExisting) {
          return {
            message: `Job Role ${isExisting.name} already exists `,
            warning: true,
          };
        }
        const updateJobRole = await ctx.db.jobRole.update({
          where: {
            id,
          },
          data: {
            name,
          },
        });
        return { message: `${updateJobRole.name} updated successfully!` };
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update job role",
        });
      }
    }),

  removeJobRole: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;
      try {
        const jobRole = await ctx.db.jobRole.update({
          where: {
            id,
          },
          data: {
            deletedAt: new Date(),
          },
        });
        return { message: `Job role ${jobRole.name} deleted successfully!` };
      } catch (error) {
        Sentry.captureException(error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete job role",
        });
      }
    }),
});

export default jobRoleRouter;
