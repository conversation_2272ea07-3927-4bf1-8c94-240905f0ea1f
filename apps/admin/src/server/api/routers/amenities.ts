import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure } from "../trpc";
import AddUpdateAmenitySchema from "../validations/add-update-amenities.validation";
import { z } from "zod";
import * as Sentry from "@sentry/nextjs";


const amenitiesRouter = createTRPCRouter({
  getAllAmenities: adminProcedure.query(({ ctx }) => {
    return ctx.db.amenities.findMany({
      select: {
        id: true,
        name: true,
        fileKey: true,
        filePublicUrl: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }),

  getAmenityById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(({ ctx, input }) => {
      return ctx.db.amenities.findUnique({
        where: {
          id: input.id,
        },
      });
    }),

  updateAmenity: adminProcedure
    .input(AddUpdateAmenitySchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id, name, filePublicUrl, fileKey } = input;

      try {
        const query = await ctx.db.amenities.update({
          where: {
            id: id,
          },
          data: {
            name: name,
            filePublicUrl: filePublicUrl,
            fileKey: fileKey,
          },
        });

        return {
          message: `${query.name} updated successfully.`,
        };
      } catch (err) {
        Sentry.captureException(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update amenity.",
        });
      }
    }),

  addAmenity: adminProcedure
    .input(AddUpdateAmenitySchema)
    .mutation(async ({ ctx, input }) => {
      const { name, fileKey, filePublicUrl } = input;

      try {
        const newAmenity = await ctx.db.amenities.create({
          data: {
            name: name,
            fileKey: fileKey,
            filePublicUrl: filePublicUrl,
          },
        });

        return {
          message: `Amenity: ${newAmenity.name} created successfully.`,
        };
      } catch (e) {
        Sentry.captureException(e); 
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create new amenity",
        });
      }
    }),

  removeAmenity: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      try {
        const query = await ctx.db.amenities.delete({
          where: {
            id: id,
          },
        });

        return {
          message: `${query.name} deleted successfully.`,
        };
      } catch (err) {
        Sentry.captureException(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to remove amenity",
        });
      }
    }),
});

export default amenitiesRouter;
