import { z } from "zod";
import { adminProcedure, createTR<PERSON><PERSON>outer, publicProcedure } from "../trpc";
import generatePassword from "../utils/generate-password";
import adminCreateFromSchema from "../validations/admin-create.validation";
import { TRPCError } from "@trpc/server";
import adminUpdateSchema from "../validations/admin-update.validation";

import { sendEmail } from "@repo/mail";
import { jwtVerify, SignJWT } from "jose";
import { env } from "~/env";
import { resetPasswordFormSchema } from "../validations/reset-password.validation";
import { hash, compare } from "bcryptjs";

const adminRouter = createTRPCRouter({
  getAdminById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const { id } = input;

        const admin = await ctx.db.adminUser.findUnique({
          where: {
            id: id,
          },
        });

        if (!admin) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Admin not found",
          });
        }

        return admin;
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch admin details",
        });
      }
    }),

  getAllAdmins: adminProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.adminUser.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          active: true,
          userType: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch admin list",
      });
    }
  }),

  createNewAdmin: adminProcedure
    .input(adminCreateFromSchema)
    .mutation(async ({ ctx, input }) => {
      const { name, email, active, userType } = input;

      try {
        const adminAlreadyExists = await ctx.db.adminUser.findUnique({
          where: {
            email: email,
          },
        });

        if (adminAlreadyExists) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Admin already exists !",
          });
        }

        const randomPassword: string = generatePassword();
        console.log("Generated Password: ", randomPassword);
        const passwordHash = await hash(randomPassword, 10);

        const newAdmin = await ctx.db.adminUser.create({
          data: {
            name: name,
            email: email,
            active: active,
            passwordHash: passwordHash,
            userType: userType,
          },
          select: {
            name: true,
            email: true,
          },
        });

        const emailBodySendGrid = {
          from: env.FROM_EMAIL,
          subject: "Your admin credentials!",
          to: [newAdmin.email],
          html: `<p>Hi,<strong> ${newAdmin.name}</strong><br/></p>
                     <span>This is your system generated password.</span><br/>
                     ${randomPassword}`,
        };
        await sendEmail(emailBodySendGrid, env.SENDGRID_API_KEY);
        return { admin: newAdmin, message: "Admin created successfully" };
      } catch (err) {
        console.log(err);
        if (err instanceof TRPCError) throw err;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create new admin",
        });
      }
    }),

  updatePassword: adminProcedure
    .input(
      z.object({
        // id: z.string(),
        oldPassword: z.string(),
        newPassword: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { oldPassword, newPassword } = input;

        if (!ctx.session.user.email) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "User not authenticated",
          });
        }

        const fetchAdminDetail = await ctx.db.adminUser.findUnique({
          where: {
            email: ctx.session.user.email,
          },
        });

        if (!fetchAdminDetail) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Admin not found",
          });
        }

        if (!(await compare(oldPassword, fetchAdminDetail.passwordHash))) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Invalid old password",
          });
        }

        const passwordHash = await hash(newPassword, 10);

        return await ctx.db.adminUser.update({
          where: {
            email: fetchAdminDetail.email,
          },
          data: {
            passwordHash: passwordHash,
          },
        });
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update password",
        });
      }
    }),
  updateAdminDetails: adminProcedure
    .input(adminUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, name, email, active, userType } = input;

        // Check if admin exists
        const adminExists = await ctx.db.adminUser.findUnique({
          where: { id },
        });

        if (!adminExists) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Admin not found",
          });
        }

        // Check if email is already in use by another admin
        if (email !== adminExists.email) {
          const emailInUse = await ctx.db.adminUser.findUnique({
            where: { email },
          });

          if (emailInUse) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Email already in use by another admin",
            });
          }
        }

        const updatedAdmin = await ctx.db.adminUser.update({
          where: {
            id: id,
          },
          data: {
            name: name,
            email: email,
            active: active,
            userType: userType,
          },
        });

        return { admin: updatedAdmin, message: "Updated Successfully !" };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update admin details",
        });
      }
    }),

  deleteAdmin: adminProcedure
    .input(z.object({ id: z.string().optional() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id } = input;

        if (!id) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Admin id not provided !",
          });
        }

        // Check if admin exists
        const adminExists = await ctx.db.adminUser.findUnique({
          where: { id },
        });

        if (!adminExists) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Admin not found",
          });
        }

        await ctx.db.adminUser.delete({
          where: {
            id: id,
          },
        });

        return { message: "Admin Deleted Successfully !" };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete admin",
        });
      }
    }),

  forgetPassword: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const admin = await ctx.db.adminUser.findFirst({
          where: {
            email: {
              equals: input.email,
              mode: "insensitive",
            },
          },
          select: {
            email: true,
            name: true,
          },
        });

        if (!admin) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No admin found with the provided email.",
          });
        }
        const token = await new SignJWT({ email: admin.email })
          .setProtectedHeader({ alg: "HS256" })
          .setExpirationTime("4h")
          .sign(new TextEncoder().encode(env.EMAIL_JWT_SECRET));
        // const token = jwt.sign({ email: admin.email }, env.EMAIL_JWT_SECRET, {
        //   expiresIn: "4h",
        // });

        const link = `${env.NEXTAUTH_URL}/reset-password?resetPasswordToken=${token}`;
        const emailBodySendGrid = {
          from: env.FROM_EMAIL,
          subject: "Reset password instructions!",
          to: [admin.email],
          html: `<p>Hi,<strong> ${admin.name}</strong><br/></p>
                   <span>Here is the link to reset your password.</span><br/>
                   <a href="${link}">${link}</a>
                   <p>This is valid for next 4 hours.</p>`,
        };

        await sendEmail(emailBodySendGrid, env.SENDGRID_API_KEY);

        return {
          status: 200,
          message: "Reset password instructions sent to your email",
        };
      } catch (err) {
        if (err instanceof TRPCError) throw err;

        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send reset password link.",
        });
      }
    }),

  resetPassword: publicProcedure
    .input(resetPasswordFormSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { password, token } = input;

        let decoded;
        try {
          const result = await jwtVerify(
            token,
            new TextEncoder().encode(env.EMAIL_JWT_SECRET),
          );
          decoded = { email: result.payload.email as string };
        } catch (err) {
          console.log(err);
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "Invalid or expired token. Please request a new reset link.",
          });
        }
        console.log("decoded", decoded);

        const email = decoded.email;

        // Check if admin exists
        const adminExists = await ctx.db.adminUser.findUnique({
          where: { email },
        });

        if (!adminExists) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Admin not found",
          });
        }

        const passwordHash = await hash(password, 10);

        await ctx.db.adminUser.update({
          where: {
            email: email,
          },
          data: {
            passwordHash: passwordHash,
          },
        });

        return {
          message: "Password updated successfully.",
        };
      } catch (err) {
        if (err instanceof TRPCError) throw err;

        console.log(err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reset password.",
        });
      }
    }),
});

export default adminRouter;
