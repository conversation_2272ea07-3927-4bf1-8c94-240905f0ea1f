import { TRPCError } from "@trpc/server";
import { createTR<PERSON>Router, adminProcedure } from "../trpc";
import AddUpdateFaqSchema from "../validations/add-update-faq.validations";
import { z } from "zod";
import { FaqPagesEnum, Prisma, ProjectEnum } from "@repo/database";

const faqRouter = createTRPCRouter({
  getAllFaq: adminProcedure
    .input(
      z.object({
        project: z.nativeEnum(ProjectEnum),
        page: z.nativeEnum(FaqPagesEnum),
      }),
    )
    .query(async ({ ctx, input }) => {
      try {
        const faqs = await ctx.db.faq.findMany({
          where: {
            page: input.page,
            project: input.project,
          },
          orderBy: {
            order: "asc",
          },
        });
        return faqs;
      } catch (error) {
        console.error("Error fetching FAQs:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve FAQ list. Please try again later.",
        });
      }
    }),

  getFaqById: adminProcedure
    .input(z.object({ id: z.string().min(1, "FAQ ID is required") }))
    .query(async ({ ctx, input }) => {
      try {
        const faq = await ctx.db.faq.findUnique({
          where: {
            id: input.id,
          },
        });

        if (!faq) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `FAQ with ID ${input.id} not found`,
          });
        }

        return faq;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error(`Error fetching FAQ with ID ${input.id}:`, error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve FAQ details. Please try again later.",
        });
      }
    }),

  updateFaq: adminProcedure
    .input(
      AddUpdateFaqSchema.extend({
        id: z.string().min(1, "FAQ ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { answer, id, order, question, page, project } = input;

      try {
        // First check if the FAQ exists
        const existingFaq = await ctx.db.faq.findUnique({
          where: { id },
        });

        if (!existingFaq) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Cannot update: FAQ with ID ${id} not found`,
          });
        }

        await ctx.db.faq.update({
          where: {
            id: id,
          },
          data: {
            question,
            answer,
            order,
            page,
            project,
          },
        });

        return { message: "FAQ updated successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2025") {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `FAQ with ID ${id} not found`,
            });
          }
        }

        console.error("Error updating FAQ:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update FAQ. Please try again later.",
        });
      }
    }),

  addFaq: adminProcedure
    .input(AddUpdateFaqSchema)
    .mutation(async ({ ctx, input }) => {
      const { answer, order, question, project, page } = input;
      console.log(input);
      try {
        await ctx.db.faq.create({
          data: {
            answer,
            order,
            question,
            project,
            page,
          },
        });

        return { message: "FAQ created successfully" };
      } catch (error) {
        console.error("Error creating FAQ:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create new FAQ. Please try again later.",
        });
      }
    }),

  removeFaq: adminProcedure
    .input(z.object({ id: z.string().min(1, "FAQ ID is required") }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      try {
        // First check if the FAQ exists
        const existingFaq = await ctx.db.faq.findUnique({
          where: { id },
        });

        if (!existingFaq) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Cannot delete: FAQ with ID ${id} not found`,
          });
        }

        await ctx.db.faq.delete({
          where: {
            id,
          },
        });

        return { message: "FAQ deleted successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2025") {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `FAQ with ID ${id} not found`,
            });
          }
        }

        console.error("Error deleting FAQ:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete FAQ. Please try again later.",
        });
      }
    }),
});

export default faqRouter;
