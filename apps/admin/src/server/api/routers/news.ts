import { TRPCError } from "@trpc/server";
import { createTRPCRouter, adminProcedure } from "../trpc";
import AddUpdateNewsSchema from "../validations/add-update-news.validations";
import { z } from "zod";
import { <PERSON>risma } from "@repo/database";

const newsRouter = createTRPCRouter({
  getAllNews: adminProcedure.query(async ({ ctx }) => {
    try {
      const news = await ctx.db.news.findMany({
        orderBy: {
          createdAt: "desc",
        },
      });
      return news;
    } catch (error) {
      console.error("Error fetching news:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to retrieve news list. Please try again later.",
      });
    }
  }),

  getNewsById: adminProcedure
    .input(z.object({ id: z.string().min(1, "News ID is required") }))
    .query(async ({ ctx, input }) => {
      try {
        const news = await ctx.db.news.findUnique({
          where: {
            id: input.id,
          },
        });

        if (!news) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `News with ID ${input.id} not found`,
          });
        }

        return news;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error(`Error fetching news with ID ${input.id}:`, error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve news details. Please try again later.",
        });
      }
    }),

  updateNews: adminProcedure
    .input(
      AddUpdateNewsSchema.extend({
        id: z.string().min(1, "News ID is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { title, description, fileKey, filePublicUrl, id, redirectUrl } =
        input;

      try {
        // First check if the news exists
        const existingNews = await ctx.db.news.findUnique({
          where: { id },
        });

        if (!existingNews) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Cannot update: News with ID ${id} not found`,
          });
        }

        await ctx.db.news.update({
          where: {
            id: id,
          },
          data: {
            title,
            description,
            fileKey,
            filePublicUrl,
            redirectUrl,
          },
        });
        return { message: "News updated successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2025") {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `News with ID ${id} not found`,
            });
          }
        }

        console.error("Error updating news:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update news data. Please try again later.",
        });
      }
    }),

  addNews: adminProcedure
    .input(AddUpdateNewsSchema)
    .mutation(async ({ ctx, input }) => {
      const { description, fileKey, filePublicUrl, redirectUrl, title } = input;
      try {
        await ctx.db.news.create({
          data: {
            title,
            description,
            fileKey,
            filePublicUrl,
            redirectUrl,
          },
        });

        return { message: "News created successfully" };
      } catch (error) {
        console.error("Error creating news:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create news. Please try again later.",
        });
      }
    }),

  removeNews: adminProcedure
    .input(z.object({ id: z.string().min(1, "News ID is required") }))
    .mutation(async ({ ctx, input }) => {
      const { id } = input;

      try {
        // First check if the news exists
        const existingNews = await ctx.db.news.findUnique({
          where: { id },
        });

        if (!existingNews) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Cannot delete: News with ID ${id} not found`,
          });
        }

        await ctx.db.news.delete({
          where: {
            id,
          },
        });

        return { message: "News deleted successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2025") {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: `News with ID ${id} not found`,
            });
          }
        }

        console.error("Error deleting news:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete news. Please try again later.",
        });
      }
    }),
});

export default newsRouter;
