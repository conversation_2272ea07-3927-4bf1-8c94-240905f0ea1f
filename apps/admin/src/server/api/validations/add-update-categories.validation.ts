import { PostPropertyFormFieldStatusEnum } from "@repo/database";
import z from "zod";

const addUpdateCategoriesSchema = z.object({
  name: z.string().min(1, "Name is required"),
  showBedrooms: z.nativeEnum(PostPropertyFormFieldStatusEnum, {
    message: "Required",
    required_error: "Please select a value",
    invalid_type_error: "Please select a value",
  }),
  showBathrooms: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showSecurityDeposit: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showAreaIn: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showArea: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showAboutProperty: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showPropertyAddress: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showPropertyLocation: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showUtilities: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showSocietyName: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showBuildYear: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showPossessionState: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showAmenities: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showFurnishing: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showFacing: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showTotalFloors: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showFloorNumber: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showCarParking: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
  showPropertyState: z
    .nativeEnum(PostPropertyFormFieldStatusEnum, {
      message: "Required",
      required_error: "Please select a value",
      invalid_type_error: "Please select a value",
    })
    .default(PostPropertyFormFieldStatusEnum.HIDE),
});

export default addUpdateCategoriesSchema;
