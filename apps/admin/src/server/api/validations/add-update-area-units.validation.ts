import z from "zod";

const AddUpdateAreaUnitSchema = z.object({
  category: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
      }),
    )
    .min(1, { message: "At least 1 category needs to be selected" }),
  name: z.string().min(4, { message: "Name must of at least 4 character" }),
  shortForm: z
    .string()
    .min(2, { message: "Short form must be of at least 2 character" }),
  conversionMultiplyer: z.coerce.number({ message: "enter a number" }),
});

export default AddUpdateAreaUnitSchema;
