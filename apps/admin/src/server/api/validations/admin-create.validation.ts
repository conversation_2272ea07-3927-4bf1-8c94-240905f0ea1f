import { z } from "zod";
import { AdminUserTypeEnum } from "@repo/database";

const AddUpdateAdminSchema = z.object({
  name: z.string().min(2, { message: "Name must of 2 chars" }),
  email: z.string().email(),
  active: z.boolean().optional(),
  userType: z.nativeEnum(AdminUserTypeEnum, {
    required_error: "Please select a value",
    invalid_type_error: "Please select a value",
  }),
});

export default AddUpdateAdminSchema;
