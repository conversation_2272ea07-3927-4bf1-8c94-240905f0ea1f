import { FaqPagesEnum, ProjectEnum } from "@repo/database";
import z from "zod";

const AddUpdateFaqSchema = z.object({
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required"),
  order: z.coerce.number().min(1, "Order must be at least 1"),
  project: z.nativeEnum(ProjectEnum).optional(),
  page: z.nativeEnum(FaqPagesEnum).optional(),
});

export default AddUpdateFaqSchema;
