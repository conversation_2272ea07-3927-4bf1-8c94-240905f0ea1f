import {
  FacingEnum,
  FurnishingEnum,
  PossessionStateEnum,
  PropertyForEnum,
  PropertyMediaTypeEnum,
  PropertyStateEnum,
} from "@repo/database";
import { z } from "zod";

export const PostPropertyStep1BaseSchema = z.object({
  registeryFileKey: z.string().optional().nullable(),
  // .nonempty({ message: "Registry file is required" }),
  propertyTitle: z
    .string()
    .nonempty({ message: "Property title is required" })
    .min(3, { message: "Property title must be at least 3 characters long" }),
  propertyFor: z.nativeEnum(PropertyForEnum, {
    errorMap: () => ({
      message: "Please select property listing type (Rent/Sale)",
    }),
  }),
  bedrooms: z.coerce
    .number()
    .min(1, { message: "Number of bedrooms must be at least 1" })
    .optional(),
  bathrooms: z.coerce
    .number()
    .min(1, { message: "Number of bathrooms must be at least 1" })
    .optional(),
  propertyPrice: z
    .number()
    .int({ message: "Property price must be a whole number" })
    .positive({ message: "Property price must be greater than 0" }),
  securityDeposit: z.coerce
    .number()
    .optional()
    .refine((val) => val === undefined || val >= 1, {
      message: "Security deposit must be greater than 0",
    }),
  areaUnitId: z
    .string()
    .nonempty({ message: "Area unit selection is required" }),
  area: z
    .number()
    .int({ message: "Area must be a whole number" })
    .positive({ message: "Area must be greater than 0" }),
  propertyCategoryId: z
    .string()
    .nonempty({ message: "Property category selection is required" }),
  propertyTypeId: z
    .string()
    .nonempty({ message: "Property type selection is required" }),
  aboutProperty: z
    .string()
    .min(20, {
      message: "Property description must be at least 20 characters long",
    })
    .max(1000, {
      message: "Property description cannot exceed 1000 characters",
    })
    .optional(),
});
export type PostPropertyStep1Base = z.infer<typeof PostPropertyStep1BaseSchema>;

export const PostPropertyStep2BaseSchema = z.object({
  propertyAddress: z
    .string()
    .min(2, { message: "Property address is required" })
    .optional(),
  propertyLatitude: z.number(),
  propertyLongitude: z.number(),
  propertyGooglePlaceId: z.string(),
  propertyAddressComponents: z.any(),
  propertyMarkersLatLng: z
    .record(z.any(), z.any(), {
      message: "Please add the property markers on the map.",
    })
    .array(),
  propertyLocation: z
    .string()
    .min(2, {
      message: "Flat, House no., Building, Company, Apartment is required",
    })
    .optional(),
  utilities: z
    .array(
      z.object({
        utility: z.string().min(1, { message: "Utility name is required" }),
        distanceInKm: z.coerce
          .number({ invalid_type_error: "Must be a number" })
          .positive({ message: "Distance must be greater than 0" }),
      }),
    )
    .optional(),
});
export type PostPropertyStep2Base = z.infer<typeof PostPropertyStep2BaseSchema>;

export const PostPropertyStep3BaseSchema = z.object({
  societyOrLocalityName: z
    .string()
    .min(2, { message: "Society name must be of 2 characters" })
    .optional(),
  buildYear: z.number().min(1900).max(new Date().getFullYear()).optional(),
  possessionState: z.nativeEnum(PossessionStateEnum).optional(),
  amenities: z.array(z.object({ id: z.string(), name: z.string() })).optional(),
  furnishing: z.nativeEnum(FurnishingEnum).optional(),
  totalFloors: z
    .number()
    .min(1, { message: "Total floors must be greater than 1" })
    .optional(),
  floorNumber: z
    .number()
    .min(1, { message: "Floor number must be greater than 1" })
    .optional(),
  carParking: z
    .number()
    .min(1, {
      message: "Car parking must be greater than 1",
    })
    .optional(),
  facing: z.nativeEnum(FacingEnum).optional(),
  propertyState: z.nativeEnum(PropertyStateEnum).optional(),
});
export type PostPropertyStep3Base = z.infer<typeof PostPropertyStep3BaseSchema>;

export const PostPropertyStep4Schema = z.object({
  mediaSections: z.array(
    z.object({
      title: z.string().min(1, "Section title is required"),
      media: z
        .array(
          z.object({
            fileKey: z.string().optional(),
            filePublicUrl: z.string().optional(),
            cloudinaryUrl: z.string().optional(),
            cloudinaryId: z.string().optional(),
            mediaType: z.nativeEnum(PropertyMediaTypeEnum).optional(),
          }),
        )
        .min(3, "Minimum 3 photos required per section"),
    }),
  ),
});
export type PostPropertyStep4Base = z.infer<typeof PostPropertyStep4Schema>;
