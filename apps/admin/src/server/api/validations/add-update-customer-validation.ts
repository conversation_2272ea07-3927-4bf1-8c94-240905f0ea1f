import z from "zod";

const addUpdateTestimonialSchema = z.object({
  name: z.string().min(2, { message: "Name must of atleast 2 chars" }),
  description: z
    .string()
    .min(5, { message: "Description should be min of 10 chars" })
    .max(80, { message: "Description should contain max of 80 chars" }),
  rating: z.coerce.number(),
  cityId: z.string({ message: "Select the city." }),
  fileKey: z.string().min(2, { message: "File is required." }).optional(),
  filePublicUrl: z
    .string()
    .url({ message: "File public url is required." })
    .optional(),
});

export default addUpdateTestimonialSchema;
