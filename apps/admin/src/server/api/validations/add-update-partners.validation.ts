import z from "zod";

const phoneRegex = new RegExp(
  /^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/,
);

const pancardRegex = new RegExp(/([A-Z]){5}([0-9]){4}([A-Z]){1}$/);

const gstRegex = new RegExp(
  /\d{2}[A-Z]{5}\d{4}[A-Z]{1}[A-Z\d]{1}[Z]{1}[A-Z\d]{1}/,
);

const adharcardNumber = new RegExp(/^[1-9]\d{11}$/);

export const AddUpdatePartnersSchema = z.object({
  name: z.string().min(2, { message: "Name must be of 2 characters" }),
  phoneNumber: z
    .string()
    .regex(phoneRegex, { message: "Invalid phone number" }),
  email: z.string().email(),
  pancardNumber: z
    .union([
      z.string()
        .regex(pancardRegex, { message: "Please enter a valid PAN card number in the correct format (e.g., **********)." })
        .min(10, { message: "PAN card number must have exactly 10 characters." }),
      z.literal(""),
    ])
    .optional()
    .nullable(),
  adharcardNumber: z
    .string()
    .regex(adharcardNumber, {
      message:
        "Please enter a valid Aadhar card number with exactly 12 digits.",
    })
    .min(12, { message: "Aadhar card number must have exactly 12 digits." })
    .optional(),
  cityId: z.string(),
  reraNumber: z.string().optional(),
  referredBy: z.string().optional(),
  gstNumber: z
    .string()
    // .regex(gstRegex, { message: "Invalid gst number" })
    .optional(),
  //   referredBy:z.string().optional(),
  bio: z.string().optional(),
  experience: z.string().optional(),
  userLocation: z.string().optional(),
  longitude: z.string().optional(),
  latitude: z.string().optional(),
  // operationArea:z.array(z.object({
  //   name: z.string().min(2),
  //     operationAreaLatitude: z.string(),
  //     operationAreaLongitude: z.string(),
  //     operationAreaGooglePlaceId: z.string(),
  //     operationAreaAddressComponent: z.any(),
  // })).optional()
  operationArea: z.string().optional(),
});

export default AddUpdatePartnersSchema;
