import { AdminUserTypeEnum } from "@repo/database";
import { z } from "zod";

const adminUpdateSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(2, { message: "Name must be 2 chars" }),
  email: z.string().email(),
  active: z.boolean().optional(),
  userType: z.nativeEnum(AdminUserTypeEnum, {
    required_error: "Please select a value",
    invalid_type_error: "Please select a value",
  }),
});

export default adminUpdateSchema;
