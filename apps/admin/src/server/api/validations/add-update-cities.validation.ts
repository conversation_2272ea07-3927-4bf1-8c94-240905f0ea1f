import { z } from "zod";

const AddUpdateCitiesSchema = z.object({
  cityName: z.string().min(2, {
    message: "City name must be at least 2 characters.",
  }),
  cityMarkersLatLng: z
    .record(z.any(), z.any(), {
      message: "Please add the property markers on the map.",
    })
    .array(),
  northMaxLat: z.number(),
  southMaxLat: z.number(),
  eastMaxLng: z.number(),
  westMaxLng: z.number(),
});

export { AddUpdateCitiesSchema };
