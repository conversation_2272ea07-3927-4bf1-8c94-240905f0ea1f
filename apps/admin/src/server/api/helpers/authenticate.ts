"use server";
import { AdminUserTypeEnum } from "@repo/database";
import { compare } from "bcryptjs";
import { env } from "~/env";
import { db } from "~/server/db";

const authenticate = async ({
  email,
  password,
}: {
  email: string | undefined;
  password: string | undefined;
}) => {
  if (!email || !password) {
    return null;
  }

  const adminUser = await db.adminUser.findUnique({
    where: {
      email: email,
    },
  });

  if (!adminUser) return null;

  if (env.NODE_ENV === "development") return adminUser;

  const passwordMatchResult = await compare(password, adminUser.passwordHash);
  //   const userRole = adminUser.userType === AdminUserTypeEnum.ADMIN;
  const isActive = adminUser.active;

  if (passwordMatchResult && isActive) return adminUser;

  return null;

  //   return adminUser;
};

export default authenticate;
