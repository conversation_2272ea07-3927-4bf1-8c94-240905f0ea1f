{"name": "admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev --turbopack -p 3000", "lint": "next lint", "start": "next start -p 7004"}, "dependencies": {"@auth/prisma-adapter": "^1.6.0", "@hookform/resolvers": "^3.9.0", "@prisma/nextjs-monorepo-workaround-plugin": "6.8.2", "@repo/aws": "*", "@repo/database": "*", "@repo/mail": "*", "@repo/partner-api": "*", "@repo/tailwind-config": "*", "@repo/ui": "*", "@sentry/nextjs": "^9", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-form": "^1.2.1", "@tanstack/react-query": "^5", "@tanstack/react-query-devtools": "^5", "@tanstack/react-table": "^8.20.5", "@tanstack/zod-form-adapter": "^0.42.1", "@trpc/client": "^11.2.0", "@trpc/next": "^11.2.0", "@trpc/react-query": "^11.2.0", "@trpc/server": "^11.2.0", "@trpc/tanstack-react-query": "^11.2.0", "axios": "^1.7.9", "bcryptjs": "^3", "cloudinary": "^2.6.0", "date-fns": "^4.1.0", "jose": "6.0.11", "json-to-csv-export": "^3.0.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.446.0", "next": "15.2.1-canary.6", "next-auth": "5.0.0-beta.25", "next-cloudinary": "^6.16.0", "next-themes": "^0.3.0", "nextjs-toploader": "^3.8.16", "nuqs": "^2.4.1", "react": "19.0.0", "react-csv-reader": "^4.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.53.0", "server-only": "^0.0.1", "superjson": "^2.2.1", "zod": "^3.24.1"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/tsconfig": "*", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.14.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^9.12.0", "eslint-config-next": "15.0.1", "postcss": "^8.4.47", "autoprefixer": "^10.4.19", "postcss-load-config": "^6.0.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.15", "typescript": "^5.8.2"}, "ct3aMetadata": {"initVersion": "7.37.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}